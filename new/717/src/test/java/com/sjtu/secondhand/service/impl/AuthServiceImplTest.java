package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.LoginRequest;
import com.sjtu.secondhand.dto.request.RegisterRequest;
import com.sjtu.secondhand.dto.response.JwtAuthResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.security.JwtTokenProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AuthServiceImplTest {

    @Mock
    private UserRepository userRepository;
    @Mock
    private PasswordEncoder passwordEncoder;
    @Mock
    private AuthenticationManager authenticationManager;
    @Mock
    private JwtTokenProvider jwtTokenProvider;
    @Mock
    private Authentication authentication; // Mock Authentication object

    @InjectMocks
    private AuthServiceImpl authService;

    private User user;
    private RegisterRequest registerRequest;

    @BeforeEach
    void setUp() {
        user = new User("testuser", "encodedPassword", "<EMAIL>");
        user.setId(1L);

        registerRequest = new RegisterRequest("newuser", "password123", "<EMAIL>");
    }

    @Test
    void login_shouldReturnJwtAuthResponse_whenCredentialsAreValid() {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("testuser", "password");
        when(authenticationManager.authenticate(any())).thenReturn(authentication);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));
        when(jwtTokenProvider.createToken("testuser", 1L)).thenReturn("test-jwt-token");

        // Act
        JwtAuthResponse response = authService.login(loginRequest);

        // Assert
        assertNotNull(response);
        assertEquals("test-jwt-token", response.getToken());
        assertEquals(user.getId(), response.getId());
        assertEquals(user.getUsername(), response.getUsername());
    }

    @Test
    void login_shouldThrowBadCredentialsException_whenCredentialsAreInvalid() {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("user", "wrongpassword");
        when(authenticationManager.authenticate(any())).thenThrow(new BadCredentialsException("认证失败"));

        // Act & Assert
        assertThrows(BadCredentialsException.class, () -> authService.login(loginRequest));
    }

    @Test
    void register_shouldReturnSuccessMessage_whenRegistrationIsSuccessful() {
        // Arrange
        when(userRepository.existsByUsername(registerRequest.getUsername())).thenReturn(false);
        when(userRepository.existsByEmail(registerRequest.getEmail())).thenReturn(false);
        when(passwordEncoder.encode(registerRequest.getPassword())).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(new User());

        // Act
        String result = authService.register(registerRequest);

        // Assert
        assertEquals("用户注册成功", result);
        verify(userRepository).save(any(User.class)); // 验证save方法被调用
    }

    @Test
    void register_shouldThrowApiException_whenUsernameExists() {
        // Arrange
        when(userRepository.existsByUsername(registerRequest.getUsername())).thenReturn(true);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> authService.register(registerRequest));
        assertEquals("用户名已被占用", exception.getMessage());
    }

    @Test
    void register_shouldThrowApiException_whenEmailExists() {
        // Arrange
        when(userRepository.existsByUsername(registerRequest.getUsername())).thenReturn(false);
        when(userRepository.existsByEmail(registerRequest.getEmail())).thenReturn(true);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> authService.register(registerRequest));
        assertEquals("邮箱已被注册", exception.getMessage());
    }
    
    @Test
    void register_shouldThrowApiException_whenEmailIsNotSjtuEmail() {
        // Arrange
        RegisterRequest invalidEmailRequest = new RegisterRequest("anotheruser", "password", "<EMAIL>");
        when(userRepository.existsByUsername(invalidEmailRequest.getUsername())).thenReturn(false);
        when(userRepository.existsByEmail(invalidEmailRequest.getEmail())).thenReturn(false);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> authService.register(invalidEmailRequest));
        assertEquals("请使用上海交通大学邮箱注册", exception.getMessage());
    }
} 