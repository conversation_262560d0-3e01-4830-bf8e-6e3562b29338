package com.sjtu.secondhand.model.es;

import com.sjtu.secondhand.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ItemDocumentTest {

    private ItemDocument itemDocument;

    @BeforeEach
    void setUp() {
        itemDocument = new ItemDocument();
    }

    @Test
    void testDefaultConstructor() {
        // When
        ItemDocument document = new ItemDocument();

        // Then
        assertNull(document.getId());
        assertNull(document.getName());
        assertNull(document.getDescription());
        assertNull(document.getPrice());
        assertNull(document.getPriceMin());
        assertNull(document.getPriceMax());
        assertNull(document.getItemType());
        assertNull(document.getCondition());
        assertNull(document.getStatus());
        assertNull(document.getIsVisible());
        assertNull(document.getViewCount());
        assertNull(document.getFavoriteCount());
        assertNull(document.getCategory());
        assertNull(document.getUser());
        assertNull(document.getCreatedAt());
        assertNull(document.getUpdatedAt());
        assertNull(document.getImageUrls());
    }

    @Test
    void testSetAndGetId() {
        // Given
        Long id = 123L;

        // When
        itemDocument.setId(id);

        // Then
        assertEquals(id, itemDocument.getId());
    }

    @Test
    void testSetAndGetName() {
        // Given
        String name = "Test Item Name";

        // When
        itemDocument.setName(name);

        // Then
        assertEquals(name, itemDocument.getName());
    }

    @Test
    void testSetAndGetDescription() {
        // Given
        String description = "Test item description";

        // When
        itemDocument.setDescription(description);

        // Then
        assertEquals(description, itemDocument.getDescription());
    }

    @Test
    void testSetAndGetPrice() {
        // Given
        BigDecimal price = new BigDecimal("99.99");

        // When
        itemDocument.setPrice(price);

        // Then
        assertEquals(price, itemDocument.getPrice());
    }

    @Test
    void testSetAndGetPriceMin() {
        // Given
        BigDecimal priceMin = new BigDecimal("10.00");

        // When
        itemDocument.setPriceMin(priceMin);

        // Then
        assertEquals(priceMin, itemDocument.getPriceMin());
    }

    @Test
    void testSetAndGetPriceMax() {
        // Given
        BigDecimal priceMax = new BigDecimal("200.00");

        // When
        itemDocument.setPriceMax(priceMax);

        // Then
        assertEquals(priceMax, itemDocument.getPriceMax());
    }

    @Test
    void testSetAndGetItemType() {
        // Given
        String itemType = "IDLE";

        // When
        itemDocument.setItemType(itemType);

        // Then
        assertEquals(itemType, itemDocument.getItemType());
    }

    @Test
    void testSetAndGetCondition() {
        // Given
        String condition = "BRAND_NEW";

        // When
        itemDocument.setCondition(condition);

        // Then
        assertEquals(condition, itemDocument.getCondition());
    }

    @Test
    void testSetAndGetStatus() {
        // Given
        String status = "FOR_SALE";

        // When
        itemDocument.setStatus(status);

        // Then
        assertEquals(status, itemDocument.getStatus());
    }

    @Test
    void testSetAndGetIsVisible() {
        // When
        itemDocument.setIsVisible(true);

        // Then
        assertTrue(itemDocument.getIsVisible());

        // When
        itemDocument.setIsVisible(false);

        // Then
        assertFalse(itemDocument.getIsVisible());
    }

    @Test
    void testSetAndGetViewCount() {
        // Given
        Integer viewCount = 150;

        // When
        itemDocument.setViewCount(viewCount);

        // Then
        assertEquals(viewCount, itemDocument.getViewCount());
    }

    @Test
    void testSetAndGetFavoriteCount() {
        // Given
        Integer favoriteCount = 25;

        // When
        itemDocument.setFavoriteCount(favoriteCount);

        // Then
        assertEquals(favoriteCount, itemDocument.getFavoriteCount());
    }

    @Test
    void testSetAndGetCreatedAt() {
        // Given
        LocalDateTime createdAt = LocalDateTime.now();

        // When
        itemDocument.setCreatedAt(createdAt);

        // Then
        assertEquals(createdAt, itemDocument.getCreatedAt());
    }

    @Test
    void testSetAndGetUpdatedAt() {
        // Given
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        itemDocument.setUpdatedAt(updatedAt);

        // Then
        assertEquals(updatedAt, itemDocument.getUpdatedAt());
    }

    @Test
    void testSetAndGetImageUrls() {
        // Given
        List<String> imageUrls = List.of(
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        );

        // When
        itemDocument.setImageUrls(imageUrls);

        // Then
        assertEquals(imageUrls, itemDocument.getImageUrls());
        assertEquals(2, itemDocument.getImageUrls().size());
    }

    // CategoryInfo tests
    @Test
    void testCategoryInfoDefaultConstructor() {
        // When
        ItemDocument.CategoryInfo categoryInfo = new ItemDocument.CategoryInfo();

        // Then
        assertNull(categoryInfo.getId());
        assertNull(categoryInfo.getName());
    }

    @Test
    void testCategoryInfoSetAndGetId() {
        // Given
        ItemDocument.CategoryInfo categoryInfo = new ItemDocument.CategoryInfo();
        Long id = 456L;

        // When
        categoryInfo.setId(id);

        // Then
        assertEquals(id, categoryInfo.getId());
    }

    @Test
    void testCategoryInfoSetAndGetName() {
        // Given
        ItemDocument.CategoryInfo categoryInfo = new ItemDocument.CategoryInfo();
        String name = "Electronics";

        // When
        categoryInfo.setName(name);

        // Then
        assertEquals(name, categoryInfo.getName());
    }

    @Test
    void testSetAndGetCategory() {
        // Given
        ItemDocument.CategoryInfo categoryInfo = new ItemDocument.CategoryInfo();
        categoryInfo.setId(123L);
        categoryInfo.setName("Books");

        // When
        itemDocument.setCategory(categoryInfo);

        // Then
        assertEquals(categoryInfo, itemDocument.getCategory());
        assertEquals(123L, itemDocument.getCategory().getId());
        assertEquals("Books", itemDocument.getCategory().getName());
    }

    // UserInfo tests
    @Test
    void testUserInfoDefaultConstructor() {
        // When
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();

        // Then
        assertNull(userInfo.getId());
        assertNull(userInfo.getUsername());
        assertNull(userInfo.getAvatarUrl());
        assertNull(userInfo.getRating());
    }

    @Test
    void testUserInfoSetAndGetId() {
        // Given
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        Long id = 789L;

        // When
        userInfo.setId(id);

        // Then
        assertEquals(id, userInfo.getId());
    }

    @Test
    void testUserInfoSetAndGetUsername() {
        // Given
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        String username = "testuser";

        // When
        userInfo.setUsername(username);

        // Then
        assertEquals(username, userInfo.getUsername());
    }

    @Test
    void testUserInfoSetAndGetAvatarUrl() {
        // Given
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        String avatarUrl = "https://example.com/avatar.jpg";

        // When
        userInfo.setAvatarUrl(avatarUrl);

        // Then
        assertEquals(avatarUrl, userInfo.getAvatarUrl());
    }

    @Test
    void testUserInfoSetAndGetRating() {
        // Given
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        Double rating = 4.5;

        // When
        userInfo.setRating(rating);

        // Then
        assertEquals(rating, userInfo.getRating());
    }

    @Test
    void testSetAndGetUser() {
        // Given
        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        userInfo.setId(999L);
        userInfo.setUsername("seller");
        userInfo.setAvatarUrl("https://example.com/seller.jpg");
        userInfo.setRating(4.8);

        // When
        itemDocument.setUser(userInfo);

        // Then
        assertEquals(userInfo, itemDocument.getUser());
        assertEquals(999L, itemDocument.getUser().getId());
        assertEquals("seller", itemDocument.getUser().getUsername());
        assertEquals("https://example.com/seller.jpg", itemDocument.getUser().getAvatarUrl());
        assertEquals(4.8, itemDocument.getUser().getRating());
    }

    // fromItem static method tests
    @Test
    void testFromItemWithCompleteItem() {
        // Given
        Item item = createCompleteItem();

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertEquals(item.getId(), document.getId());
        assertEquals(item.getName(), document.getName());
        assertEquals(item.getDescription(), document.getDescription());
        assertEquals(item.getPrice(), document.getPrice());
        assertEquals(item.getPriceMin(), document.getPriceMin());
        assertEquals(item.getPriceMax(), document.getPriceMax());
        assertEquals("IDLE", document.getItemType());
        assertEquals("BRAND_NEW", document.getCondition());
        assertEquals("FOR_SALE", document.getStatus());
        assertEquals(item.getIsVisible(), document.getIsVisible());
        assertEquals(item.getViewCount(), document.getViewCount());
        assertEquals(item.getFavoriteCount(), document.getFavoriteCount());
        assertEquals(item.getCreatedAt(), document.getCreatedAt());
        assertEquals(item.getUpdatedAt(), document.getUpdatedAt());

        // Check category
        assertNotNull(document.getCategory());
        assertEquals(Long.valueOf(item.getCategory().getId()), document.getCategory().getId());
        assertEquals(item.getCategory().getName(), document.getCategory().getName());

        // Check user
        assertNotNull(document.getUser());
        assertEquals(item.getUser().getId(), document.getUser().getId());
        assertEquals(item.getUser().getUsername(), document.getUser().getUsername());
        assertEquals(item.getUser().getAvatarUrl(), document.getUser().getAvatarUrl());
        assertEquals(Double.valueOf(item.getUser().getRating()), document.getUser().getRating());

        // Check images
        assertNotNull(document.getImageUrls());
        assertEquals(2, document.getImageUrls().size());
        assertTrue(document.getImageUrls().contains("https://example.com/image1.jpg"));
        assertTrue(document.getImageUrls().contains("https://example.com/image2.jpg"));
    }

    @Test
    void testFromItemWithNullCategory() {
        // Given
        Item item = createCompleteItem();
        item.setCategory(null);

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertNull(document.getCategory());
    }

    @Test
    void testFromItemWithNullUser() {
        // Given
        Item item = createCompleteItem();
        item.setUser(null);

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertNull(document.getUser());
    }

    @Test
    void testFromItemWithNullImages() {
        // Given
        Item item = createCompleteItem();
        item.setImages(null);

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertNull(document.getImageUrls());
    }

    @Test
    void testFromItemWithEmptyImages() {
        // Given
        Item item = createCompleteItem();
        item.setImages(new ArrayList<>());

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertNull(document.getImageUrls());
    }

    @Test
    void testFromItemWithNullEnums() {
        // Given
        Item item = createCompleteItem();
        item.setItemType(null);
        item.setCondition(null);
        item.setStatus(null);

        // When
        ItemDocument document = ItemDocument.fromItem(item);

        // Then
        assertNull(document.getItemType());
        assertNull(document.getCondition());
        assertNull(document.getStatus());
    }

    @Test
    void testCompleteDocumentState() {
        // Given
        ItemDocument.CategoryInfo categoryInfo = new ItemDocument.CategoryInfo();
        categoryInfo.setId(100L);
        categoryInfo.setName("Test Category");

        ItemDocument.UserInfo userInfo = new ItemDocument.UserInfo();
        userInfo.setId(200L);
        userInfo.setUsername("testuser");
        userInfo.setAvatarUrl("https://example.com/avatar.jpg");
        userInfo.setRating(4.5);

        List<String> imageUrls = List.of("https://example.com/img1.jpg", "https://example.com/img2.jpg");

        // When
        itemDocument.setId(1L);
        itemDocument.setName("Complete Item");
        itemDocument.setDescription("Complete description");
        itemDocument.setPrice(new BigDecimal("99.99"));
        itemDocument.setPriceMin(new BigDecimal("50.00"));
        itemDocument.setPriceMax(new BigDecimal("150.00"));
        itemDocument.setItemType("IDLE");
        itemDocument.setCondition("BRAND_NEW");
        itemDocument.setStatus("FOR_SALE");
        itemDocument.setIsVisible(true);
        itemDocument.setViewCount(100);
        itemDocument.setFavoriteCount(10);
        itemDocument.setCategory(categoryInfo);
        itemDocument.setUser(userInfo);
        itemDocument.setCreatedAt(LocalDateTime.now());
        itemDocument.setUpdatedAt(LocalDateTime.now());
        itemDocument.setImageUrls(imageUrls);

        // Then
        assertEquals(1L, itemDocument.getId());
        assertEquals("Complete Item", itemDocument.getName());
        assertEquals("Complete description", itemDocument.getDescription());
        assertEquals(new BigDecimal("99.99"), itemDocument.getPrice());
        assertEquals(new BigDecimal("50.00"), itemDocument.getPriceMin());
        assertEquals(new BigDecimal("150.00"), itemDocument.getPriceMax());
        assertEquals("IDLE", itemDocument.getItemType());
        assertEquals("BRAND_NEW", itemDocument.getCondition());
        assertEquals("FOR_SALE", itemDocument.getStatus());
        assertTrue(itemDocument.getIsVisible());
        assertEquals(100, itemDocument.getViewCount());
        assertEquals(10, itemDocument.getFavoriteCount());
        assertEquals(categoryInfo, itemDocument.getCategory());
        assertEquals(userInfo, itemDocument.getUser());
        assertNotNull(itemDocument.getCreatedAt());
        assertNotNull(itemDocument.getUpdatedAt());
        assertEquals(imageUrls, itemDocument.getImageUrls());
    }

    @Test
    void testSetNullValues() {
        // When
        itemDocument.setId(null);
        itemDocument.setName(null);
        itemDocument.setDescription(null);
        itemDocument.setPrice(null);
        itemDocument.setPriceMin(null);
        itemDocument.setPriceMax(null);
        itemDocument.setItemType(null);
        itemDocument.setCondition(null);
        itemDocument.setStatus(null);
        itemDocument.setIsVisible(null);
        itemDocument.setViewCount(null);
        itemDocument.setFavoriteCount(null);
        itemDocument.setCategory(null);
        itemDocument.setUser(null);
        itemDocument.setCreatedAt(null);
        itemDocument.setUpdatedAt(null);
        itemDocument.setImageUrls(null);

        // Then
        assertNull(itemDocument.getId());
        assertNull(itemDocument.getName());
        assertNull(itemDocument.getDescription());
        assertNull(itemDocument.getPrice());
        assertNull(itemDocument.getPriceMin());
        assertNull(itemDocument.getPriceMax());
        assertNull(itemDocument.getItemType());
        assertNull(itemDocument.getCondition());
        assertNull(itemDocument.getStatus());
        assertNull(itemDocument.getIsVisible());
        assertNull(itemDocument.getViewCount());
        assertNull(itemDocument.getFavoriteCount());
        assertNull(itemDocument.getCategory());
        assertNull(itemDocument.getUser());
        assertNull(itemDocument.getCreatedAt());
        assertNull(itemDocument.getUpdatedAt());
        assertNull(itemDocument.getImageUrls());
    }

    private Item createCompleteItem() {
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        item.setDescription("Test Description");
        item.setPrice(new BigDecimal("99.99"));
        item.setPriceMin(new BigDecimal("50.00"));
        item.setPriceMax(new BigDecimal("150.00"));
        item.setItemType(Item.ItemType.IDLE);
        item.setCondition(Item.ItemCondition.BRAND_NEW);
        item.setStatus(Item.ItemStatus.FOR_SALE);
        item.setIsVisible(true);
        item.setViewCount(100);
        item.setFavoriteCount(10);
        item.setCreatedAt(LocalDateTime.now());
        item.setUpdatedAt(LocalDateTime.now());

        // Create category
        Category category = new Category();
        category.setId(1);
        category.setName("Test Category");
        item.setCategory(category);

        // Create user
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        user.setAvatarUrl("https://example.com/avatar.jpg");
        user.setCreditScore(85);
        item.setUser(user);

        // Create images
        List<ItemImage> images = new ArrayList<>();
        ItemImage image1 = new ItemImage();
        image1.setUrl("https://example.com/image1.jpg");
        ItemImage image2 = new ItemImage();
        image2.setUrl("https://example.com/image2.jpg");
        images.add(image1);
        images.add(image2);
        item.setImages(images);

        return item;
    }
} 