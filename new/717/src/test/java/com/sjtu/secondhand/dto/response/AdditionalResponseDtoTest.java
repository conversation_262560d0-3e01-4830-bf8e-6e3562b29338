package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.sjtu.secondhand.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 其他 Response DTO 类的测试
 * 用于提高ApiResponse、ItemPageResponse、NotificationDto等类的覆盖率
 */
public class AdditionalResponseDtoTest {

    private ObjectMapper objectMapper;
    private User testUser;
    private Category testCategory;
    private Item testItem;
    private ItemResponse testItemResponse;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 创建测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setAvatarUrl("http://example.com/avatar.jpg");
        testUser.setCreditScore(85);
        
        // 创建测试分类
        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("电子产品");
        
        // 创建测试商品
        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("测试商品");
        testItem.setDescription("这是一个测试商品");
        testItem.setPrice(BigDecimal.valueOf(100.00));
        testItem.setCategory(testCategory);
        testItem.setCondition(Item.ItemCondition.FINE);
        testItem.setItemType(Item.ItemType.IDLE);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setUser(testUser);
        testItem.setViewCount(10);
        testItem.setFavoriteCount(5);
        testItem.setCreatedAt(LocalDateTime.now());
        testItem.setUpdatedAt(LocalDateTime.now());
        
        // 创建测试图片
        List<ItemImage> images = new ArrayList<>();
        ItemImage image = new ItemImage();
        image.setUrl("http://example.com/image.jpg");
        image.setItem(testItem);
        images.add(image);
        testItem.setImages(images);
        
        // 创建测试ItemResponse
        testItemResponse = new ItemResponse(testItem);
    }

    /**
     * 测试 ApiResponse 类 (当前覆盖率: 54.90%)
     */
    @Test
    void testApiResponse() throws Exception {
        // 测试默认构造函数
        ApiResponse<String> response1 = new ApiResponse<>();
        assertNotNull(response1);
        assertNotNull(response1.getTimestamp());
        
        // 测试带参数的构造函数
        ApiResponse<String> response2 = new ApiResponse<>(true, "SUCCESS", "操作成功", "测试数据");
        assertTrue(response2.isSuccess());
        assertEquals("SUCCESS", response2.getCode());
        assertEquals("操作成功", response2.getMessage());
        assertEquals("测试数据", response2.getData());
        assertNotNull(response2.getTimestamp());
        
        // 测试静态成功方法
        ApiResponse<String> successResponse1 = ApiResponse.success("成功数据");
        assertTrue(successResponse1.isSuccess());
        assertEquals("SUCCESS", successResponse1.getCode());
        assertEquals("操作成功", successResponse1.getMessage());
        assertEquals("成功数据", successResponse1.getData());
        
        ApiResponse<String> successResponse2 = ApiResponse.success("自定义成功消息", "成功数据");
        assertTrue(successResponse2.isSuccess());
        assertEquals("SUCCESS", successResponse2.getCode());
        assertEquals("自定义成功消息", successResponse2.getMessage());
        assertEquals("成功数据", successResponse2.getData());
        
        // 测试静态错误方法
        ApiResponse<String> errorResponse1 = ApiResponse.error("错误消息");
        assertFalse(errorResponse1.isSuccess());
        assertEquals("ERROR", errorResponse1.getCode());
        assertEquals("错误消息", errorResponse1.getMessage());
        assertNull(errorResponse1.getData());
        
        ApiResponse<String> errorResponse2 = ApiResponse.error("CUSTOM_ERROR", "自定义错误消息");
        assertFalse(errorResponse2.isSuccess());
        assertEquals("CUSTOM_ERROR", errorResponse2.getCode());
        assertEquals("自定义错误消息", errorResponse2.getMessage());
        assertNull(errorResponse2.getData());
        
        ApiResponse<String> errorResponse3 = ApiResponse.error("CUSTOM_ERROR", "自定义错误消息", "错误数据");
        assertFalse(errorResponse3.isSuccess());
        assertEquals("CUSTOM_ERROR", errorResponse3.getCode());
        assertEquals("自定义错误消息", errorResponse3.getMessage());
        assertEquals("错误数据", errorResponse3.getData());
        
        // 测试静态失败方法
        ApiResponse<String> failureResponse = ApiResponse.failure("失败消息");
        assertFalse(failureResponse.isSuccess());
        assertEquals("FAILURE", failureResponse.getCode());
        assertEquals("失败消息", failureResponse.getMessage());
        assertNull(failureResponse.getData());
        
        // 测试forTest方法
        ApiResponse<Object> testResponse = ApiResponse.forTest("测试数据");
        assertTrue(testResponse.isSuccess());
        assertEquals("SUCCESS", testResponse.getCode());
        assertEquals("操作成功", testResponse.getMessage());
        assertEquals("测试数据", testResponse.getData());
        
        // 测试所有setter方法
        response1.setSuccess(true);
        response1.setCode("TEST_CODE");
        response1.setMessage("测试消息");
        response1.setData("测试数据");
        LocalDateTime now = LocalDateTime.now();
        response1.setTimestamp(now);
        
        assertTrue(response1.isSuccess());
        assertEquals("TEST_CODE", response1.getCode());
        assertEquals("测试消息", response1.getMessage());
        assertEquals("测试数据", response1.getData());
        assertEquals(now, response1.getTimestamp());
        
        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(response2);
        assertNotNull(json);
        assertTrue(json.contains("测试数据"));
        assertTrue(json.contains("操作成功"));
        
        // 测试JSON反序列化
        ApiResponse<?> deserializedResponse = objectMapper.readValue(json, ApiResponse.class);
        assertEquals(response2.isSuccess(), deserializedResponse.isSuccess());
        assertEquals(response2.getCode(), deserializedResponse.getCode());
        assertEquals(response2.getMessage(), deserializedResponse.getMessage());
    }

    /**
     * 测试 ItemPageResponse 类 (当前覆盖率: 34.02%)
     */
    @Test
    void testItemPageResponse() throws Exception {
        // 测试默认构造函数
        ItemPageResponse response1 = new ItemPageResponse();
        assertNotNull(response1);
        
        // 创建测试数据
        List<ItemResponse> items = Arrays.asList(testItemResponse);
        Pageable pageable = PageRequest.of(0, 10);
        Page<ItemResponse> page = new PageImpl<>(items, pageable, 1);
        
        // 测试带List和Page参数的构造函数
        ItemPageResponse response2 = new ItemPageResponse(items, page);
        assertEquals(items, response2.getItems());
        assertEquals(0, response2.getCurrentPage());
        assertEquals(10, response2.getSize());
        assertEquals(1, response2.getTotalItems());
        assertEquals(1, response2.getTotalPages());
        
        // 测试带Page<ItemResponse>参数的构造函数
        ItemPageResponse response3 = new ItemPageResponse(page);
        assertEquals(items, response3.getItems());
        assertEquals(0, response3.getCurrentPage());
        assertEquals(10, response3.getSize());
        assertEquals(1, response3.getTotalItems());
        assertEquals(1, response3.getTotalPages());
        
        // 测试Builder模式
        ItemPageResponse response4 = ItemPageResponse.builder()
                .items(items)
                .currentPage(1)
                .size(20)
                .totalItems(50)
                .totalPages(3)
                .source("database")
                .build();
        
        assertEquals(items, response4.getItems());
        assertEquals(1, response4.getCurrentPage());
        assertEquals(20, response4.getSize());
        assertEquals(50, response4.getTotalItems());
        assertEquals(3, response4.getTotalPages());
        assertEquals("database", response4.getSource());
        
        // 测试所有setter和getter方法
        response1.setItems(items);
        response1.setCurrentPage(2);
        response1.setSize(30);
        response1.setTotalItems(100);
        response1.setTotalPages(4);
        response1.setSource("elasticsearch");
        
        assertEquals(items, response1.getItems());
        assertEquals(2, response1.getCurrentPage());
        assertEquals(30, response1.getSize());
        assertEquals(100, response1.getTotalItems());
        assertEquals(4, response1.getTotalPages());
        assertEquals("elasticsearch", response1.getSource());
        
        // 测试兼容性方法
        response1.setContent(items);
        assertEquals(items, response1.getContent());
        
        response1.setPage(5);
        assertEquals(5, response1.getPage());
        
        response1.setTotalElements(200);
        assertEquals(200, response1.getTotalElements());
        
        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(response2);
        assertNotNull(json);
        assertTrue(json.contains("items"));
        assertTrue(json.contains("currentPage"));
        assertTrue(json.contains("totalItems"));
    }

    /**
     * 测试 NotificationDto 类 (当前覆盖率: 46.38%)
     */
    @Test
    void testNotificationDto() throws Exception {
        // 测试默认构造函数
        NotificationDto dto1 = new NotificationDto();
        assertNotNull(dto1);
        
        // 测试所有setter和getter方法
        dto1.setId(1L);
        dto1.setType(Notification.NotificationType.IDLE_ORDER_CONFIRMED);
        dto1.setContent("订单确认通知");
        dto1.setIsRead(true);
        dto1.setCreatedAt(LocalDateTime.now());
        dto1.setRelatedEntityId(100L);
        dto1.setSenderId(2L);
        dto1.setSenderUsername("sender");
        dto1.setTitle("通知标题");
        dto1.setSellerEmail("<EMAIL>");
        dto1.setSellerUsername("卖家");
        dto1.setItemName("商品名称");
        
        assertEquals(1L, dto1.getId());
        assertEquals(Notification.NotificationType.IDLE_ORDER_CONFIRMED, dto1.getType());
        assertEquals("订单确认通知", dto1.getContent());
        assertTrue(dto1.getIsRead());
        assertTrue(dto1.getIs_read()); // 测试前端兼容字段
        assertNotNull(dto1.getCreatedAt());
        assertEquals(100L, dto1.getRelatedEntityId());
        assertEquals(2L, dto1.getSenderId());
        assertEquals("sender", dto1.getSenderUsername());
        assertEquals("通知标题", dto1.getTitle());
        assertEquals("<EMAIL>", dto1.getSellerEmail());
        assertEquals("卖家", dto1.getSellerUsername());
        assertEquals("商品名称", dto1.getItemName());
        
        // 测试is_read字段的双向同步
        dto1.setIs_read(false);
        assertFalse(dto1.getIs_read());
        
        // 测试setIsRead对is_read字段的影响
        dto1.setIsRead(true);
        assertTrue(dto1.getIs_read());
        
        // 测试不同的通知类型
        dto1.setType(Notification.NotificationType.WANTED_OFFER_CONFIRMED);
        assertEquals(Notification.NotificationType.WANTED_OFFER_CONFIRMED, dto1.getType());
        
        dto1.setType(Notification.NotificationType.TRANSACTION_COMPLETED);
        assertEquals(Notification.NotificationType.TRANSACTION_COMPLETED, dto1.getType());
        
        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(dto1);
        assertNotNull(json);
        assertTrue(json.contains("订单确认通知"));
        assertTrue(json.contains("is_read"));
        assertTrue(json.contains("createdAt"));
        
        // 测试JSON反序列化
        NotificationDto deserializedDto = objectMapper.readValue(json, NotificationDto.class);
        assertEquals(dto1.getId(), deserializedDto.getId());
        assertEquals(dto1.getContent(), deserializedDto.getContent());
        assertEquals(dto1.getIsRead(), deserializedDto.getIsRead());
    }

    /**
     * 测试 JwtAuthResponse 类 (当前覆盖率: 72.34%)
     */
    @Test
    void testJwtAuthResponse() throws Exception {
        // 测试默认构造函数
        JwtAuthResponse response1 = new JwtAuthResponse();
        assertNotNull(response1);
        assertEquals("Bearer", response1.getTokenType()); // 默认值
        
        // 测试带部分参数的构造函数
        JwtAuthResponse response2 = new JwtAuthResponse("test_token", 1L, "testuser");
        assertEquals("test_token", response2.getToken());
        assertEquals("Bearer", response2.getTokenType());
        assertEquals(1L, response2.getId());
        assertEquals("testuser", response2.getUsername());
        
        // 测试带全部参数的构造函数
        JwtAuthResponse response3 = new JwtAuthResponse("test_token", "Custom", 1L, "testuser");
        assertEquals("test_token", response3.getToken());
        assertEquals("Custom", response3.getTokenType());
        assertEquals(1L, response3.getId());
        assertEquals("testuser", response3.getUsername());
        
        // 测试所有setter和getter方法
        response1.setToken("new_token");
        response1.setTokenType("JWT");
        response1.setId(99L);
        response1.setUsername("newuser");
        response1.setEmail("<EMAIL>");
        response1.setAvatar("new_avatar.jpg");
        
        assertEquals("new_token", response1.getToken());
        assertEquals("JWT", response1.getTokenType());
        assertEquals(99L, response1.getId());
        assertEquals("newuser", response1.getUsername());
        assertEquals("<EMAIL>", response1.getEmail());
        assertEquals("new_avatar.jpg", response1.getAvatar());
        
        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(response2);
        assertNotNull(json);
        assertTrue(json.contains("test_token"));
        assertTrue(json.contains("token_type")); // 验证@JsonProperty注解
        assertTrue(json.contains("avatar_url")); // 验证@JsonProperty注解，即使值为null
        
        // 测试JSON反序列化
        JwtAuthResponse deserializedResponse = objectMapper.readValue(json, JwtAuthResponse.class);
        assertEquals(response2.getToken(), deserializedResponse.getToken());
        assertEquals(response2.getTokenType(), deserializedResponse.getTokenType());
        assertEquals(response2.getId(), deserializedResponse.getId());
        assertEquals(response2.getUsername(), deserializedResponse.getUsername());
    }

    /**
     * 测试 ItemResponse 类的其他方法 (当前覆盖率: 28.85%)
     */
    @Test
    void testItemResponseAdditionalMethods() throws Exception {
        // 测试从Item构造的ItemResponse
        ItemResponse response = new ItemResponse(testItem);
        
        // 测试title字段（与name一致）
        assertEquals(testItem.getName(), response.getTitle());
        
        // 测试setTitle方法
        response.setTitle("新标题");
        assertEquals("新标题", response.getTitle());
        
        // 测试isFavorited字段
        assertFalse(response.getIsFavorited()); // 默认为false
        response.setIsFavorited(true);
        assertTrue(response.getIsFavorited());
        
        // 测试viewTime相关方法
        LocalDateTime viewTime = LocalDateTime.now();
        response.setViewTime(viewTime);
        assertNotNull(response.getViewTime());
        assertEquals(viewTime, response.getViewTimeRaw());
        
        // 测试字符串格式的viewTime设置
        response.setViewTime("2023-01-01 10:00:00");
        assertEquals("2023-01-01 10:00:00", response.getViewTime());
        
        // 测试LocalDateTime格式的createdAt和updatedAt设置
        LocalDateTime now = LocalDateTime.now();
        response.setCreatedAt(now);
        response.setUpdatedAt(now);
        assertNotNull(response.getCreatedAt());
        assertNotNull(response.getUpdatedAt());
        
        // 测试null值处理
        response.setCreatedAt((LocalDateTime) null);
        response.setUpdatedAt((LocalDateTime) null);
        response.setViewTime((LocalDateTime) null);
        assertNull(response.getCreatedAt());
        assertNull(response.getUpdatedAt());
        assertNull(response.getViewTime());
        assertNull(response.getViewTimeRaw());
        
        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(response);
        assertNotNull(json);
        assertTrue(json.contains("title"));
        assertTrue(json.contains("isFavorited"));
    }
} 