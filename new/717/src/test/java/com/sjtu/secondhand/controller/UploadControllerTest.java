package com.sjtu.secondhand.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CORSConfiguration;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.OssStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class UploadControllerTest {

    @Mock
    private FileStorageService fileStorageService;
    
    @Mock
    private OssStorageService ossStorageService;
    
    @Mock
    private OSS ossClient;

    @InjectMocks
    private UploadController uploadController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(uploadController).build();
        objectMapper = new ObjectMapper();
        
        // 设置配置属性
        ReflectionTestUtils.setField(uploadController, "contextPath", "/api");
        ReflectionTestUtils.setField(uploadController, "uploadDir", "uploads");
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        ReflectionTestUtils.setField(uploadController, "allowedOrigins", "http://localhost:3000");
        ReflectionTestUtils.setField(uploadController, "bucketName", "test-bucket");
        ReflectionTestUtils.setField(uploadController, "endpoint", "oss-cn-shanghai.aliyuncs.com");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getUploadSignature_shouldReturnLocalSignature_whenLocalStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");

        // Act & Assert
        mockMvc.perform(get("/upload/signature")
                .param("filename", "test.jpg")
                .param("content_type", "image/jpeg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("签名生成成功"))
                .andExpect(jsonPath("$.data.upload_url").value("/api/upload/file"))
                .andExpect(jsonPath("$.data.method").value("POST"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getUploadSignature_shouldReturnOssSignature_whenOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        Map<String, String> signatureData = new HashMap<>();
        signatureData.put("upload_url", "https://test-bucket.oss-cn-shanghai.aliyuncs.com/test.jpg");
        signatureData.put("method", "PUT");
        signatureData.put("expires", "3600");
        
        when(ossStorageService.generatePresignedUrl(eq("test.jpg"), eq("image/jpeg")))
                .thenReturn(signatureData);

        // Act & Assert
        mockMvc.perform(get("/upload/signature")
                .param("filename", "test.jpg")
                .param("content_type", "image/jpeg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("签名生成成功"))
                .andExpect(jsonPath("$.data.upload_url").value("https://test-bucket.oss-cn-shanghai.aliyuncs.com/test.jpg"))
                .andExpect(jsonPath("$.data.method").value("PUT"));

        verify(ossStorageService, times(1)).generatePresignedUrl("test.jpg", "image/jpeg");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getUploadSignature_shouldReturnError_whenOssSignatureEmpty() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        Map<String, String> signatureData = new HashMap<>();
        signatureData.put("upload_url", null);
        
        when(ossStorageService.generatePresignedUrl(eq("test.jpg"), eq("image/jpeg")))
                .thenReturn(signatureData);

        // Act & Assert
        mockMvc.perform(get("/upload/signature")
                .param("filename", "test.jpg")
                .param("content_type", "image/jpeg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("SIGNATURE_ERROR"))
                .andExpect(jsonPath("$.message").value("生成的上传URL为空"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getUploadSignature_shouldReturnError_whenOssServiceThrowsException() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        when(ossStorageService.generatePresignedUrl(eq("test.jpg"), eq("image/jpeg")))
                .thenThrow(new RuntimeException("OSS服务异常"));

        // Act & Assert
        mockMvc.perform(get("/upload/signature")
                .param("filename", "test.jpg")
                .param("content_type", "image/jpeg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("SIGNATURE_ERROR"))
                .andExpect(jsonPath("$.message").value("生成OSS签名失败: OSS服务异常"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void uploadFile_shouldUploadToLocal_whenLocalStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        
        MockMultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        
        when(fileStorageService.storeFile(any())).thenReturn("stored-test.jpg");

        // Act & Assert
        mockMvc.perform(multipart("/upload/file")
                .file(file)
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("文件上传成功"))
                .andExpect(jsonPath("$.data").value("/api/upload/files/stored-test.jpg"));

        verify(fileStorageService, times(1)).storeFile(any());
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void uploadFile_shouldUploadToOss_whenOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        MockMultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());
        
        when(ossStorageService.uploadFile(any())).thenReturn("https://test-bucket.oss-cn-shanghai.aliyuncs.com/test.jpg");

        // Act & Assert
        mockMvc.perform(multipart("/upload/file")
                .file(file)
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("文件上传成功"))
                .andExpect(jsonPath("$.data").value("https://test-bucket.oss-cn-shanghai.aliyuncs.com/test.jpg"));

        verify(ossStorageService, times(1)).uploadFile(any());
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void deleteFile_shouldDeleteFromLocal_whenLocalStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        
        when(fileStorageService.deleteFile(eq("test.jpg"))).thenReturn(true);

        // Act & Assert
        mockMvc.perform(delete("/upload/file")
                .param("objectName", "test.jpg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("文件删除成功"))
                .andExpect(jsonPath("$.data").value(true));

        verify(fileStorageService, times(1)).deleteFile("test.jpg");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void deleteFile_shouldDeleteFromOss_whenOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        when(ossStorageService.deleteFile(eq("test.jpg"))).thenReturn(true);

        // Act & Assert
        mockMvc.perform(delete("/upload/file")
                .param("objectName", "test.jpg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("文件删除成功"))
                .andExpect(jsonPath("$.data").value(true));

        verify(ossStorageService, times(1)).deleteFile("test.jpg");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void deleteFile_shouldReturnFalse_whenFileNotExists() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        
        when(fileStorageService.deleteFile(eq("nonexistent.jpg"))).thenReturn(false);

        // Act & Assert
        mockMvc.perform(delete("/upload/file")
                .param("objectName", "nonexistent.jpg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("文件删除失败，文件不存在"))
                .andExpect(jsonPath("$.data").value(false));

        verify(fileStorageService, times(1)).deleteFile("nonexistent.jpg");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getOssInfo_shouldReturnOssInfo_whenOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        List<SetBucketCORSRequest.CORSRule> corsRules = new ArrayList<>();
        SetBucketCORSRequest.CORSRule rule = new SetBucketCORSRequest.CORSRule();
        rule.setAllowedOrigins(Collections.singletonList("http://localhost:3000"));
        rule.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE"));
        rule.setAllowedHeaders(Collections.singletonList("*"));
        rule.setExposeHeaders(Collections.singletonList("ETag"));
        rule.setMaxAgeSeconds(3600);
        corsRules.add(rule);
        
        when(ossClient.getBucketCORSRules(eq("test-bucket"))).thenReturn(corsRules);

        // Act & Assert
        mockMvc.perform(get("/upload/oss-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取OSS配置信息成功"))
                .andExpect(jsonPath("$.data.bucketName").value("test-bucket"))
                .andExpect(jsonPath("$.data.endpoint").value("oss-cn-shanghai.aliyuncs.com"))
                .andExpect(jsonPath("$.data.corsRules").isArray())
                .andExpect(jsonPath("$.data.corsRules.length()").value(1));

        verify(ossClient, times(1)).getBucketCORSRules("test-bucket");
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getOssInfo_shouldReturnError_whenNotOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");

        // Act & Assert
        mockMvc.perform(get("/upload/oss-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("NOT_OSS"))
                .andExpect(jsonPath("$.message").value("当前存储类型不是OSS"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getOssInfo_shouldReturnError_whenOssClientThrowsException() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        when(ossClient.getBucketCORSRules(eq("test-bucket"))).thenThrow(new RuntimeException("OSS连接失败"));

        // Act & Assert
        mockMvc.perform(get("/upload/oss-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("OSS_INFO_ERROR"))
                .andExpect(jsonPath("$.message").value("获取OSS配置信息失败: OSS连接失败"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getOssInfo_shouldHandleNullCorsRules() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        when(ossClient.getBucketCORSRules(eq("test-bucket"))).thenReturn(null);

        // Act & Assert
        mockMvc.perform(get("/upload/oss-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取OSS配置信息成功"))
                .andExpect(jsonPath("$.data.corsRules").isArray())
                .andExpect(jsonPath("$.data.corsRules.length()").value(0));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void updateOssCors_shouldUpdateCors_whenOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        doNothing().when(ossClient).setBucketCORS(any());

        // Act & Assert
        mockMvc.perform(post("/upload/update-cors")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("OSS CORS规则更新成功"))
                .andExpect(jsonPath("$.data").value(true));

        verify(ossClient, times(1)).setBucketCORS(any());
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void updateOssCors_shouldReturnError_whenNotOssStorage() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");

        // Act & Assert
        mockMvc.perform(post("/upload/update-cors")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("NOT_OSS"))
                .andExpect(jsonPath("$.message").value("当前存储类型不是OSS"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void updateOssCors_shouldReturnError_whenOssClientThrowsException() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "oss");
        
        doThrow(new RuntimeException("更新CORS失败")).when(ossClient).setBucketCORS(any());

        // Act & Assert
        mockMvc.perform(post("/upload/update-cors")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("UPDATE_CORS_ERROR"))
                .andExpect(jsonPath("$.message").value("更新OSS CORS规则失败: 更新CORS失败"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getRequestInfo_shouldReturnRequestInfo() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/upload/request-info")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Test-Agent")
                .header("Authorization", "Bearer token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取请求信息成功"))
                .andExpect(jsonPath("$.data.headers").isMap())
                .andExpect(jsonPath("$.data.method").value("GET"))
                .andExpect(jsonPath("$.data.requestURI").value("/upload/request-info"));
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getRequestInfo_shouldIncludeAllRequestDetails() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/upload/request-info")
                .param("test", "value")
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Custom-Header", "CustomValue"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.queryString").value("test=value"))
                .andExpect(jsonPath("$.data.scheme").value("http"))
                .andExpect(jsonPath("$.data.serverName").value("localhost"))
                .andExpect(jsonPath("$.data.serverPort").value(80))
                .andExpect(jsonPath("$.data.headers.x-custom-header").value("CustomValue"));
    }

    @Test
    void getUploadSignature_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/upload/signature")
                .param("filename", "test.jpg")
                .param("content_type", "image/jpeg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void uploadFile_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile("file", "test.jpg", "image/jpeg", "test content".getBytes());

        // Act & Assert
        mockMvc.perform(multipart("/upload/file")
                .file(file)
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void deleteFile_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Act & Assert
        mockMvc.perform(delete("/upload/file")
                .param("objectName", "test.jpg")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void getOssInfo_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/upload/oss-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void updateOssCors_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/upload/update-cors")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void getRequestInfo_shouldReturnUnauthorized_whenNotAuthenticated() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/upload/request-info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testConstructor() {
        // Given
        FileStorageService mockFileStorageService = mock(FileStorageService.class);
        OssStorageService mockOssStorageService = mock(OssStorageService.class);

        // When
        UploadController controller = new UploadController(mockFileStorageService, mockOssStorageService);

        // Then
        assertNotNull(controller);
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void getUploadSignature_shouldHandleVariousFileTypes() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        
        String[] fileTypes = {"image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain"};
        String[] fileNames = {"test.jpg", "test.png", "test.gif", "test.pdf", "test.txt"};
        
        for (int i = 0; i < fileTypes.length; i++) {
            // Act & Assert
            mockMvc.perform(get("/upload/signature")
                    .param("filename", fileNames[i])
                    .param("content_type", fileTypes[i])
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.upload_url").value("/api/upload/file"));
        }
    }

    @Test
    @WithMockUser(username="testuser", roles={"USER"})
    void uploadFile_shouldHandleVariousFileTypes() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uploadController, "storageType", "local");
        
        String[] fileTypes = {"image/jpeg", "image/png", "text/plain"};
        String[] fileNames = {"test.jpg", "test.png", "test.txt"};
        
        when(fileStorageService.storeFile(any())).thenReturn("stored-file");
        
        for (int i = 0; i < fileTypes.length; i++) {
            MockMultipartFile file = new MockMultipartFile("file", fileNames[i], fileTypes[i], "content".getBytes());
            
            // Act & Assert
            mockMvc.perform(multipart("/upload/file")
                    .file(file)
                    .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").value("/api/upload/files/stored-file"));
        }
    }

    private void assertNotNull(UploadController controller) {
        if (controller == null) {
            throw new AssertionError("Controller should not be null");
        }
    }
}