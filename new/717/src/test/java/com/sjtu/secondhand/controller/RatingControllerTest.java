package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.service.RatingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class RatingControllerTest {

    @Mock
    private RatingService ratingService;

    @InjectMocks
    private RatingController ratingController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(ratingController).build();
        objectMapper = new ObjectMapper();
    }

    // Test createRating - Success
    @Test
    void createRating_Success() throws Exception {
        RatingRequest request = createValidRatingRequest();
        RatingResponse mockResponse = createMockRatingResponse();

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("评价创建成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.score").value(5));

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - IDLE transaction type
    @Test
    void createRating_IdleTransactionType() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);
        request.setScore(4);

        RatingResponse mockResponse = createMockRatingResponse();
        mockResponse.setTransaction_type("IDLE");

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("评价创建成功"))
                .andExpect(jsonPath("$.data.transaction_type").value("IDLE"));

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - WANTED transaction type
    @Test
    void createRating_WantedTransactionType() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("WANTED");
        request.setRelated_transaction_id(2);
        request.setScore(3);

        RatingResponse mockResponse = createMockRatingResponse();
        mockResponse.setTransaction_type("WANTED");

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("评价创建成功"))
                .andExpect(jsonPath("$.data.transaction_type").value("WANTED"));

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - Invalid request body
    @Test
    void createRating_InvalidRequestBody() throws Exception {
        String invalidJson = "{ invalid json }";

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - Null transaction type
    @Test
    void createRating_NullTransactionType() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type(null);
        request.setRelated_transaction_id(1);
        request.setScore(5);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()); // 验证注解会拦截

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - Null transaction ID
    @Test
    void createRating_NullTransactionId() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(null);
        request.setScore(5);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()); // 验证注解会拦截

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - Invalid score (too low)
    @Test
    void createRating_ScoreTooLow() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);
        request.setScore(0); // 低于最小值1

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()); // 验证注解会拦截

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - Invalid score (too high)
    @Test
    void createRating_ScoreTooHigh() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);
        request.setScore(6); // 高于最大值5

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()); // 验证注解会拦截

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - Service throws ApiException
    @Test
    void createRating_ServiceThrowsApiException() throws Exception {
        RatingRequest request = createValidRatingRequest();

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenThrow(new ApiException(HttpStatus.BAD_REQUEST, "交易不存在"));

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - Service throws RuntimeException
    @Test
    void createRating_ServiceThrowsRuntimeException() throws Exception {
        RatingRequest request = createValidRatingRequest();

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenThrow(new RuntimeException("Database connection error"));

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError());

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - Empty request body
    @Test
    void createRating_EmptyRequestBody() throws Exception {
        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest()); // 必填字段为空

        verify(ratingService, never()).createRating(any());
    }

    // Test createRating - All edge case scores
    @Test
    void createRating_EdgeCaseScores() throws Exception {
        // Test minimum valid score (1)
        RatingRequest request1 = createValidRatingRequest();
        request1.setScore(1);

        RatingResponse mockResponse1 = createMockRatingResponse();
        mockResponse1.setScore(1);

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse1);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.data.score").value(1));

        // Test maximum valid score (5)
        RatingRequest request2 = createValidRatingRequest();
        request2.setScore(5);

        RatingResponse mockResponse2 = createMockRatingResponse();
        mockResponse2.setScore(5);

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse2);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.data.score").value(5));

        verify(ratingService, times(2)).createRating(any(RatingRequest.class));
    }

    // Test createRating - Large transaction ID
    @Test
    void createRating_LargeTransactionId() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(Integer.MAX_VALUE);
        request.setScore(5);

        RatingResponse mockResponse = createMockRatingResponse();
        mockResponse.setRelated_transaction_id((long)Integer.MAX_VALUE);

        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true));

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - Invalid transaction type
    @Test
    void createRating_InvalidTransactionType() throws Exception {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("INVALID_TYPE");
        request.setRelated_transaction_id(1);
        request.setScore(5);

        RatingResponse mockResponse = createMockRatingResponse();
        when(ratingService.createRating(any(RatingRequest.class)))
            .thenReturn(mockResponse);

        // 注意：控制器层没有验证transaction_type的具体值，只验证@NotNull
        // 具体的业务验证由service层处理
        mockMvc.perform(post("/ratings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        verify(ratingService).createRating(any(RatingRequest.class));
    }

    // Test createRating - Multiple valid scores
    @Test
    void createRating_MultipleValidScores() throws Exception {
        for (int score = 1; score <= 5; score++) {
            RatingRequest request = createValidRatingRequest();
            request.setScore(score);

            RatingResponse mockResponse = createMockRatingResponse();
            mockResponse.setScore(score);

            when(ratingService.createRating(any(RatingRequest.class)))
                .thenReturn(mockResponse);

            mockMvc.perform(post("/ratings")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.data.score").value(score));
        }

        verify(ratingService, times(5)).createRating(any(RatingRequest.class));
    }

    // Helper methods
    private RatingRequest createValidRatingRequest() {
        RatingRequest request = new RatingRequest();
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);
        request.setScore(5);
        return request;
    }

    private RatingResponse createMockRatingResponse() {
        RatingResponse response = new RatingResponse();
        response.setId(1L);
        response.setScore(5);
        response.setTransaction_type("IDLE");
        response.setRelated_transaction_id(1L);
        response.setCreated_at(LocalDateTime.now());

        Map<String, Object> rater = new HashMap<>();
        rater.put("id", 1L);
        rater.put("username", "rater");
        rater.put("avatar_url", "avatar1.jpg");
        response.setRater(rater);

        Map<String, Object> ratee = new HashMap<>();
        ratee.put("id", 2L);
        ratee.put("username", "ratee");
        ratee.put("avatar_url", "avatar2.jpg");
        response.setRatee(ratee);

        return response;
    }
} 