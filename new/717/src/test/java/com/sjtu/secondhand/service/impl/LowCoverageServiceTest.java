package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.*;
import com.sjtu.secondhand.repository.*;
import com.sjtu.secondhand.repository.es.ItemDocumentRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 低覆盖率Service类的单元测试
 * 专门测试ElasticsearchSyncServiceImpl、RatingServiceImpl、RecommendationServiceImpl等
 */
@ExtendWith(MockitoExtension.class)
public class LowCoverageServiceTest {

    // ElasticsearchSyncServiceImpl 相关Mock
    @Mock
    private ItemRepository itemRepository;
    @Mock
    private ItemDocumentRepository itemDocumentRepository;
    @Mock
    private CategoryRepository categoryRepository;
    @Mock
    private ElasticsearchOperations elasticsearchOperations;
    @Mock
    private RestTemplate restTemplate;

    // RatingServiceImpl 相关Mock
    @Mock
    private RatingRepository ratingRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private OrderRepository orderRepository;
    @Mock
    private UserService userService;
    @Mock
    private NotificationService notificationService;

    // RecommendationServiceImpl 相关Mock
    @Mock
    private ItemSimilarityRepository itemSimilarityRepository;
    @Mock
    private ItemService itemService;
    @Mock
    private JdbcTemplate jdbcTemplate;

    private ElasticsearchSyncServiceImpl elasticsearchSyncService;
    private RatingServiceImpl ratingService;
    private RecommendationServiceImpl recommendationService;

    // 测试数据
    private User testUser;
    private User testUser2;
    private Item testItem;
    private Category testCategory;
    private Order testOrder;
    private Rating testRating;

    @BeforeEach
    void setUp() {
        // 初始化Service实例
        elasticsearchSyncService = new ElasticsearchSyncServiceImpl(
                itemRepository,
                itemDocumentRepository,
                categoryRepository,
                elasticsearchOperations
        );

        ratingService = new RatingServiceImpl(
                ratingRepository,
                userRepository,
                orderRepository,
                userService,
                notificationService
        );

        recommendationService = new RecommendationServiceImpl(
                itemRepository,
                itemSimilarityRepository,
                userRepository,
                userService,
                itemService,
                jdbcTemplate
        );

        // 创建测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setCreditScore(85);

        testUser2 = new User();
        testUser2.setId(2L);
        testUser2.setUsername("testuser2");
        testUser2.setEmail("<EMAIL>");
        testUser2.setCreditScore(90);

        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("电子产品");

        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("测试商品");
        testItem.setDescription("这是一个测试商品");
        testItem.setPrice(BigDecimal.valueOf(100.00));
        testItem.setCategory(testCategory);
        testItem.setCondition(Item.ItemCondition.FINE);
        testItem.setItemType(Item.ItemType.IDLE);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setUser(testUser);
        testItem.setViewCount(10);
        testItem.setFavoriteCount(5);
        testItem.setCreatedAt(LocalDateTime.now());
        testItem.setUpdatedAt(LocalDateTime.now());

        testOrder = new Order();
        testOrder.setId(1L);
        testOrder.setItem(testItem);
        testOrder.setBuyer(testUser2);
        testOrder.setSeller(testUser);
        testOrder.setStatus(Order.OrderStatus.COMPLETED);
        testOrder.setIsBuyerRated(false);
        testOrder.setIsSellerRated(false);
        testOrder.setCreatedAt(LocalDateTime.now());
        testOrder.setUpdatedAt(LocalDateTime.now());

        testRating = new Rating();
        testRating.setId(1L);
        testRating.setRater(testUser2);
        testRating.setRatee(testUser);
        testRating.setScore((byte) 4);
        testRating.setTransactionType(Rating.TransactionType.IDLE);
        testRating.setRelatedTransactionId(1L);
        testRating.setCreatedAt(LocalDateTime.now());
    }

    // ================= ElasticsearchSyncServiceImpl 测试 =================

    /**
     * 测试同步单个商品到Elasticsearch - 正常情况
     */
    @Test
    void testSyncItemToElasticsearch_Success() {
        // 准备
        when(itemDocumentRepository.save(any())).thenReturn(null);

        // 执行
        assertDoesNotThrow(() -> elasticsearchSyncService.syncItemToElasticsearch(testItem));

        // 验证
        verify(itemDocumentRepository).save(any());
    }

    /**
     * 测试同步空商品到Elasticsearch
     */
    @Test
    void testSyncItemToElasticsearch_NullItem() {
        // 执行
        assertDoesNotThrow(() -> elasticsearchSyncService.syncItemToElasticsearch(null));

        // 验证 - 不应该调用save方法
        verify(itemDocumentRepository, never()).save(any());
    }

    /**
     * 测试同步商品异常处理
     */
    @Test
    void testSyncItemToElasticsearch_Exception() {
        // 准备
        when(itemDocumentRepository.save(any())).thenThrow(new RuntimeException("ES连接异常"));

        // 执行 - 应该不抛出异常（因为是异步方法，内部捕获异常）
        assertDoesNotThrow(() -> elasticsearchSyncService.syncItemToElasticsearch(testItem));

        // 验证
        verify(itemDocumentRepository).save(any());
    }

    /**
     * 测试从Elasticsearch删除商品
     */
    @Test
    void testDeleteItemFromElasticsearch_Success() {
        // 准备
        doNothing().when(itemDocumentRepository).deleteById(1L);

        // 执行
        assertDoesNotThrow(() -> elasticsearchSyncService.deleteItemFromElasticsearch(1L));

        // 验证
        verify(itemDocumentRepository).deleteById(1L);
    }

    /**
     * 测试从Elasticsearch删除商品异常处理
     */
    @Test
    void testDeleteItemFromElasticsearch_Exception() {
        // 准备
        doThrow(new RuntimeException("ES删除异常")).when(itemDocumentRepository).deleteById(1L);

        // 执行 - 应该不抛出异常
        assertDoesNotThrow(() -> elasticsearchSyncService.deleteItemFromElasticsearch(1L));

        // 验证
        verify(itemDocumentRepository).deleteById(1L);
    }

    /**
     * 测试同步所有商品到Elasticsearch - 正常情况
     */
    @Test
    void testSyncAllItemsToElasticsearch_Success() {
        // 准备
        List<Item> items = Arrays.asList(testItem);
        when(itemRepository.findAll()).thenReturn(items);
        doNothing().when(itemDocumentRepository).deleteAll();
        when(itemDocumentRepository.saveAll(any())).thenReturn(Arrays.asList());

        // 执行
        assertDoesNotThrow(() -> elasticsearchSyncService.syncAllItemsToElasticsearch());

        // 验证
        verify(itemRepository).findAll();
        verify(itemDocumentRepository).deleteAll();
        verify(itemDocumentRepository).saveAll(any());
    }

    /**
     * 测试同步所有商品到Elasticsearch - 空数据库
     */
    @Test
    void testSyncAllItemsToElasticsearch_EmptyDatabase() {
        // 准备
        when(itemRepository.findAll()).thenReturn(Collections.emptyList());

        // 执行
        assertDoesNotThrow(() -> elasticsearchSyncService.syncAllItemsToElasticsearch());

        // 验证
        verify(itemRepository).findAll();
        verify(itemDocumentRepository, never()).saveAll(any());
    }

    /**
     * 测试同步所有商品异常处理
     */
    @Test
    void testSyncAllItemsToElasticsearch_Exception() {
        // 准备
        when(itemRepository.findAll()).thenThrow(new RuntimeException("数据库连接异常"));

        // 执行 & 验证
        assertThrows(RuntimeException.class, () -> elasticsearchSyncService.syncAllItemsToElasticsearch());
    }

    /**
     * 测试关键词搜索 - 回退到MySQL
     */
    @Test
    void testSearchItemsByKeyword() {
        // 执行
        Object result = elasticsearchSyncService.searchItemsByKeyword("测试", 0, 10);

        // 验证
        assertNotNull(result);
        assertTrue(result instanceof Map);
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        assertEquals("elasticsearch_fallback", resultMap.get("source"));
        assertEquals(0, resultMap.get("total"));
    }

    /**
     * 测试高级搜索 - 回退到MySQL
     */
    @Test
    void testAdvancedSearch() {
        // 执行
        Object result = elasticsearchSyncService.advancedSearch(
                "测试", 1L, 50.0, 200.0, "IDLE", "FINE", 0, 10, "relevance");

        // 验证
        assertNotNull(result);
        assertTrue(result instanceof Map);
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        assertEquals("elasticsearch_fallback", resultMap.get("source"));
        assertEquals(0, resultMap.get("total"));
    }

    // ================= RatingServiceImpl 测试 =================

    /**
     * 测试创建评分 - 买家评价卖家
     */
    @Test
    void testCreateRating_BuyerRateSeller() {
        // 准备
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(testUser2); // 买家
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, testUser2)).thenReturn(false);
        when(ratingRepository.save(any(Rating.class))).thenReturn(testRating);
        when(orderRepository.save(any(Order.class))).thenReturn(testOrder);

        // 执行
        RatingResponse response = ratingService.createRating(request);

        // 验证
        assertNotNull(response);
        verify(ratingRepository).save(any(Rating.class));
        verify(orderRepository).save(any(Order.class));
        // 验证买家评价状态已更新
        assertTrue(testOrder.getIsBuyerRated());
    }

    /**
     * 测试创建评分 - 卖家评价买家
     */
    @Test
    void testCreateRating_SellerRateBuyer() {
        // 准备
        RatingRequest request = new RatingRequest();
        request.setScore(4);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(testUser); // 卖家
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, testUser)).thenReturn(false);
        when(ratingRepository.save(any(Rating.class))).thenReturn(testRating);
        when(orderRepository.save(any(Order.class))).thenReturn(testOrder);

        // 执行
        RatingResponse response = ratingService.createRating(request);

        // 验证
        assertNotNull(response);
        verify(ratingRepository).save(any(Rating.class));
        verify(orderRepository).save(any(Order.class));
        // 验证卖家评价状态已更新
        assertTrue(testOrder.getIsSellerRated());
    }

    /**
     * 测试创建评分 - 订单不存在
     */
    @Test
    void testCreateRating_OrderNotFound() {
        // 准备
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(999);

        when(userService.getCurrentUser()).thenReturn(testUser2);
        when(orderRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(request));
        assertEquals("订单不存在", exception.getMessage());
    }

    /**
     * 测试创建评分 - 订单未完成
     */
    @Test
    void testCreateRating_OrderNotCompleted() {
        // 准备
        testOrder.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(testUser2);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(request));
        assertEquals("只能评价已完成的订单", exception.getMessage());
    }

    /**
     * 测试创建评分 - 已经评价过
     */
    @Test
    void testCreateRating_AlreadyRated() {
        // 准备
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(testUser2);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, testUser2)).thenReturn(true);

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(request));
        assertEquals("您已经对该交易进行了评价", exception.getMessage());
    }

    /**
     * 测试创建评分 - 无权限评价
     */
    @Test
    void testCreateRating_NotAuthorized() {
        // 准备
        User otherUser = new User();
        otherUser.setId(999L);
        
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("IDLE");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(otherUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));
        when(ratingRepository.existsByRelatedTransactionIdAndRater(1L, otherUser)).thenReturn(false);

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(request));
        assertEquals("您不是该订单的买家或卖家", exception.getMessage());
    }

    /**
     * 测试创建评分 - 无效交易类型
     */
    @Test
    void testCreateRating_InvalidTransactionType() {
        // 准备
        RatingRequest request = new RatingRequest();
        request.setScore(5);
        request.setTransaction_type("INVALID");
        request.setRelated_transaction_id(1);

        when(userService.getCurrentUser()).thenReturn(testUser2);

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, 
            () -> ratingService.createRating(request));
        assertEquals("无效的交易类型", exception.getMessage());
    }

    // ================= RecommendationServiceImpl 测试 =================

    /**
     * 测试获取热门推荐
     */
    @Test
    void testGetHotRecommendations() {
        // 准备
        List<Long> hotItemIds = Arrays.asList(1L, 2L, 3L);
        List<Item> hotItems = Arrays.asList(testItem);
        List<ItemResponse> expectedResponses = Arrays.asList(new ItemResponse(testItem));

        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(5))).thenReturn(hotItemIds);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(hotItems);
        when(itemService.convertItemsWithFavoriteStatus(hotItems)).thenReturn(expectedResponses);

        // 执行
        List<ItemResponse> result = recommendationService.getHotRecommendations(5);

        // 验证
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jdbcTemplate).queryForList(anyString(), eq(Long.class), eq(5));
        verify(itemRepository).findAllById(hotItemIds);
        verify(itemService).convertItemsWithFavoriteStatus(hotItems);
    }

    /**
     * 测试获取个性化推荐 - 用户有收藏
     */
    @Test
    void testGetItemCFRecommendations_WithFavorites() {
        // 准备
        Set<Item> favoriteItems = new HashSet<>(Arrays.asList(testItem));
        testUser.setFavoriteItems(favoriteItems);

        List<ItemSimilarity> similarities = Arrays.asList(
            createItemSimilarity(1L, 2L, 0.8),
            createItemSimilarity(1L, 3L, 0.7)
        );
        List<Item> recommendedItems = Arrays.asList(testItem);
        List<ItemResponse> expectedResponses = Arrays.asList(new ItemResponse(testItem));

        when(userService.getUserById(1L)).thenReturn(testUser);
        when(itemSimilarityRepository.findMostSimilarItems(1L, 4)).thenReturn(similarities);
        when(itemRepository.findAllById(any())).thenReturn(recommendedItems);
        when(itemService.convertItemsWithFavoriteStatus(recommendedItems)).thenReturn(expectedResponses);

        // 执行
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(1L, 10);

        // 验证
        assertNotNull(result);
        verify(userService).getUserById(1L);
        verify(itemSimilarityRepository).findMostSimilarItems(1L, 4);
    }

    /**
     * 测试获取个性化推荐 - 用户无收藏（冷启动）
     */
    @Test
    void testGetItemCFRecommendations_ColdStart() {
        // 准备
        testUser.setFavoriteItems(new HashSet<>());
        List<ItemResponse> hotRecommendations = Arrays.asList(new ItemResponse(testItem));

        when(userService.getUserById(1L)).thenReturn(testUser);
        // Mock热门推荐
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(10))).thenReturn(Arrays.asList(1L));
        when(itemRepository.findAllById(any())).thenReturn(Arrays.asList(testItem));
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(hotRecommendations);

        // 执行
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(1L, 10);

        // 验证
        assertNotNull(result);
        verify(userService).getUserById(1L);
        // 应该调用热门推荐而不是相似度查询
        verify(itemSimilarityRepository, never()).findMostSimilarItems(anyLong(), anyInt());
    }

    /**
     * 测试获取个性化推荐 - 用户不存在
     */
    @Test
    void testGetItemCFRecommendations_UserNotFound() {
        // 准备
        when(userService.getUserById(999L)).thenThrow(new ApiException(null, "用户不存在"));

        // 执行 & 验证
        assertThrows(ApiException.class, 
            () -> recommendationService.getItemCFRecommendations(999L, 10));
    }

    /**
     * 测试计算商品相似度方法
     */
    @Test
    void testCalculateItemSimilarities() {
        // 执行 - 这个方法被简化了，只是记录日志
        assertDoesNotThrow(() -> recommendationService.calculateItemSimilarities());
    }

    // ================= 辅助方法 =================

    private ItemSimilarity createItemSimilarity(Long itemId1, Long itemId2, Double score) {
        ItemSimilarity similarity = new ItemSimilarity();
        similarity.setItemId1(itemId1);
        similarity.setItemId2(itemId2);
        similarity.setScore(score);
        return similarity;
    }
} 