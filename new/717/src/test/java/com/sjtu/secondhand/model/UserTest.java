package com.sjtu.secondhand.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class UserTest {

    private User user;

    @BeforeEach
    void setUp() {
        user = new User();
    }

    @Test
    void testDefaultConstructor() {
        // When
        User newUser = new User();

        // Then
        assertNull(newUser.getId());
        assertNull(newUser.getUsername());
        assertNull(newUser.getPassword());
        assertNull(newUser.getEmail());
        assertNull(newUser.getAvatarUrl());
        assertEquals(50, newUser.getCreditScore());
        assertEquals(0, newUser.getPoints());
        assertNull(newUser.getLastCheckInDate());
        assertNull(newUser.getCreatedAt());
        assertNull(newUser.getUpdatedAt());
        assertNotNull(newUser.getFavoriteItems());
        assertTrue(newUser.getFavoriteItems().isEmpty());
    }

    @Test
    void testThreeParameterConstructor() {
        // Given
        String username = "testuser";
        String password = "password123";
        String email = "<EMAIL>";

        // When
        User newUser = new User(username, password, email);

        // Then
        assertEquals(username, newUser.getUsername());
        assertEquals(password, newUser.getPassword());
        assertEquals(email, newUser.getEmail());
        assertEquals(50, newUser.getCreditScore());
        assertEquals(0, newUser.getPoints());
        assertNotNull(newUser.getFavoriteItems());
    }

    @Test
    void testConstructorWithNullValues() {
        // When
        User newUser = new User(null, null, null);

        // Then
        assertNull(newUser.getUsername());
        assertNull(newUser.getPassword());
        assertNull(newUser.getEmail());
    }

    @Test
    void testSetAndGetId() {
        // Given
        Long id = 123L;

        // When
        user.setId(id);

        // Then
        assertEquals(id, user.getId());
    }

    @Test
    void testSetAndGetUsername() {
        // Given
        String username = "newusername";

        // When
        user.setUsername(username);

        // Then
        assertEquals(username, user.getUsername());
    }

    @Test
    void testSetAndGetPassword() {
        // Given
        String password = "newpassword";

        // When
        user.setPassword(password);

        // Then
        assertEquals(password, user.getPassword());
    }

    @Test
    void testSetAndGetEmail() {
        // Given
        String email = "<EMAIL>";

        // When
        user.setEmail(email);

        // Then
        assertEquals(email, user.getEmail());
    }

    @Test
    void testSetAndGetAvatarUrl() {
        // Given
        String avatarUrl = "https://example.com/avatar.jpg";

        // When
        user.setAvatarUrl(avatarUrl);

        // Then
        assertEquals(avatarUrl, user.getAvatarUrl());
    }

    @Test
    void testAvatarCompatibilityMethods() {
        // Given
        String avatarUrl = "https://example.com/avatar.jpg";

        // When
        user.setAvatar(avatarUrl);

        // Then
        assertEquals(avatarUrl, user.getAvatar());
        assertEquals(avatarUrl, user.getAvatarUrl());

        // When - Test setting via setAvatarUrl
        String newAvatarUrl = "https://example.com/new_avatar.jpg";
        user.setAvatarUrl(newAvatarUrl);

        // Then
        assertEquals(newAvatarUrl, user.getAvatar());
        assertEquals(newAvatarUrl, user.getAvatarUrl());
    }

    @Test
    void testGetPhone() {
        // When & Then
        assertNull(user.getPhone());
    }

    @Test
    void testGetJoinedAt() {
        // Given
        LocalDateTime createdAt = LocalDateTime.of(2023, 1, 15, 10, 30, 45);
        user.setCreatedAt(createdAt);

        // When
        String joinedAt = user.getJoinedAt();

        // Then
        assertEquals("2023-01-15T10:30:45", joinedAt);
    }

    @Test
    void testGetJoinedAtWithNullCreatedAt() {
        // Given
        user.setCreatedAt(null);

        // When
        String joinedAt = user.getJoinedAt();

        // Then
        assertNull(joinedAt);
    }

    @Test
    void testSetAndGetCreditScore() {
        // Given
        Integer creditScore = 75;

        // When
        user.setCreditScore(creditScore);

        // Then
        assertEquals(creditScore, user.getCreditScore());
    }

    @Test
    void testGetRating() {
        // Given
        Integer creditScore = 85;
        user.setCreditScore(creditScore);

        // When
        Integer rating = user.getRating();

        // Then
        assertEquals(creditScore, rating);
        assertEquals(85, rating);
    }

    @Test
    void testSetAndGetPoints() {
        // Given
        Integer points = 150;

        // When
        user.setPoints(points);

        // Then
        assertEquals(points, user.getPoints());
    }

    @Test
    void testSetAndGetLastCheckInDate() {
        // Given
        LocalDate checkInDate = LocalDate.of(2023, 12, 25);

        // When
        user.setLastCheckInDate(checkInDate);

        // Then
        assertEquals(checkInDate, user.getLastCheckInDate());
    }

    @Test
    void testSetAndGetCreatedAt() {
        // Given
        LocalDateTime createdAt = LocalDateTime.now();

        // When
        user.setCreatedAt(createdAt);

        // Then
        assertEquals(createdAt, user.getCreatedAt());
    }

    @Test
    void testSetAndGetUpdatedAt() {
        // Given
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        user.setUpdatedAt(updatedAt);

        // Then
        assertEquals(updatedAt, user.getUpdatedAt());
    }

    @Test
    void testSetAndGetFavoriteItems() {
        // Given
        Set<Item> favoriteItems = new HashSet<>();
        Item item1 = new Item();
        item1.setId(1L);
        Item item2 = new Item();
        item2.setId(2L);
        favoriteItems.add(item1);
        favoriteItems.add(item2);

        // When
        user.setFavoriteItems(favoriteItems);

        // Then
        assertEquals(favoriteItems, user.getFavoriteItems());
        assertEquals(2, user.getFavoriteItems().size());
    }

    @Test
    void testOnCreateMethod() throws Exception {
        // Given
        User testUser = new User();
        assertNull(testUser.getCreatedAt());
        assertNull(testUser.getUpdatedAt());

        // When - Call @PrePersist method via reflection
        Method onCreateMethod = User.class.getDeclaredMethod("onCreate");
        onCreateMethod.setAccessible(true);
        LocalDateTime beforeCall = LocalDateTime.now().minusSeconds(1);
        onCreateMethod.invoke(testUser);
        LocalDateTime afterCall = LocalDateTime.now().plusSeconds(1);

        // Then
        assertNotNull(testUser.getCreatedAt());
        assertNotNull(testUser.getUpdatedAt());
        assertTrue(testUser.getCreatedAt().isAfter(beforeCall));
        assertTrue(testUser.getCreatedAt().isBefore(afterCall));
        assertTrue(testUser.getUpdatedAt().isAfter(beforeCall));
        assertTrue(testUser.getUpdatedAt().isBefore(afterCall));
    }

    @Test
    void testOnUpdateMethod() throws Exception {
        // Given
        User testUser = new User();
        LocalDateTime originalCreatedAt = LocalDateTime.now().minusDays(1);
        testUser.setCreatedAt(originalCreatedAt);
        testUser.setUpdatedAt(originalCreatedAt);

        // When - Call @PreUpdate method via reflection
        Method onUpdateMethod = User.class.getDeclaredMethod("onUpdate");
        onUpdateMethod.setAccessible(true);
        LocalDateTime beforeCall = LocalDateTime.now().minusSeconds(1);
        onUpdateMethod.invoke(testUser);
        LocalDateTime afterCall = LocalDateTime.now().plusSeconds(1);

        // Then
        assertEquals(originalCreatedAt, testUser.getCreatedAt()); // createdAt should not change
        assertNotNull(testUser.getUpdatedAt());
        assertTrue(testUser.getUpdatedAt().isAfter(beforeCall));
        assertTrue(testUser.getUpdatedAt().isBefore(afterCall));
        assertTrue(testUser.getUpdatedAt().isAfter(originalCreatedAt));
    }

    @Test
    void testFavoriteItemsInitialization() {
        // Given
        User newUser = new User();

        // Then
        assertNotNull(newUser.getFavoriteItems());
        assertTrue(newUser.getFavoriteItems() instanceof HashSet);
        assertEquals(0, newUser.getFavoriteItems().size());
    }

    @Test
    void testAddFavoriteItem() {
        // Given
        Item favoriteItem = new Item();
        favoriteItem.setId(123L);
        favoriteItem.setName("Favorite Item");

        // When
        user.getFavoriteItems().add(favoriteItem);

        // Then
        assertEquals(1, user.getFavoriteItems().size());
        assertTrue(user.getFavoriteItems().contains(favoriteItem));
    }

    @Test
    void testDefaultValues() {
        // Given
        User newUser = new User();

        // Then
        assertEquals(50, newUser.getCreditScore());
        assertEquals(0, newUser.getPoints());
        assertEquals(50, newUser.getRating()); // getRating should return creditScore
    }

    @Test
    void testSetNullValues() {
        // When
        user.setId(null);
        user.setUsername(null);
        user.setPassword(null);
        user.setEmail(null);
        user.setAvatarUrl(null);
        user.setCreditScore(null);
        user.setPoints(null);
        user.setLastCheckInDate(null);
        user.setCreatedAt(null);
        user.setUpdatedAt(null);
        user.setFavoriteItems(null);

        // Then
        assertNull(user.getId());
        assertNull(user.getUsername());
        assertNull(user.getPassword());
        assertNull(user.getEmail());
        assertNull(user.getAvatarUrl());
        assertNull(user.getCreditScore());
        assertNull(user.getPoints());
        assertNull(user.getLastCheckInDate());
        assertNull(user.getCreatedAt());
        assertNull(user.getUpdatedAt());
        assertNull(user.getFavoriteItems());
    }

    @Test
    void testAvatarCompatibilityWithNullValues() {
        // When
        user.setAvatar(null);

        // Then
        assertNull(user.getAvatar());
        assertNull(user.getAvatarUrl());
    }

    @Test
    void testRatingWithNullCreditScore() {
        // Given
        user.setCreditScore(null);

        // When
        Integer rating = user.getRating();

        // Then
        assertNull(rating);
    }

    @Test
    void testCompleteUserState() {
        // Given
        Long id = 100L;
        String username = "completeuser";
        String password = "securepass";
        String email = "<EMAIL>";
        String avatarUrl = "https://example.com/avatar.jpg";
        Integer creditScore = 90;
        Integer points = 500;
        LocalDate checkInDate = LocalDate.now();
        LocalDateTime createdAt = LocalDateTime.now().minusDays(30);
        LocalDateTime updatedAt = LocalDateTime.now();
        Set<Item> favoriteItems = new HashSet<>();

        // When
        user.setId(id);
        user.setUsername(username);
        user.setPassword(password);
        user.setEmail(email);
        user.setAvatarUrl(avatarUrl);
        user.setCreditScore(creditScore);
        user.setPoints(points);
        user.setLastCheckInDate(checkInDate);
        user.setCreatedAt(createdAt);
        user.setUpdatedAt(updatedAt);
        user.setFavoriteItems(favoriteItems);

        // Then
        assertEquals(id, user.getId());
        assertEquals(username, user.getUsername());
        assertEquals(password, user.getPassword());
        assertEquals(email, user.getEmail());
        assertEquals(avatarUrl, user.getAvatarUrl());
        assertEquals(avatarUrl, user.getAvatar());
        assertEquals(creditScore, user.getCreditScore());
        assertEquals(creditScore, user.getRating());
        assertEquals(points, user.getPoints());
        assertEquals(checkInDate, user.getLastCheckInDate());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
        assertEquals(favoriteItems, user.getFavoriteItems());
        assertNull(user.getPhone());
    }

    @Test
    void testLongStringValues() {
        // Given
        String longUsername = "u".repeat(100);
        String longPassword = "p".repeat(300);
        String longEmail = "e".repeat(200) + "@example.com";
        String longAvatarUrl = "https://example.com/" + "a".repeat(400) + ".jpg";

        // When
        user.setUsername(longUsername);
        user.setPassword(longPassword);
        user.setEmail(longEmail);
        user.setAvatarUrl(longAvatarUrl);

        // Then
        assertEquals(longUsername, user.getUsername());
        assertEquals(longPassword, user.getPassword());
        assertEquals(longEmail, user.getEmail());
        assertEquals(longAvatarUrl, user.getAvatarUrl());
    }

    @Test
    void testEmptyStringValues() {
        // When
        user.setUsername("");
        user.setPassword("");
        user.setEmail("");
        user.setAvatarUrl("");

        // Then
        assertEquals("", user.getUsername());
        assertEquals("", user.getPassword());
        assertEquals("", user.getEmail());
        assertEquals("", user.getAvatarUrl());
        assertEquals("", user.getAvatar());
    }

    @Test
    void testExtremeNumberValues() {
        // When
        user.setCreditScore(Integer.MAX_VALUE);
        user.setPoints(Integer.MIN_VALUE);

        // Then
        assertEquals(Integer.MAX_VALUE, user.getCreditScore());
        assertEquals(Integer.MAX_VALUE, user.getRating());
        assertEquals(Integer.MIN_VALUE, user.getPoints());
    }
} 