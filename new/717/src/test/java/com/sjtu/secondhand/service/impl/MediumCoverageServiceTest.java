package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.*;
import com.sjtu.secondhand.repository.*;
import com.sjtu.secondhand.security.CustomUserDetailsService;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 中等覆盖率Service类的单元测试
 * 专门测试FootprintServiceImpl、CategoryServiceImpl、UserServiceImpl等
 */
@ExtendWith(MockitoExtension.class)
public class MediumCoverageServiceTest {

    // FootprintServiceImpl 相关Mock
    @Mock
    private FootprintRepository footprintRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private ItemRepository itemRepository;

    // CategoryServiceImpl 相关Mock
    @Mock
    private CategoryRepository categoryRepository;

    // UserServiceImpl 相关Mock
    @Mock
    private CustomUserDetailsService userDetailsService;

    // FileStorageServiceImpl 相关Mock
    @Mock
    private MultipartFile multipartFile;

    // OfferServiceImpl 相关Mock
    @Mock
    private OfferRepository offerRepository;
    @Mock
    private NotificationService notificationService;
    @Mock
    private UserService userService;

    // OrderServiceImpl 相关Mock
    @Mock
    private OrderRepository orderRepository;
    @Mock
    private NotificationEventService notificationEventService;

    // 服务实例
    private FootprintServiceImpl footprintService;
    private CategoryServiceImpl categoryService;
    private UserServiceImpl userServiceImpl;
    private FileStorageServiceImpl fileStorageService;
    private OfferServiceImpl offerService;
    private OrderServiceImpl orderService;

    // 测试数据
    private User testUser;
    private User testUser2;
    private Item testItem;
    private Category testCategory;
    private Footprint testFootprint;
    private Offer testOffer;
    private Order testOrder;

    @BeforeEach
    void setUp() {
        // 初始化Service实例
        footprintService = new FootprintServiceImpl(footprintRepository, userRepository, itemRepository);
        categoryService = new CategoryServiceImpl(categoryRepository);
        userServiceImpl = new UserServiceImpl(userRepository, userDetailsService);
        fileStorageService = new FileStorageServiceImpl();
        offerService = new OfferServiceImpl(offerRepository, itemRepository, userService, notificationService);
        orderService = new OrderServiceImpl(orderRepository, itemRepository, offerRepository, userService, notificationService, notificationEventService);

        // 创建测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setCreditScore(85);

        testUser2 = new User();
        testUser2.setId(2L);
        testUser2.setUsername("testuser2");
        testUser2.setEmail("<EMAIL>");
        testUser2.setCreditScore(90);

        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("电子产品");

        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("测试商品");
        testItem.setDescription("这是一个测试商品");
        testItem.setPrice(BigDecimal.valueOf(100.00));
        testItem.setCategory(testCategory);
        testItem.setCondition(Item.ItemCondition.FINE);
        testItem.setItemType(Item.ItemType.IDLE);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setUser(testUser);
        testItem.setViewCount(10);
        testItem.setFavoriteCount(5);
        testItem.setCreatedAt(LocalDateTime.now());
        testItem.setUpdatedAt(LocalDateTime.now());

        testFootprint = new Footprint();
        testFootprint.setId(1L);
        testFootprint.setUser(testUser);
        testFootprint.setItem(testItem);
        testFootprint.setViewTime(LocalDateTime.now());

        testOffer = new Offer();
        testOffer.setId(1L);
        testOffer.setWantedItem(testItem);
        testOffer.setOfferer(testUser2);
        testOffer.setRequester(testUser);
        testOffer.setStatus(Offer.OfferStatus.ACCEPTED);
        testOffer.setIsOffererRated(false);
        testOffer.setIsRequesterRated(false);
        testOffer.setCreatedAt(LocalDateTime.now());
        testOffer.setUpdatedAt(LocalDateTime.now());

        testOrder = new Order();
        testOrder.setId(1L);
        testOrder.setItem(testItem);
        testOrder.setBuyer(testUser2);
        testOrder.setSeller(testUser);
        testOrder.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        testOrder.setIsBuyerRated(false);
        testOrder.setIsSellerRated(false);
        testOrder.setCreatedAt(LocalDateTime.now());
        testOrder.setUpdatedAt(LocalDateTime.now());
    }

    // ================= FootprintServiceImpl 测试 =================

    /**
     * 测试记录足迹 - 新建足迹
     */
    @Test
    void testRecordFootprint_NewFootprint() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(footprintRepository.findByUserAndItemId(testUser, 1L)).thenReturn(Optional.empty());
        when(footprintRepository.save(any(Footprint.class))).thenReturn(testFootprint);

        // 执行
        boolean result = footprintService.recordFootprint(1L, 1L);

        // 验证
        assertTrue(result);
        verify(userRepository).findById(1L);
        verify(itemRepository).findById(1L);
        verify(footprintRepository).findByUserAndItemId(testUser, 1L);
        verify(footprintRepository).save(any(Footprint.class));
    }

    /**
     * 测试记录足迹 - 更新已有足迹
     */
    @Test
    void testRecordFootprint_UpdateExisting() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(footprintRepository.findByUserAndItemId(testUser, 1L)).thenReturn(Optional.of(testFootprint));
        when(footprintRepository.save(any(Footprint.class))).thenReturn(testFootprint);

        // 执行
        boolean result = footprintService.recordFootprint(1L, 1L);

        // 验证
        assertTrue(result);
        verify(footprintRepository).save(testFootprint);
        // 验证时间被更新了
        assertNotNull(testFootprint.getViewTime());
    }

    /**
     * 测试记录足迹 - 用户不存在
     */
    @Test
    void testRecordFootprint_UserNotFound() {
        // 准备
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行
        boolean result = footprintService.recordFootprint(999L, 1L);

        // 验证
        assertFalse(result);
        verify(userRepository).findById(999L);
        verify(footprintRepository, never()).save(any());
    }

    /**
     * 测试记录足迹 - 商品不存在
     */
    @Test
    void testRecordFootprint_ItemNotFound() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行
        boolean result = footprintService.recordFootprint(1L, 999L);

        // 验证
        assertFalse(result);
        verify(itemRepository).findById(999L);
        verify(footprintRepository, never()).save(any());
    }

    /**
     * 测试获取用户足迹 - 有足迹
     */
    @Test
    void testGetUserFootprints_WithFootprints() {
        // 准备
        List<Footprint> footprints = Arrays.asList(testFootprint);
        Pageable pageable = PageRequest.of(0, 10);
        Page<Footprint> footprintPage = new PageImpl<>(footprints, pageable, 1);

        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(footprintRepository.findByUserOrderByViewTimeDesc(testUser, pageable)).thenReturn(footprintPage);

        // 执行
        ItemPageResponse result = footprintService.getUserFootprints(1L, pageable);

        // 验证
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalItems());
        verify(footprintRepository).findByUserOrderByViewTimeDesc(testUser, pageable);
    }

    /**
     * 测试获取用户足迹 - 用户不存在
     */
    @Test
    void testGetUserFootprints_UserNotFound() {
        // 准备
        Pageable pageable = PageRequest.of(0, 10);
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行 & 验证
        assertThrows(ApiException.class, () -> footprintService.getUserFootprints(999L, pageable));
    }

    /**
     * 测试删除单个足迹
     */
    @Test
    void testDeleteFootprint() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        doNothing().when(footprintRepository).deleteByUserAndItemId(testUser, 1L);

        // 执行
        boolean result = footprintService.deleteFootprint(1L, 1L);

        // 验证
        assertTrue(result);
        verify(footprintRepository).deleteByUserAndItemId(testUser, 1L);
    }

    /**
     * 测试清空所有足迹
     */
    @Test
    void testClearAllFootprints() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        doNothing().when(footprintRepository).deleteAllByUser(testUser);

        // 执行
        boolean result = footprintService.clearAllFootprints(1L);

        // 验证
        assertTrue(result);
        verify(footprintRepository).deleteAllByUser(testUser);
    }

    /**
     * 测试删除足迹 - 用户不存在
     */
    @Test
    void testDeleteFootprint_UserNotFound() {
        // 准备
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行
        boolean result = footprintService.deleteFootprint(999L, 1L);

        // 验证
        assertFalse(result);
        verify(footprintRepository, never()).deleteByUserAndItemId(any(), anyLong());
    }

    // ================= CategoryServiceImpl 测试 =================

    /**
     * 测试获取所有分类
     */
    @Test
    void testGetAllCategories() {
        // 准备
        List<Category> categories = Arrays.asList(testCategory);
        when(categoryRepository.findAll()).thenReturn(categories);

        // 执行
        List<Category> result = categoryService.getAllCategories();

        // 验证
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("电子产品", result.get(0).getName());
        verify(categoryRepository).findAll();
    }

    /**
     * 测试构建分类树 - 有父子关系
     */
    @Test
    void testGetCategoryTree_WithParentChild() {
        // 准备
        Category parent = new Category();
        parent.setId(1);
        parent.setName("父分类");
        parent.setParent(null);

        Category child = new Category();
        child.setId(2);
        child.setName("子分类");
        child.setParent(parent);

        List<Category> allCategories = Arrays.asList(parent, child);
        when(categoryRepository.findAll()).thenReturn(allCategories);

        // 执行
        List<Category> result = categoryService.getCategoryTree();

        // 验证
        assertNotNull(result);
        assertEquals(1, result.size()); // 只有一个根分类
        assertEquals("父分类", result.get(0).getName());
        assertEquals(1, result.get(0).getChildren().size()); // 根分类有一个子分类
        assertEquals("子分类", result.get(0).getChildren().get(0).getName());
    }

    /**
     * 测试根据ID获取分类
     */
    @Test
    void testGetCategoryById_Success() {
        // 准备
        when(categoryRepository.findById(1)).thenReturn(Optional.of(testCategory));

        // 执行
        Category result = categoryService.getCategoryById(1L);

        // 验证
        assertNotNull(result);
        assertEquals("电子产品", result.getName());
        verify(categoryRepository).findById(1);
    }

    /**
     * 测试根据ID获取分类 - 分类不存在
     */
    @Test
    void testGetCategoryById_NotFound() {
        // 准备
        when(categoryRepository.findById(999)).thenReturn(Optional.empty());

        // 执行 & 验证
        assertThrows(RuntimeException.class, () -> categoryService.getCategoryById(999L));
    }

    /**
     * 测试创建分类
     */
    @Test
    void testCreateCategory() {
        // 准备
        when(categoryRepository.save(testCategory)).thenReturn(testCategory);

        // 执行
        Category result = categoryService.createCategory(testCategory);

        // 验证
        assertNotNull(result);
        assertEquals("电子产品", result.getName());
        verify(categoryRepository).save(testCategory);
    }

    /**
     * 测试更新分类
     */
    @Test
    void testUpdateCategory() {
        // 准备
        Category updatedCategory = new Category();
        updatedCategory.setName("更新后的分类");

        when(categoryRepository.findById(1)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.save(any(Category.class))).thenReturn(testCategory);

        // 执行
        Category result = categoryService.updateCategory(1L, updatedCategory);

        // 验证
        assertNotNull(result);
        verify(categoryRepository).findById(1);
        verify(categoryRepository).save(testCategory);
        assertEquals("更新后的分类", testCategory.getName());
    }

    /**
     * 测试删除分类
     */
    @Test
    void testDeleteCategory() {
        // 准备
        when(categoryRepository.findById(1)).thenReturn(Optional.of(testCategory));
        doNothing().when(categoryRepository).delete(testCategory);

        // 执行
        assertDoesNotThrow(() -> categoryService.deleteCategory(1L));

        // 验证
        verify(categoryRepository).findById(1);
        verify(categoryRepository).delete(testCategory);
    }

    // ================= UserServiceImpl 测试 =================

    /**
     * 测试获取当前用户
     */
    @Test
    void testGetCurrentUser() {
        // 准备
        Authentication authentication = mock(Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);

        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        SecurityContextHolder.setContext(securityContext);

        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // 执行
        User result = userServiceImpl.getCurrentUser();

        // 验证
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        verify(userRepository).findByUsername("testuser");
    }

    /**
     * 测试获取当前用户 - 用户不存在
     */
    @Test
    void testGetCurrentUser_NotFound() {
        // 准备
        Authentication authentication = mock(Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);

        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("nonexistent");
        SecurityContextHolder.setContext(securityContext);

        when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // 执行 & 验证
        assertThrows(ApiException.class, () -> userServiceImpl.getCurrentUser());
    }

    /**
     * 测试根据ID获取用户
     */
    @Test
    void testGetUserById() {
        // 准备
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

        // 执行
        User result = userServiceImpl.getUserById(1L);

        // 验证
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        verify(userRepository).findById(1L);
    }

    /**
     * 测试根据用户名获取用户
     */
    @Test
    void testGetUserByUsername() {
        // 准备
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // 执行
        User result = userServiceImpl.getUserByUsername("testuser");

        // 验证
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        verify(userRepository).findByUsername("testuser");
    }

    // ================= FileStorageServiceImpl 测试 =================

    /**
     * 测试文件存储初始化
     */
    @Test
    void testFileStorageInit() throws Exception {
        // 准备
        Path tempDir = Files.createTempDirectory("test-upload");

        // 执行
        fileStorageService.initForTest(tempDir);

        // 验证
        assertTrue(Files.exists(tempDir));

        // 清理
        Files.deleteIfExists(tempDir);
    }

    /**
     * 测试存储文件 - 成功
     */
    @Test
    void testStoreFile_Success() throws Exception {
        // 准备
        Path tempDir = Files.createTempDirectory("test-upload");
        fileStorageService.initForTest(tempDir);

        when(multipartFile.getOriginalFilename()).thenReturn("test.jpg");
        when(multipartFile.getInputStream()).thenReturn(mock(InputStream.class));

        // 执行
        String filename = fileStorageService.storeFile(multipartFile);

        // 验证
        assertNotNull(filename);
        assertTrue(filename.endsWith(".jpg"));
        assertNotEquals("test.jpg", filename); // 应该是生成的UUID名称

        // 清理
        Files.deleteIfExists(tempDir);
    }

    /**
     * 测试存储文件 - 无效文件名
     */
    @Test
    void testStoreFile_InvalidFilename() throws Exception {
        // 准备
        Path tempDir = Files.createTempDirectory("test-upload");
        fileStorageService.initForTest(tempDir);

        when(multipartFile.getOriginalFilename()).thenReturn("../../../malicious.txt");

        // 执行 & 验证
        assertThrows(ApiException.class, () -> fileStorageService.storeFile(multipartFile));

        // 清理
        Files.deleteIfExists(tempDir);
    }

    /**
     * 测试加载文件
     */
    @Test
    void testLoadFileAsResource() throws Exception {
        // 准备
        Path tempDir = Files.createTempDirectory("test-upload");
        fileStorageService.initForTest(tempDir);

        // 创建一个测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());

        // 执行
        var resource = fileStorageService.loadFileAsResource("test.txt");

        // 验证
        assertNotNull(resource);
        assertTrue(resource.exists());

        // 清理
        Files.deleteIfExists(testFile);
        Files.deleteIfExists(tempDir);
    }

    // ================= OfferServiceImpl 测试 =================

    /**
     * 测试创建Offer - 废弃方法
     */
    @Test
    void testCreateOffer_UnsupportedOperation() {
        // 准备
        OfferRequest request = new OfferRequest();

        // 执行 & 验证
        assertThrows(UnsupportedOperationException.class, () -> offerService.createOffer(request));
    }

    // ================= 辅助方法 =================

    private void setupSecurityContext() {
        Authentication authentication = mock(Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        SecurityContextHolder.setContext(securityContext);
    }
} 