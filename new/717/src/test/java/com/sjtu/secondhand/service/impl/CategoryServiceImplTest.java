package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.repository.CategoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CategoryServiceImplTest {

    @Mock
    private CategoryRepository categoryRepository;

    @InjectMocks
    private CategoryServiceImpl categoryService;

    private Category category1;
    private Category category2;

    @BeforeEach
    void setUp() {
        // 这个方法在每个测试用例运行前都会执行，用于初始化测试数据
        category1 = new Category();
        category1.setId(1);
        category1.setName("电子产品");

        category2 = new Category();
        category2.setId(2);
        category2.setName("图书教材");
    }

    @Test
    void testGetAllCategories() {
        // Arrange (准备)
        List<Category> mockCategories = Arrays.asList(category1, category2);
        when(categoryRepository.findAll()).thenReturn(mockCategories);

        // Act (执行)
        List<Category> result = categoryService.getAllCategories();

        // Assert (断言)
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("电子产品", result.get(0).getName());
        verify(categoryRepository, times(1)).findAll();
    }

    @Test
    void testGetCategoryById_whenCategoryExists() {
        // Arrange
        // 设定当调用 findById(1) 时，返回我们准备好的 category1 对象
        when(categoryRepository.findById(1)).thenReturn(Optional.of(category1));

        // Act
        Category result = categoryService.getCategoryById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getId());
        assertEquals("电子产品", result.getName());
        verify(categoryRepository, times(1)).findById(1);
    }

    @Test
    void testGetCategoryById_whenCategoryNotFound() {
        // Arrange
        // 设定当调用 findById(99) 时，返回一个空的 Optional，模拟找不到的情况
        when(categoryRepository.findById(99)).thenReturn(Optional.empty());

        // Act & Assert
        // 检查当分类不存在时，是否会按预期抛出 RuntimeException 异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            categoryService.getCategoryById(99L);
        });

        // 验证异常信息是否正确
        assertTrue(exception.getMessage().contains("分类不存在"));
        verify(categoryRepository, times(1)).findById(99);
    }
} 