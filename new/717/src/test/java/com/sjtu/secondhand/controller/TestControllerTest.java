package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class TestControllerTest {

    private MockMvc mockMvc;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private TestController testController;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mockMvc = MockMvcBuilders.standaloneSetup(testController).build();
    }

    // Test forceDeleteItem - Success
    @Test
    void forceDeleteItem_Success() throws Exception {
        Long itemId = 1L;
        
        // Mock item exists
        Item mockItem = new Item();
        mockItem.setId(itemId);
        mockItem.setName("Test Item");
        when(itemRepository.findById(itemId)).thenReturn(Optional.of(mockItem));
        
        // Mock users with favorites
        User user1 = new User();
        user1.setId(1L);
        user1.setFavoriteItems(new HashSet<>(Arrays.asList(mockItem)));
        
        User user2 = new User();
        user2.setId(2L);
        user2.setFavoriteItems(new HashSet<>());
        
        when(userRepository.findAll()).thenReturn(Arrays.asList(user1, user2));
        when(userRepository.save(any(User.class))).thenReturn(new User());

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品强制删除成功"));

        verify(orderRepository).deleteByItemId(itemId);
        verify(itemRepository).findById(itemId);
        verify(itemRepository).delete(mockItem);
        verify(userRepository).findAll();
        verify(userRepository).save(user1);  // user1 had the item in favorites
    }

    // Test forceDeleteItem - Item Not Found
    @Test
    void forceDeleteItem_ItemNotFound() throws Exception {
        Long itemId = 999L;
        
        when(itemRepository.findById(itemId)).thenReturn(Optional.empty());

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品强制删除成功"));

        verify(orderRepository).deleteByItemId(itemId);
        verify(itemRepository).findById(itemId);
        verify(itemRepository, never()).delete(any(Item.class));
    }

    // Test forceDeleteItem - Exception Handling
    @Test
    void forceDeleteItem_ExceptionHandling() throws Exception {
        Long itemId = 1L;
        
        // Mock exception during order deletion
        doThrow(new RuntimeException("Database error")).when(orderRepository).deleteByItemId(itemId);

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品强制删除成功"));

        verify(orderRepository).deleteByItemId(itemId);
    }

    // Test forceDeleteItem - User with null favorites
    @Test
    void forceDeleteItem_NullUserFavorites() throws Exception {
        Long itemId = 1L;
        
        Item mockItem = new Item();
        mockItem.setId(itemId);
        when(itemRepository.findById(itemId)).thenReturn(Optional.of(mockItem));
        
        User user = new User();
        user.setId(1L);
        user.setFavoriteItems(null);  // null favorites
        
        when(userRepository.findAll()).thenReturn(Arrays.asList(user));

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(itemRepository).delete(mockItem);
        verify(userRepository, never()).save(user);  // user with null favorites should not be saved
    }

    // Test forceDeleteSeek - Success
    @Test
    void forceDeleteSeek_Success() throws Exception {
        Long seekId = 1L;

        mockMvc.perform(delete("/test/seeks/{id}", seekId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("求购强制删除成功"));
    }

    // Test forceDeleteSeek - Different ID
    @Test
    void forceDeleteSeek_DifferentId() throws Exception {
        Long seekId = 999L;

        mockMvc.perform(delete("/test/seeks/{id}", seekId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("求购强制删除成功"));
    }

    // Test forceDeleteItem - Repository Exception during Item Deletion
    @Test
    void forceDeleteItem_ItemDeletionException() throws Exception {
        Long itemId = 1L;
        
        Item mockItem = new Item();
        mockItem.setId(itemId);
        when(itemRepository.findById(itemId)).thenReturn(Optional.of(mockItem));
        when(userRepository.findAll()).thenReturn(Arrays.asList());
        
        // Mock exception during item deletion
        doThrow(new RuntimeException("Delete failed")).when(itemRepository).delete(mockItem);

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品强制删除成功"));

        verify(itemRepository).delete(mockItem);
    }

    // Test forceDeleteItem - User Repository Exception
    @Test
    void forceDeleteItem_UserRepositoryException() throws Exception {
        Long itemId = 1L;
        
        Item mockItem = new Item();
        mockItem.setId(itemId);
        when(itemRepository.findById(itemId)).thenReturn(Optional.of(mockItem));
        
        // Mock exception during user operations
        when(userRepository.findAll()).thenThrow(new RuntimeException("User repo error"));

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品强制删除成功"));

        verify(userRepository).findAll();
    }

    // Test Edge Cases
    @Test
    void forceDeleteItem_EmptyUserList() throws Exception {
        Long itemId = 1L;
        
        Item mockItem = new Item();
        mockItem.setId(itemId);
        when(itemRepository.findById(itemId)).thenReturn(Optional.of(mockItem));
        when(userRepository.findAll()).thenReturn(Arrays.asList());

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(itemRepository).delete(mockItem);
        verify(userRepository, never()).save(any(User.class));
    }

    // Test Path Variable Validation 
    @Test
    void forceDeleteItem_ZeroId() throws Exception {
        Long itemId = 0L;

        mockMvc.perform(delete("/test/items/{id}", itemId))
                .andExpect(status().isOk());

        verify(orderRepository).deleteByItemId(itemId);
    }
} 