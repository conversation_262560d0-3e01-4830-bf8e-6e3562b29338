# ### 登录获取令牌
# POST http://localhost:8080/api/auth/login
# Content-Type: application/json

# {
#   "username": "testuser",
#   "password": "password"
# }

# > {%
#     client.global.set("auth_token", response.body.token);
# %}

# ### 创建寻求物品
# POST http://localhost:8080/api/seeks
# Content-Type: application/json
# Authorization: Bearer {{auth_token}}

# {
#   "title": "寻求高中数学课本",
#   "description": "需要一本高中数学必修1课本，人教版，二手即可",
#   "category": "教材",
#   "expectedPrice": 20.0,
#   "images": []
# }

# > {%
#     client.global.set("seek_id", response.body.id);
# %}

# ### 获取寻求物品详情
# GET http://localhost:8080/api/seeks/{{seek_id}}
# Authorization: Bearer {{auth_token}}

# ### 获取所有寻求物品
# GET http://localhost:8080/api/seeks?page=0&size=10
# Authorization: Bearer {{auth_token}}

# ### 更新寻求物品
# PUT http://localhost:8080/api/seeks/{{seek_id}}
# Content-Type: application/json
# Authorization: Bearer {{auth_token}}

# {
#   "title": "寻求高中数学课本(修改)",
#   "description": "需要一本高中数学必修1课本，人教版，二手即可，价格可议",
#   "category": "教材",
#   "expectedPrice": 25.0,
#   "images": []
# }

# ### 删除寻求物品
# DELETE http://localhost:8080/api/seeks/{{seek_id}}
# Authorization: Bearer {{auth_token}}

# ### 创建寻求响应
# POST http://localhost:8080/api/seeks/{{seek_id}}/responses
# Content-Type: application/json
# Authorization: Bearer {{auth_token}}

# {
#   "message": "我有一本二手的高中数学必修1课本，9成新",
#   "offeredPrice": 18.0,
#   "images": []
# }

# > {%
#     client.global.set("response_id", response.body.id);
# %}

# ### 获取寻求响应详情
# GET http://localhost:8080/api/seeks/responses/{{response_id}}
# Authorization: Bearer {{auth_token}}

# ### 获取寻求物品的所有响应
# GET http://localhost:8080/api/seeks/{{seek_id}}/responses
# Authorization: Bearer {{auth_token}}

# ### 接受寻求响应
# PUT http://localhost:8080/api/seeks/responses/{{response_id}}/accept
# Authorization: Bearer {{auth_token}}

# ### 完成寻求响应
# PUT http://localhost:8080/api/seeks/responses/{{response_id}}/complete
# Authorization: Bearer {{auth_token}}

# ### 评价响应者
# POST http://localhost:8080/api/seeks/responses/{{response_id}}/ratings/seeker
# Content-Type: application/json
# Authorization: Bearer {{auth_token}}

# {
#   "rating": 5,
#   "comment": "非常好的交易体验，书本质量很好",
#   "tags": ["守时", "诚信", "物品完好"]
# }

# ### 评价寻求者
# POST http://localhost:8080/api/seeks/responses/{{response_id}}/ratings/responder
# Content-Type: application/json
# Authorization: Bearer {{auth_token}}

# {
#   "rating": 5,
#   "comment": "很好的买家，交易顺利",
#   "tags": ["守时", "礼貌", "付款及时"]
# }

# ### 获取用户的所有通知
# GET http://localhost:8080/api/notifications
# Authorization: Bearer {{auth_token}}

# ### 获取用户的未读通知
# GET http://localhost:8080/api/notifications/unread
# Authorization: Bearer {{auth_token}}

# ### 获取用户的未读通知数量
# GET http://localhost:8080/api/notifications/count
# Authorization: Bearer {{auth_token}}

# ### 将通知标记为已读
# PUT http://localhost:8080/api/notifications/1/read
# Authorization: Bearer {{auth_token}}

# ### 将所有通知标记为已读
# PUT http://localhost:8080/api/notifications/read-all
# Authorization: Bearer {{auth_token}}

# ### 删除通知
# DELETE http://localhost:8080/api/notifications/1
# Authorization: Bearer {{auth_token}} 