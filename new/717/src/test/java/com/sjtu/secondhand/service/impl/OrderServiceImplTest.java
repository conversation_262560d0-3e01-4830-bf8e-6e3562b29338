package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OfferRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderServiceImplTest {

    @Mock
    private OrderRepository orderRepository;
    @Mock
    private ItemRepository itemRepository;
    @Mock
    private OfferRepository offerRepository;
    @Mock
    private UserService userService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private NotificationEventService notificationEventService;

    @InjectMocks
    private OrderServiceImpl orderService;

    private User buyer;
    private User seller;
    private Item item;
    private OrderRequest orderRequest;

    @BeforeEach
    void setUp() {
        buyer = new User("buyer", "password", "<EMAIL>");
        buyer.setId(1L);

        seller = new User("seller", "password", "<EMAIL>");
        seller.setId(2L);

        item = new Item();
        item.setId(1L);
        item.setUser(seller);
        item.setStatus(Item.ItemStatus.FOR_SALE);
        item.setItemType(Item.ItemType.IDLE);
        item.setName("Test Item");
        item.setPrice(new BigDecimal("100.00"));
        item.setCondition(Item.ItemCondition.BRAND_NEW);

        orderRequest = new OrderRequest();
        orderRequest.setItem_id(1);
    }

    @Test
    void createOrder_shouldSucceed_whenItemIsAvailable() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.existsActiveOrderForItem(any(Item.class))).thenReturn(false);
        when(orderRepository.save(any(Order.class))).thenAnswer(inv -> inv.getArgument(0));

        // Act
        OrderResponse response = orderService.createOrder(orderRequest);

        // Assert
        assertNotNull(response);
        assertEquals(item.getId(), response.getItem().get("id"));
        assertEquals(buyer.getId(), response.getBuyer().get("id"));
        assertEquals(Item.ItemStatus.RESERVED, item.getStatus()); // 验证物品状态已更新
        verify(itemRepository).save(item); // 验证物品被保存
        verify(orderRepository).save(any(Order.class)); // 验证订单被保存
        verify(notificationService).createNewOrderNotification(any(Order.class)); // 验证通知已发送
    }

    @Test
    void createOrder_shouldFail_whenItemNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.createOrder(orderRequest));
        assertEquals("商品不存在", exception.getMessage());
    }

    @Test
    void createOrder_shouldFail_whenItemIsNotForSale() {
        // Arrange
        item.setStatus(Item.ItemStatus.SOLD); // 设置物品状态为已售出
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.createOrder(orderRequest));
        assertEquals("该商品不可购买", exception.getMessage());
    }

    @Test
    void createOrder_shouldFail_whenBuyerIsSeller() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(seller); // 买家就是卖家
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.createOrder(orderRequest));
        assertEquals("不能购买自己的商品", exception.getMessage());
    }

    @Test
    void createOrder_shouldFail_whenItemIsAlreadyReserved() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.existsActiveOrderForItem(item)).thenReturn(true); // 模拟物品已有活跃订单

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.createOrder(orderRequest));
        assertEquals("该商品已有进行中的订单", exception.getMessage());
    }

    @Test
    void getOrderById_shouldReturnOrder_whenOrderExists() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        order.setCreatedAt(LocalDateTime.now());

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act
        OrderResponse response = orderService.getOrderById(1L);

        // Assert
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, response.getStatus());
        verify(orderRepository).findById(1L);
    }

    @Test
    void getOrderById_shouldThrowException_whenOrderNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.getOrderById(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("订单不存在", exception.getMessage());
    }

    @Test
    void getOrderById_shouldThrowException_whenUserNotAuthorized() {
        // Arrange
        User unauthorizedUser = new User();
        unauthorizedUser.setId(999L);
        unauthorizedUser.setUsername("unauthorized");

        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(userService.getCurrentUser()).thenReturn(unauthorizedUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.getOrderById(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("没有权限访问该订单"));
    }

    @Test
    void getOrderById_shouldHandleValidOrderAccess() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        order.setCreatedAt(LocalDateTime.now());

        when(userService.getCurrentUser()).thenReturn(seller); // 卖家访问订单
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act
        OrderResponse response = orderService.getOrderById(1L);

        // Assert
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, response.getStatus());
        verify(orderRepository).findById(1L);
    }

    @Test
    void confirmOrder_shouldUpdateStatusToConfirmed() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);

        // Act
        OrderResponse response = orderService.confirmOrder(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Order.OrderStatus.CONFIRMED, order.getStatus());
        verify(orderRepository).save(order);
        verify(notificationService).createOrderConfirmedNotification(order);
    }

    @Test
    void confirmOrder_shouldThrowException_whenNotSeller() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(buyer); // 买家尝试确认订单
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.confirmOrder(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有卖家"));
    }

    @Test
    void orderShouldHaveCorrectInitialStatus() {
        // Arrange & Act
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        // Assert
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, order.getStatus());
        assertEquals(buyer, order.getBuyer());
        assertEquals(seller, order.getSeller());
        assertEquals(item, order.getItem());
    }

    @Test
    void createOrder_shouldHandleRepositoryException() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.existsActiveOrderForItem(any(Item.class))).thenReturn(false);
        when(orderRepository.save(any(Order.class))).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> orderService.createOrder(orderRequest));
        assertEquals("Database error", exception.getMessage());
    }

    @Test
    void createOrder_shouldFailWhenItemTypeIsWanted() {
        // Arrange
        item.setItemType(Item.ItemType.WANTED);
        item.setCondition(Item.ItemCondition.BRAND_NEW); // 确保condition字段设置
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.createOrder(orderRequest));
        assertTrue(exception.getMessage().contains("求购类型"));
    }

    @Test
    void createOrder_shouldValidateItemOwnership() {
        // Arrange
        User itemOwner = new User();
        itemOwner.setId(3L);
        itemOwner.setUsername("owner");
        item.setUser(itemOwner);
        item.setCondition(Item.ItemCondition.BRAND_NEW); // 确保condition字段设置

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.existsActiveOrderForItem(any(Item.class))).thenReturn(false);

        // Act
        when(orderRepository.save(any(Order.class))).thenAnswer(inv -> inv.getArgument(0));
        OrderResponse response = orderService.createOrder(orderRequest);

        // Assert
        assertNotNull(response);
        assertEquals(itemOwner.getId(), response.getSeller().get("id"));
    }

    @Test
    void getMyBoughtOrders_shouldReturnPagedResults() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        order.setCreatedAt(LocalDateTime.now());

        List<Order> orders = Arrays.asList(order);
        Page<Order> orderPage = new PageImpl<>(orders, pageable, orders.size());

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findByBuyer(buyer, pageable)).thenReturn(orderPage);

        // Act
        Page<OrderResponse> result = orderService.getMyBoughtOrders(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(order.getId(), result.getContent().get(0).getId());
        verify(orderRepository).findByBuyer(buyer, pageable);
    }

    @Test
    void getMySoldOrders_shouldReturnPagedResults() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);
        order.setCreatedAt(LocalDateTime.now());

        List<Order> orders = Arrays.asList(order);
        Page<Order> orderPage = new PageImpl<>(orders, pageable, orders.size());

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findBySeller(seller, pageable)).thenReturn(orderPage);

        // Act
        Page<OrderResponse> result = orderService.getMySoldOrders(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(order.getId(), result.getContent().get(0).getId());
        verify(orderRepository).findBySeller(seller, pageable);
    }

    @Test
    void cancelOrder_shouldSucceed_whenBuyerCancels() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        // Act
        OrderResponse response = orderService.cancelOrder(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Order.OrderStatus.CANCELLED, order.getStatus());
        assertEquals(Item.ItemStatus.FOR_SALE, item.getStatus()); // 物品状态应该恢复
        verify(orderRepository).save(order);
        verify(itemRepository).save(item);
        verify(notificationService).createOrderCancelledNotification(order, buyer);
    }

    @Test
    void cancelOrder_shouldSucceed_whenSellerCancels() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.CONFIRMED);

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        // Act
        OrderResponse response = orderService.cancelOrder(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Order.OrderStatus.CANCELLED, order.getStatus());
        assertEquals(Item.ItemStatus.FOR_SALE, item.getStatus());
        verify(orderRepository).save(order);
        verify(itemRepository).save(item);
        verify(notificationService).createOrderCancelledNotification(order, seller);
    }

    @Test
    void cancelOrder_shouldFail_whenUserNotParticipant() {
        // Arrange
        User unauthorizedUser = new User();
        unauthorizedUser.setId(999L);
        unauthorizedUser.setUsername("unauthorized");

        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(unauthorizedUser);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.cancelOrder(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有买家或卖家"));
    }

    @Test
    void cancelOrder_shouldFail_whenOrderAlreadyCompleted() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.COMPLETED);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.cancelOrder(1L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("已完成或已取消"));
    }

    @Test
    void confirmContact_shouldSucceed_whenBuyerConfirms() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);

        // Act
        OrderResponse response = orderService.confirmContact(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Order.OrderStatus.CONFIRMED, order.getStatus());
        verify(orderRepository).save(order);
        verify(notificationService).createContactConfirmedNotification(order);
    }

    @Test
    void confirmContact_shouldFail_whenNotBuyer() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);

        when(userService.getCurrentUser()).thenReturn(seller); // 卖家尝试确认联系
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.confirmContact(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有买家"));
    }

    @Test
    void confirmContact_shouldFail_whenOrderNotConfirmed() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.confirmContact(1L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有待确认联系的订单"));
    }

    @Test
    void completeOrder_shouldSucceed_whenBuyerCompletes() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.CONFIRMED);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        // Act
        OrderResponse response = orderService.completeOrder(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Order.OrderStatus.COMPLETED, order.getStatus());
        assertEquals(Item.ItemStatus.SOLD, item.getStatus());
        verify(orderRepository).save(order);
        verify(itemRepository).save(item);
        verify(notificationService).createOrderCompletedNotification(order);
    }

    @Test
    void completeOrder_shouldFail_whenNotBuyer() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.CONFIRMED);

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.completeOrder(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有买家"));
    }

    @Test
    void completeOrder_shouldFail_whenOrderNotConfirmed() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.completeOrder(1L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有已确认的订单"));
    }

    @Test
    void confirmOrder_shouldFail_whenOrderNotPending() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.CONFIRMED); // 已经确认了

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> orderService.confirmOrder(1L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("只有待确认的订单"));
    }

    @Test
    void getMyBoughtOrders_shouldReturnEmptyPage_whenNoOrders() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Order> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findByBuyer(buyer, pageable)).thenReturn(emptyPage);

        // Act
        Page<OrderResponse> result = orderService.getMyBoughtOrders(pageable);

        // Assert
        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void getMySoldOrders_shouldReturnEmptyPage_whenNoOrders() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Order> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findBySeller(seller, pageable)).thenReturn(emptyPage);

        // Act
        Page<OrderResponse> result = orderService.getMySoldOrders(pageable);

        // Assert
        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void confirmOrder_shouldSendNotificationEvent() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        when(userService.getCurrentUser()).thenReturn(seller);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);

        // Act
        OrderResponse response = orderService.confirmOrder(1L);

        // Assert
        assertNotNull(response);
        verify(notificationEventService).sendOrderUpdateEvent(eq(order), eq("confirmed"), any(String.class));
    }

    @Test
    void createOrder_shouldHandleNotificationEventException() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(buyer);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.existsActiveOrderForItem(any(Item.class))).thenReturn(false);
        when(orderRepository.save(any(Order.class))).thenAnswer(inv -> inv.getArgument(0));
        doThrow(new RuntimeException("Event error")).when(notificationEventService)
                .sendOrderUpdateEvent(any(Order.class), any(String.class), any(String.class));

        // Act & Assert - 应该不抛异常，只记录错误
        assertDoesNotThrow(() -> orderService.createOrder(orderRequest));
        verify(notificationEventService).sendOrderUpdateEvent(any(Order.class), eq("created"), any(String.class));
    }

    @Test
    void completeOrder_shouldHandleNotificationEventException() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);
        order.setStatus(Order.OrderStatus.CONFIRMED);

        when(userService.getCurrentUser()).thenReturn(buyer);
        when(orderRepository.findById(1L)).thenReturn(Optional.of(order));
        when(orderRepository.save(any(Order.class))).thenReturn(order);
        when(itemRepository.save(any(Item.class))).thenReturn(item);
        doThrow(new RuntimeException("Event error")).when(notificationEventService)
                .sendOrderUpdateEvent(any(Order.class), any(String.class), any(String.class));

        // Act & Assert - 应该不抛异常，只记录错误
        assertDoesNotThrow(() -> orderService.completeOrder(1L));
        verify(notificationEventService).sendOrderUpdateEvent(any(Order.class), eq("completed"), any(String.class));
    }
} 