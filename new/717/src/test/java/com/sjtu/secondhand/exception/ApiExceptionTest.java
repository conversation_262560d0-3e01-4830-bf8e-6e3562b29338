package com.sjtu.secondhand.exception;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.*;

class ApiExceptionTest {

    @Test
    void testConstructorWithStatusAndMessage() {
        // Given
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = "Test error message";

        // When
        ApiException exception = new ApiException(status, message);

        // Then
        assertEquals(status, exception.getStatus());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void testConstructorWithSuperMessageStatusAndMessage() {
        // Given
        String superMessage = "Super error message";
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = "Custom error message";

        // When
        ApiException exception = new ApiException(superMessage, status, message);

        // Then
        assertEquals(status, exception.getStatus());
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void testGetStatus() {
        // Given
        HttpStatus expectedStatus = HttpStatus.UNAUTHORIZED;
        ApiException exception = new ApiException(expectedStatus, "Unauthorized access");

        // When
        HttpStatus actualStatus = exception.getStatus();

        // Then
        assertEquals(expectedStatus, actualStatus);
    }

    @Test
    void testGetMessage() {
        // Given
        String expectedMessage = "Not found";
        ApiException exception = new ApiException(HttpStatus.NOT_FOUND, expectedMessage);

        // When
        String actualMessage = exception.getMessage();

        // Then
        assertEquals(expectedMessage, actualMessage);
    }

    @Test
    void testGetMessageWithSuperMessageConstructor() {
        // Given
        String superMessage = "This is super message";
        String expectedMessage = "This is custom message";
        ApiException exception = new ApiException(superMessage, HttpStatus.FORBIDDEN, expectedMessage);

        // When
        String actualMessage = exception.getMessage();

        // Then
        assertEquals(expectedMessage, actualMessage);
    }

    @Test
    void testExceptionIsRuntimeException() {
        // Given
        ApiException exception = new ApiException(HttpStatus.BAD_REQUEST, "Test");

        // Then
        assertInstanceOf(RuntimeException.class, exception);
    }

    @Test
    void testDifferentHttpStatuses() {
        // Test various HTTP status codes
        HttpStatus[] statuses = {
            HttpStatus.OK,
            HttpStatus.CREATED,
            HttpStatus.BAD_REQUEST,
            HttpStatus.UNAUTHORIZED,
            HttpStatus.FORBIDDEN,
            HttpStatus.NOT_FOUND,
            HttpStatus.INTERNAL_SERVER_ERROR
        };

        for (HttpStatus status : statuses) {
            ApiException exception = new ApiException(status, "Test message for " + status);
            assertEquals(status, exception.getStatus());
            assertEquals("Test message for " + status, exception.getMessage());
        }
    }

    @Test
    void testNullMessage() {
        // Given
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = null;

        // When
        ApiException exception = new ApiException(status, message);

        // Then
        assertEquals(status, exception.getStatus());
        assertNull(exception.getMessage());
    }

    @Test
    void testEmptyMessage() {
        // Given
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = "";

        // When
        ApiException exception = new ApiException(status, message);

        // Then
        assertEquals(status, exception.getStatus());
        assertEquals("", exception.getMessage());
    }
} 