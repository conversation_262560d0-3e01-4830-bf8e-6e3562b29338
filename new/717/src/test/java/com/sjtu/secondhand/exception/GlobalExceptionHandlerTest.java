package com.sjtu.secondhand.exception;

import com.sjtu.secondhand.dto.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.WebRequest;

import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private WebRequest webRequest;



    @BeforeEach
    void setUp() {
        globalExceptionHandler = new GlobalExceptionHandler();
    }

    @Test
    void testHandleApiException() {
        // Given
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = "API error occurred";
        ApiException apiException = new ApiException(status, message);

        // When
        ResponseEntity<ApiResponse<Object>> response = globalExceptionHandler.handleApiException(apiException);

        // Then
        assertNotNull(response);
        assertEquals(status, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("400 BAD_REQUEST", response.getBody().getCode());
        assertEquals(message, response.getBody().getMessage());
    }

    @Test
    void testHandleApiExceptionWithDifferentStatus() {
        // Given
        HttpStatus status = HttpStatus.NOT_FOUND;
        String message = "Resource not found";
        ApiException apiException = new ApiException(status, message);

        // When
        ResponseEntity<ApiResponse<Object>> response = globalExceptionHandler.handleApiException(apiException);

        // Then
        assertNotNull(response);
        assertEquals(status, response.getStatusCode());
        assertEquals("404 NOT_FOUND", response.getBody().getCode());
        assertEquals(message, response.getBody().getMessage());
    }

    @Test
    void testHandleBadCredentialsException() {
        // Given
        BadCredentialsException exception = new BadCredentialsException("Invalid credentials");

        // When
        ResponseEntity<ApiResponse<Object>> response = globalExceptionHandler.handleBadCredentialsException();

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("AUTH_ERROR", response.getBody().getCode());
        assertEquals("用户名或密码错误", response.getBody().getMessage());
    }

    @Test
    void testHandleAccessDeniedException() {
        // Given
        AccessDeniedException exception = new AccessDeniedException("Access denied");

        // When
        ResponseEntity<ApiResponse<Object>> response = globalExceptionHandler.handleAccessDeniedException();

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("ACCESS_DENIED", response.getBody().getCode());
        assertEquals("没有访问权限", response.getBody().getMessage());
    }

    @Test
    void testHandleValidationErrors() {
        // Given - Create a real MethodArgumentNotValidException using spring framework classes
        String objectName = "user";
        String field1 = "name";
        String field2 = "email"; 
        String error1 = "Name is required";
        String error2 = "Email is invalid";
        
        // Create real FieldError objects
        FieldError fieldError1 = new FieldError(objectName, field1, error1);
        FieldError fieldError2 = new FieldError(objectName, field2, error2);
        
        // Create real validation exception using Spring's BeanPropertyBindingResult
        org.springframework.validation.BeanPropertyBindingResult bindingResult = 
            new org.springframework.validation.BeanPropertyBindingResult(new Object(), objectName);
        bindingResult.addError(fieldError1);
        bindingResult.addError(fieldError2);
        
        // Create real MethodArgumentNotValidException
        org.springframework.core.MethodParameter methodParameter = null;
        try {
            // Get a real method parameter for constructor
            java.lang.reflect.Method method = this.getClass().getMethod("dummyMethod", String.class);
            methodParameter = new org.springframework.core.MethodParameter(method, 0);
        } catch (Exception e) {
            // Fallback - create a simple parameter
            methodParameter = mock(org.springframework.core.MethodParameter.class);
        }
        
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        // When
        ResponseEntity<ApiResponse<Map<String, String>>> response = 
            globalExceptionHandler.handleValidationErrors(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("INVALID_PARAMS", response.getBody().getCode());
        assertEquals("参数验证失败", response.getBody().getMessage());
        
        Map<String, String> errors = response.getBody().getData();
        assertNotNull(errors);
        assertEquals(2, errors.size());
        assertEquals(error1, errors.get(field1));
        assertEquals(error2, errors.get(field2));
    }

    @Test
    void testHandleValidationErrorsWithSingleError() {
        // Given - Test with single validation error
        String objectName = "user";
        String field = "password";
        String errorMessage = "Password is too short";
        
        FieldError fieldError = new FieldError(objectName, field, errorMessage);
        
        org.springframework.validation.BeanPropertyBindingResult bindingResult = 
            new org.springframework.validation.BeanPropertyBindingResult(new Object(), objectName);
        bindingResult.addError(fieldError);
        
        org.springframework.core.MethodParameter methodParameter = mock(org.springframework.core.MethodParameter.class);
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        // When
        ResponseEntity<ApiResponse<Map<String, String>>> response = 
            globalExceptionHandler.handleValidationErrors(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Map<String, String> errors = response.getBody().getData();
        assertEquals(1, errors.size());
        assertEquals(errorMessage, errors.get(field));
    }

    @Test
    void testHandleValidationErrorsWithEmptyErrors() {
        // Given - Test with no validation errors
        String objectName = "user";
        
        org.springframework.validation.BeanPropertyBindingResult bindingResult = 
            new org.springframework.validation.BeanPropertyBindingResult(new Object(), objectName);
        // No errors added
        
        org.springframework.core.MethodParameter methodParameter = mock(org.springframework.core.MethodParameter.class);
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        // When
        ResponseEntity<ApiResponse<Map<String, String>>> response = 
            globalExceptionHandler.handleValidationErrors(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Map<String, String> errors = response.getBody().getData();
        assertNotNull(errors);
        assertTrue(errors.isEmpty());
    }
    
    @Test
    void testHandleValidationErrorsWithComplexFieldErrors() {
        // Given - Test with complex field errors including nested fields
        String objectName = "userRegistration";
        
        // Create various types of field errors
        FieldError fieldError1 = new FieldError(objectName, "user.name", "rejected value", false, null, null, "Name cannot be blank");
        FieldError fieldError2 = new FieldError(objectName, "user.email", "invalid@", false, new String[]{"Email"}, new Object[]{"email"}, "Email format is invalid");
        FieldError fieldError3 = new FieldError(objectName, "user.age", "15", false, new String[]{"Range"}, new Object[]{18, 100}, "Age must be between 18 and 100");
        
        org.springframework.validation.BeanPropertyBindingResult bindingResult = 
            new org.springframework.validation.BeanPropertyBindingResult(new Object(), objectName);
        bindingResult.addError(fieldError1);
        bindingResult.addError(fieldError2);
        bindingResult.addError(fieldError3);
        
        org.springframework.core.MethodParameter methodParameter = mock(org.springframework.core.MethodParameter.class);
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        // When
        ResponseEntity<ApiResponse<Map<String, String>>> response = 
            globalExceptionHandler.handleValidationErrors(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Map<String, String> errors = response.getBody().getData();
        assertEquals(3, errors.size());
        assertEquals("Name cannot be blank", errors.get("user.name"));
        assertEquals("Email format is invalid", errors.get("user.email"));
        assertEquals("Age must be between 18 and 100", errors.get("user.age"));
    }

    @Test
    void testHandleValidationErrorsWithNullDefaultMessage() {
        // Given - Test with field error that has null default message
        String objectName = "user";
        String field = "description";
        
        // Create FieldError with null default message
        FieldError fieldError = new FieldError(objectName, field, "some value", false, new String[]{"NotNull"}, null, null);
        
        org.springframework.validation.BeanPropertyBindingResult bindingResult = 
            new org.springframework.validation.BeanPropertyBindingResult(new Object(), objectName);
        bindingResult.addError(fieldError);
        
        org.springframework.core.MethodParameter methodParameter = mock(org.springframework.core.MethodParameter.class);
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        // When
        ResponseEntity<ApiResponse<Map<String, String>>> response = 
            globalExceptionHandler.handleValidationErrors(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Map<String, String> errors = response.getBody().getData();
        assertEquals(1, errors.size());
        assertNull(errors.get(field)); // null message should be preserved
    }

    @Test
    void testResponseStructureConsistency() {
        // Given - Test that all exception handlers return consistent response structure
        ApiException apiException = new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "Server error");
        BadCredentialsException badCredentialsException = new BadCredentialsException("Bad credentials");
        AccessDeniedException accessDeniedException = new AccessDeniedException("Access denied");
        ConstraintViolationException constraintException = new ConstraintViolationException("Constraint violation", null);
        RuntimeException runtimeException = new RuntimeException("Runtime error");

        // When
        ResponseEntity<ApiResponse<Object>> apiResponse = globalExceptionHandler.handleApiException(apiException);
        ResponseEntity<ApiResponse<Object>> badCredResponse = globalExceptionHandler.handleBadCredentialsException();
        ResponseEntity<ApiResponse<Object>> accessResponse = globalExceptionHandler.handleAccessDeniedException();
        ResponseEntity<ApiResponse<Object>> constraintResponse = globalExceptionHandler.handleConstraintViolation(constraintException);
        ResponseEntity<ApiResponse<Object>> globalResponse = globalExceptionHandler.handleGlobalException(runtimeException, webRequest);

        // Then - All responses should have consistent structure
        assertNotNull(apiResponse.getBody());
        assertNotNull(badCredResponse.getBody());
        assertNotNull(accessResponse.getBody());
        assertNotNull(constraintResponse.getBody());
        assertNotNull(globalResponse.getBody());

        // All should have timestamps
        assertNotNull(apiResponse.getBody().getTimestamp());
        assertNotNull(badCredResponse.getBody().getTimestamp());
        assertNotNull(accessResponse.getBody().getTimestamp());
        assertNotNull(constraintResponse.getBody().getTimestamp());
        assertNotNull(globalResponse.getBody().getTimestamp());

        // All should be marked as unsuccessful
        assertFalse(apiResponse.getBody().isSuccess());
        assertFalse(badCredResponse.getBody().isSuccess());
        assertFalse(accessResponse.getBody().isSuccess());
        assertFalse(constraintResponse.getBody().isSuccess());
        assertFalse(globalResponse.getBody().isSuccess());
    }

    // Dummy method for creating MethodParameter in test
    public void dummyMethod(String param) {
        // This method is only used for reflection in tests
    }

    @Test
    void testHandleConstraintViolation() {
        // Given
        String violationMessage = "Validation failed: size must be between 1 and 100";
        ConstraintViolationException exception = new ConstraintViolationException(violationMessage, null);

        // When
        ResponseEntity<ApiResponse<Object>> response = globalExceptionHandler.handleConstraintViolation(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("INVALID_PARAMS", response.getBody().getCode());
        assertEquals(violationMessage, response.getBody().getMessage());
    }

    @Test
    void testHandleGlobalException() {
        // Given
        String errorMessage = "Unexpected error occurred";
        Exception exception = new RuntimeException(errorMessage);

        // When
        ResponseEntity<ApiResponse<Object>> response = 
            globalExceptionHandler.handleGlobalException(exception, webRequest);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertEquals("SERVER_ERROR", response.getBody().getCode());
        assertTrue(response.getBody().getMessage().contains("发生了一个未处理的异常"));
        assertTrue(response.getBody().getMessage().contains(errorMessage));
    }

    @Test
    void testHandleGlobalExceptionWithNullMessage() {
        // Given
        Exception exception = new RuntimeException();

        // When
        ResponseEntity<ApiResponse<Object>> response = 
            globalExceptionHandler.handleGlobalException(exception, webRequest);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertTrue(response.getBody().getMessage().contains("发生了一个未处理的异常"));
    }

    @Test
    void testHandleGlobalExceptionWithDifferentExceptionTypes() {
        // Test with different exception types
        Exception[] exceptions = {
            new IllegalArgumentException("Invalid argument"),
            new NullPointerException("Null pointer"),
            new IllegalStateException("Invalid state")
        };

        for (Exception exception : exceptions) {
            ResponseEntity<ApiResponse<Object>> response = 
                globalExceptionHandler.handleGlobalException(exception, webRequest);
            
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
            assertEquals("SERVER_ERROR", response.getBody().getCode());
            assertTrue(response.getBody().getMessage().contains(exception.getMessage()));
        }
    }

    @Test
    void testAllResponsesHaveTimestamp() {
        // Test that all exception handlers return responses with timestamps
        ApiException apiException = new ApiException(HttpStatus.BAD_REQUEST, "Test");
        ResponseEntity<ApiResponse<Object>> apiResponse = globalExceptionHandler.handleApiException(apiException);
        assertNotNull(apiResponse.getBody().getTimestamp());

        ResponseEntity<ApiResponse<Object>> badCredentialsResponse = globalExceptionHandler.handleBadCredentialsException();
        assertNotNull(badCredentialsResponse.getBody().getTimestamp());

        ResponseEntity<ApiResponse<Object>> accessDeniedResponse = globalExceptionHandler.handleAccessDeniedException();
        assertNotNull(accessDeniedResponse.getBody().getTimestamp());

        ConstraintViolationException constraintException = new ConstraintViolationException("Test", null);
        ResponseEntity<ApiResponse<Object>> constraintResponse = globalExceptionHandler.handleConstraintViolation(constraintException);
        assertNotNull(constraintResponse.getBody().getTimestamp());

        Exception generalException = new Exception("Test");
        ResponseEntity<ApiResponse<Object>> generalResponse = globalExceptionHandler.handleGlobalException(generalException, webRequest);
        assertNotNull(generalResponse.getBody().getTimestamp());
    }
} 