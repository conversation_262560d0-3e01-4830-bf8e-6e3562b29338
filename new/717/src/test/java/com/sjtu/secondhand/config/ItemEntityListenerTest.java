package com.sjtu.secondhand.config;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ItemEntityListener的单元测试
 */
@ExtendWith(MockitoExtension.class)
class ItemEntityListenerTest {

    @Mock
    private ApplicationContext applicationContext;
    
    @Mock
    private ElasticsearchSyncService elasticsearchSyncService;
    
    @Mock
    private Item item;
    
    private ItemEntityListener itemEntityListener;
    
    @BeforeEach
    void setUp() {
        itemEntityListener = new ItemEntityListener();
        itemEntityListener.setApplicationContext(applicationContext);
    }
    
    @Test
    void testSetApplicationContext() {
        // Given
        ApplicationContext newContext = mock(ApplicationContext.class);
        
        // When
        itemEntityListener.setApplicationContext(newContext);
        
        // Then
        assertTrue(itemEntityListener.isServiceAvailable() || !itemEntityListener.isServiceAvailable());
        // 这个测试确保setApplicationContext方法被正确调用
    }
    
    @Test
    void testOnSaveOrUpdate_WithValidItem() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        
        // When
        itemEntityListener.onSaveOrUpdate(item);
        
        // Then
        verify(elasticsearchSyncService).syncItemToElasticsearch(item);
    }
    
    @Test
    void testOnSaveOrUpdate_WithNullItem() {
        // When
        itemEntityListener.onSaveOrUpdate(null);
        
        // Then
        // 方法应该正常返回，不会抛出异常
        verifyNoInteractions(elasticsearchSyncService);
    }
    
    @Test
    void testOnSaveOrUpdate_ServiceNotAvailable() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class))
            .thenThrow(new BeansException("Service not found") {});
        
        // When
        itemEntityListener.onSaveOrUpdate(item);
        
        // Then
        // 方法应该正常返回，不会抛出异常
        verifyNoInteractions(elasticsearchSyncService);
    }
    
    @Test
    void testOnSaveOrUpdate_ServiceThrowsException() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        doThrow(new RuntimeException("Sync failed")).when(elasticsearchSyncService).syncItemToElasticsearch(item);
        
        // When
        itemEntityListener.onSaveOrUpdate(item);
        
        // Then
        verify(elasticsearchSyncService).syncItemToElasticsearch(item);
        // 异常应该被捕获并记录日志
    }
    
    @Test
    void testPostRemove_WithValidItem() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        
        // When
        itemEntityListener.postRemove(item);
        
        // Then
        verify(elasticsearchSyncService).deleteItemFromElasticsearch(1L);
    }
    
    @Test
    void testPostRemove_WithNullItem() {
        // When
        itemEntityListener.postRemove(null);
        
        // Then
        verifyNoInteractions(elasticsearchSyncService);
    }
    
    @Test
    void testPostRemove_WithNullItemId() {
        // Given
        when(item.getId()).thenReturn(null);
        
        // When
        itemEntityListener.postRemove(item);
        
        // Then
        verifyNoInteractions(elasticsearchSyncService);
    }
    
    @Test
    void testPostRemove_ServiceNotAvailable() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class))
            .thenThrow(new BeansException("Service not found") {});
        
        // When
        itemEntityListener.postRemove(item);
        
        // Then
        verifyNoInteractions(elasticsearchSyncService);
    }
    
    @Test
    void testPostRemove_ServiceThrowsException() {
        // Given
        when(item.getId()).thenReturn(1L);
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        doThrow(new RuntimeException("Delete failed")).when(elasticsearchSyncService).deleteItemFromElasticsearch(1L);
        
        // When
        itemEntityListener.postRemove(item);
        
        // Then
        verify(elasticsearchSyncService).deleteItemFromElasticsearch(1L);
        // 异常应该被捕获并记录日志
    }
    
    @Test
    void testIsServiceAvailable_WhenServiceExists() {
        // Given
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        
        // When
        boolean result = itemEntityListener.isServiceAvailable();
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testIsServiceAvailable_WhenServiceNotExists() {
        // Given
        when(applicationContext.getBean(ElasticsearchSyncService.class))
            .thenThrow(new BeansException("Service not found") {});
        
        // When
        boolean result = itemEntityListener.isServiceAvailable();
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testIsServiceAvailable_WhenApplicationContextIsNull() {
        // Given
        ItemEntityListener listener = new ItemEntityListener();
        // 不设置ApplicationContext
        
        // When
        boolean result = listener.isServiceAvailable();
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testResetServiceCache() {
        // Given
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        
        // 先调用一次获取服务，让其被缓存
        itemEntityListener.isServiceAvailable();
        
        // When
        itemEntityListener.resetServiceCache();
        
        // Then
        // 再次调用应该重新从ApplicationContext获取服务
        itemEntityListener.isServiceAvailable();
        verify(applicationContext, times(2)).getBean(ElasticsearchSyncService.class);
    }
    
    @Test
    void testServiceCaching() {
        // Given
        when(applicationContext.getBean(ElasticsearchSyncService.class)).thenReturn(elasticsearchSyncService);
        
        // When
        itemEntityListener.isServiceAvailable();
        itemEntityListener.isServiceAvailable();
        
        // Then
        // 应该只调用一次getBean方法，因为第二次使用了缓存
        verify(applicationContext, times(1)).getBean(ElasticsearchSyncService.class);
    }
}