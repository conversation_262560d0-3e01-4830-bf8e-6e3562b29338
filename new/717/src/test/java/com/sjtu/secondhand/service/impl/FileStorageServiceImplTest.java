package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.core.io.Resource;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class FileStorageServiceImplTest {

    private FileStorageServiceImpl fileStorageService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() throws IOException {
        fileStorageService = new FileStorageServiceImpl();
        // 使用反射或修改构造函数来设置 uploadDir，这里我们用一个技巧来初始化
        // 为了简单起见，我们假设有一个setter
        // 在实际项目中，最好是通过构造函数注入依赖
        fileStorageService.initForTest(tempDir);
    }

    @Test
    void storeFile_shouldStoreFileInUploadDirectory() {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "Hello, World!".getBytes()
        );

        // Act
        String storedFileName = fileStorageService.storeFile(file);

        // Assert
        assertNotNull(storedFileName);
        assertTrue(Files.exists(tempDir.resolve(storedFileName)));
    }

    @Test
    void loadFileAsResource_shouldReturnResource_whenFileExists() {
        // Arrange
        String fileName = "existing-file.txt";
        Path filePath = tempDir.resolve(fileName);
        try {
            Files.write(filePath, "test data".getBytes());
        } catch (IOException e) {
            fail("Failed to create test file");
        }
        
        // Act
        Resource resource = fileStorageService.loadFileAsResource(fileName);

        // Assert
        assertNotNull(resource);
        assertTrue(resource.exists());
        assertEquals(fileName, resource.getFilename());
    }

    @Test
    void loadFileAsResource_shouldThrowException_whenFileNotExists() {
        // Arrange
        String nonExistentFile = "not-found.txt";

        // Act & Assert
        Exception exception = assertThrows(ApiException.class, () -> {
            fileStorageService.loadFileAsResource(nonExistentFile);
        });
        assertTrue(exception.getMessage().contains("文件未找到"));
    }
    
    @Test
    void deleteFile_shouldDeleteFile_whenFileExists() throws IOException {
        // Arrange
        String fileName = "to-be-deleted.txt";
        Path filePath = tempDir.resolve(fileName);
        Files.write(filePath, "delete me".getBytes());

        // Act
        boolean deleted = fileStorageService.deleteFile(fileName);

        // Assert
        assertTrue(deleted);
        assertFalse(Files.exists(filePath));
    }
}

// 为了让测试能够设置临时目录，我们需要在 FileStorageServiceImpl 中添加一个测试用的初始化方法
// 我现在就去修改 FileStorageServiceImpl.java 