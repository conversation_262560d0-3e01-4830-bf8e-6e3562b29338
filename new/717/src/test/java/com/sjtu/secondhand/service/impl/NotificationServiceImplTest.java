package com.sjtu.secondhand.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.response.NotificationDto;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.NotificationRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.NotificationEventService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.Item;
import java.math.BigDecimal;

@ExtendWith(MockitoExtension.class)
class NotificationServiceImplTest {

    @Mock
    private NotificationRepository notificationRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private NotificationEventService notificationEventService;
    @Spy
    private ObjectMapper objectMapper; // 使用@Spy来创建一个真实的ObjectMapper实例

    @InjectMocks
    private NotificationServiceImpl notificationService;

    private User user;
    private Notification notification;

    @BeforeEach
    void setUp() {
        user = new User();
        user.setId(1L);
        user.setUsername("testuser");

        notification = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 0L, "{\"message\":\"Welcome!\"}");
        notification.setId(1L);
    }

    @Test
    void createNotification_shouldSaveAndSendNotification() {
        // Arrange
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, "Test Content", Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        assertEquals(notification.getId(), result.getId());
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void markAsRead_shouldUpdateNotificationStatus_whenUserIsRecipient() {
        // Arrange
        notification.setIsRead(false);
        when(notificationRepository.findById(1L)).thenReturn(Optional.of(notification));
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.markAsRead(1L, 1L);

        // Assert
        assertNotNull(result);
        assertTrue(result.getIsRead());
        verify(notificationRepository).save(notification);
    }

    @Test
    void markAsRead_shouldThrowException_whenUserIsNotRecipient() {
        // Arrange
        notification.setIsRead(false);
        User anotherUser = new User();
        anotherUser.setId(2L);
        notification.setRecipient(anotherUser); // Set a different recipient
        
        when(notificationRepository.findById(1L)).thenReturn(Optional.of(notification));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> notificationService.markAsRead(1L, 1L)); // Current user is 1L
        assertEquals("您无权操作此通知", exception.getMessage());
    }

    @Test
    void getAllNotifications_shouldReturnUserNotifications() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipient(user)).thenReturn(Collections.singletonList(notification));

        // Act
        List<NotificationDto> results = notificationService.getAllNotifications(1L);

        // Assert
        assertFalse(results.isEmpty());
        assertEquals(1, results.size());
        assertEquals(notification.getId(), results.get(0).getId());
    }

    @Test
    void getAllNotifications_shouldThrowException_whenUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.getAllNotifications(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void getUnreadNotifications_shouldReturnUnreadNotifications() {
        // Arrange
        notification.setIsRead(false);
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipientAndIsReadFalse(user))
                .thenReturn(Collections.singletonList(notification));

        // Act
        List<NotificationDto> results = notificationService.getUnreadNotifications(1L);

        // Assert
        assertFalse(results.isEmpty());
        assertEquals(1, results.size());
        assertFalse(results.get(0).getIsRead());
    }

    @Test
    void getUnreadNotifications_shouldThrowException_whenUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.getUnreadNotifications(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void markAsRead_shouldThrowException_whenNotificationNotFound() {
        // Arrange
        when(notificationRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.markAsRead(999L, 1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("通知不存在", exception.getMessage());
    }

    @Test
    void createNotification_shouldHandleNullContent() {
        // Arrange
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, null, Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createNotification_shouldHandleEmptyContent() {
        // Arrange
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, "", Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createNotification_shouldHandleNotificationEventException() {
        // Arrange
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);
        doThrow(new RuntimeException("SSE error")).when(notificationEventService)
                .sendNotificationEvent(any(Notification.class));

        // Act & Assert - 应该不抛异常，只记录错误
        assertDoesNotThrow(() -> notificationService.createNotification(
                user, "Test", Notification.NotificationType.SYSTEM_WELCOME, 1L));
        
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createNotification_shouldCreateValidJsonContent() {
        // Arrange
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, "Test message with special chars: {}[]", 
                Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
    }

    @Test
    void createNotificationWithUserAndSender_shouldSetSender() {
        // Arrange
        User sender = new User();
        sender.setId(2L);
        sender.setUsername("sender");

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, sender, Notification.NotificationType.SYSTEM_WELCOME, 1L, "Test content");

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void markAsRead_shouldNotUpdateAlreadyReadNotification() {
        // Arrange
        notification.setIsRead(true); // 已经读过了
        notification.setRecipient(user); // 确保recipient正确设置
        when(notificationRepository.findById(1L)).thenReturn(Optional.of(notification));
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.markAsRead(1L, 1L);

        // Assert
        assertNotNull(result);
        assertTrue(result.getIsRead());
        // 由于已经是read状态，仍会调用save方法
        verify(notificationRepository).save(notification);
    }

    @Test
    void getAllNotifications_shouldReturnEmptyList_whenNoNotifications() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipient(user)).thenReturn(Collections.emptyList());

        // Act
        List<NotificationDto> results = notificationService.getAllNotifications(1L);

        // Assert
        assertTrue(results.isEmpty());
    }

    @Test
    void getUnreadNotifications_shouldReturnEmptyList_whenNoUnreadNotifications() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipientAndIsReadFalse(user))
                .thenReturn(Collections.emptyList());

        // Act
        List<NotificationDto> results = notificationService.getUnreadNotifications(1L);

        // Assert
        assertTrue(results.isEmpty());
    }

    @Test
    void getAllNotifications_shouldReturnMultipleNotifications() {
        // Arrange
        Notification notification2 = new Notification(
                user, null, Notification.NotificationType.SYSTEM_WELCOME, 2L, "{\"message\":\"Another notification\"}");
        notification2.setId(2L);

        List<Notification> notifications = Arrays.asList(notification, notification2);

        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipient(user)).thenReturn(notifications);

        // Act
        List<NotificationDto> results = notificationService.getAllNotifications(1L);

        // Assert
        assertEquals(2, results.size());
        assertEquals(notification.getId(), results.get(0).getId());
        assertEquals(notification2.getId(), results.get(1).getId());
    }

    @Test
    void markAllAsRead_shouldMarkAllUnreadNotifications() {
        // Arrange
        Notification notification1 = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 1L, "{\"message\":\"Welcome!\"}");
        notification1.setId(1L);
        notification1.setIsRead(false);
        
        Notification notification2 = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 2L, "{\"message\":\"Another!\"}");
        notification2.setId(2L);
        notification2.setIsRead(false);
        
        List<Notification> unreadNotifications = Arrays.asList(notification1, notification2);
        
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.findByRecipientAndIsReadFalse(user)).thenReturn(unreadNotifications);

        // Act
        notificationService.markAllAsRead(1L);

        // Assert
        assertTrue(notification1.getIsRead());
        assertTrue(notification2.getIsRead());
        verify(notificationRepository, times(2)).save(any(Notification.class));
    }

    @Test
    void markAllAsRead_shouldThrowException_whenUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.markAllAsRead(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void deleteNotification_shouldDeleteNotification_whenUserIsRecipient() {
        // Arrange
        when(notificationRepository.findById(1L)).thenReturn(Optional.of(notification));

        // Act
        notificationService.deleteNotification(1L, 1L);

        // Assert
        verify(notificationRepository).delete(notification);
    }

    @Test
    void deleteNotification_shouldThrowException_whenNotificationNotFound() {
        // Arrange
        when(notificationRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.deleteNotification(999L, 1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("通知不存在", exception.getMessage());
    }

    @Test
    void deleteNotification_shouldThrowException_whenUserIsNotRecipient() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        notification.setRecipient(anotherUser);
        
        when(notificationRepository.findById(1L)).thenReturn(Optional.of(notification));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> notificationService.deleteNotification(1L, 1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertEquals("您无权操作此通知", exception.getMessage());
    }

    @Test
    void countUnreadNotifications_shouldReturnCorrectCount() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(notificationRepository.countByRecipientAndIsReadFalse(user)).thenReturn(5L);

        // Act
        long count = notificationService.countUnreadNotifications(1L);

        // Assert
        assertEquals(5L, count);
        verify(notificationRepository).countByRecipientAndIsReadFalse(user);
    }

    @Test
    void countUnreadNotifications_shouldThrowException_whenUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> notificationService.countUnreadNotifications(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void createNewOrderNotification_shouldCreateNotificationForSeller() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createNewOrderNotification(order);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOrderConfirmedNotification_shouldCreateNotificationForBuyer() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOrderConfirmedNotification(order);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOrderCancelledNotification_shouldCreateNotificationForSeller_whenBuyerCancels() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOrderCancelledNotification(order, buyer);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOrderCancelledNotification_shouldCreateNotificationForBuyer_whenSellerCancels() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOrderCancelledNotification(order, seller);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOrderCompletedNotification_shouldCreateNotificationForSeller() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOrderCompletedNotification(order);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createContactConfirmedNotification_shouldCreateNotificationForSeller() {
        // Arrange
        User buyer = new User();
        buyer.setId(1L);
        buyer.setUsername("buyer");
        
        User seller = new User();
        seller.setId(2L);
        seller.setUsername("seller");
        
        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");
        
        Order order = new Order();
        order.setId(1L);
        order.setBuyer(buyer);
        order.setSeller(seller);
        order.setItem(item);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createContactConfirmedNotification(order);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createNewOfferNotification_shouldCreateNotificationForWantedItemOwner() {
        // Arrange
        User offerer = new User();
        offerer.setId(1L);
        offerer.setUsername("offerer");
        
        User wantedItemOwner = new User();
        wantedItemOwner.setId(2L);
        wantedItemOwner.setUsername("owner");
        
        Item wantedItem = new Item();
        wantedItem.setId(1L);
        wantedItem.setName("Wanted Item");
        wantedItem.setUser(wantedItemOwner);
        
        Offer offer = new Offer();
        offer.setId(1L);
        offer.setOfferer(offerer);
        offer.setWantedItem(wantedItem);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createNewOfferNotification(offer);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOfferAcceptedNotification_shouldCreateNotificationForOfferer() {
        // Arrange
        User offerer = new User();
        offerer.setId(1L);
        offerer.setUsername("offerer");
        
        User wantedItemOwner = new User();
        wantedItemOwner.setId(2L);
        wantedItemOwner.setUsername("owner");
        
        Item wantedItem = new Item();
        wantedItem.setId(1L);
        wantedItem.setName("Wanted Item");
        wantedItem.setUser(wantedItemOwner);
        
        Offer offer = new Offer();
        offer.setId(1L);
        offer.setOfferer(offerer);
        offer.setWantedItem(wantedItem);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOfferAcceptedNotification(offer);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOfferRejectedNotification_shouldCreateNotificationForOfferer() {
        // Arrange
        User offerer = new User();
        offerer.setId(1L);
        offerer.setUsername("offerer");
        
        User wantedItemOwner = new User();
        wantedItemOwner.setId(2L);
        wantedItemOwner.setUsername("owner");
        
        Item wantedItem = new Item();
        wantedItem.setId(1L);
        wantedItem.setName("Wanted Item");
        wantedItem.setUser(wantedItemOwner);
        
        Offer offer = new Offer();
        offer.setId(1L);
        offer.setOfferer(offerer);
        offer.setWantedItem(wantedItem);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOfferRejectedNotification(offer);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createOfferConfirmedNotification_shouldCreateNotificationForWantedItemOwner() {
        // Arrange
        User offerer = new User();
        offerer.setId(1L);
        offerer.setUsername("offerer");
        
        User wantedItemOwner = new User();
        wantedItemOwner.setId(2L);
        wantedItemOwner.setUsername("owner");
        
        Item wantedItem = new Item();
        wantedItem.setId(1L);
        wantedItem.setName("Wanted Item");
        wantedItem.setUser(wantedItemOwner);
        
        Offer offer = new Offer();
        offer.setId(1L);
        offer.setOfferer(offerer);
        offer.setWantedItem(wantedItem);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createOfferConfirmedNotification(offer);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createRatingReceivedNotification_shouldCreateNotificationForRatee() {
        // Arrange
        User rater = new User();
        rater.setId(1L);
        rater.setUsername("rater");
        
        User ratee = new User();
        ratee.setId(2L);
        ratee.setUsername("ratee");

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        notificationService.createRatingReceivedNotification(1L, ratee, rater);

        // Assert
        verify(notificationRepository).save(any(Notification.class));
        verify(notificationEventService).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void createJsonContent_shouldHandleAlreadyValidJson() {
        // Arrange
        String validJson = "{\"message\":\"Already valid JSON\",\"timestamp\":123456789}";
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, validJson, Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
    }

    @Test
    void createJsonContent_shouldHandleInvalidJsonLikeString() {
        // Arrange
        String invalidJsonLike = "{\"message\":\"Invalid JSON\""; // 缺少结束括号
        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, invalidJsonLike, Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        verify(notificationRepository).save(any(Notification.class));
    }

    @Test
    void mapToDto_shouldExtractSellerEmailFromOrderConfirmedNotification() {
        // Arrange
        User sender = new User();
        sender.setId(2L);
        sender.setUsername("seller");
        sender.setEmail("<EMAIL>");

        String jsonContent = "{\"message\":\"Order confirmed\",\"sellerEmail\":\"<EMAIL>\",\"sellerUsername\":\"seller\",\"itemName\":\"Test Item\"}";
        Notification orderNotification = new Notification(user, sender, Notification.NotificationType.IDLE_ORDER_CONFIRMED, 1L, jsonContent);
        orderNotification.setId(1L);

        when(notificationRepository.save(any(Notification.class))).thenReturn(orderNotification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, sender, Notification.NotificationType.IDLE_ORDER_CONFIRMED, 1L, "Order confirmed");

        // Assert
        assertNotNull(result);
        assertEquals("Order confirmed", result.getContent());
    }

    @Test
    void createOrderConfirmedNotification_shouldHandleJsonException() {
        // Arrange
        User seller = new User();
        seller.setId(1L);
        seller.setUsername("seller");
        seller.setEmail("<EMAIL>");

        User buyer = new User();
        buyer.setId(2L);
        buyer.setUsername("buyer");

        Item item = new Item();
        item.setId(1L);
        item.setName("Test Item");

        Order order = new Order();
        order.setId(1L);
        order.setSeller(seller);
        order.setBuyer(buyer);
        order.setItem(item);

        // 使用一个会抛出异常的ObjectMapper
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(mockObjectMapper.createObjectNode()).thenThrow(new RuntimeException("JSON error"));

        NotificationServiceImpl serviceWithMockMapper = new NotificationServiceImpl(
                notificationRepository, userRepository, mockObjectMapper, notificationEventService);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

        // Act
        serviceWithMockMapper.createOrderConfirmedNotification(order);

        // Assert
        verify(notificationRepository, times(2)).save(any(Notification.class)); // 一次失败，一次成功的后备方案
        verify(notificationEventService, times(2)).sendNotificationEvent(any(Notification.class));
    }

    @Test
    void getNotificationTitle_shouldReturnCorrectTitles() {
        // Arrange & Act & Assert - 测试不同通知类型的标题
        Notification welcomeNotification = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 1L, "{\"message\":\"Welcome!\"}");
        welcomeNotification.setId(1L);

        Notification ratingNotification = new Notification(user, null, Notification.NotificationType.NEW_RATING_RECEIVED, 1L, "{\"message\":\"New rating!\"}");
        ratingNotification.setId(2L);

        Notification commentNotification = new Notification(user, null, Notification.NotificationType.NEW_COMMENT_ON_ITEM, 1L, "{\"message\":\"New comment!\"}");
        commentNotification.setId(3L);

        when(notificationRepository.save(any(Notification.class)))
                .thenReturn(welcomeNotification)
                .thenReturn(ratingNotification)
                .thenReturn(commentNotification);

        // 测试系统欢迎通知
        NotificationDto welcomeResult = notificationService.createNotification(
                user, "Welcome", Notification.NotificationType.SYSTEM_WELCOME, 1L);
        assertEquals("系统通知", welcomeResult.getTitle());

        // 测试新评价通知
        NotificationDto ratingResult = notificationService.createNotification(
                user, "New rating", Notification.NotificationType.NEW_RATING_RECEIVED, 1L);
        assertEquals("收到评价", ratingResult.getTitle());

        // 测试新评论通知
        NotificationDto commentResult = notificationService.createNotification(
                user, "New comment", Notification.NotificationType.NEW_COMMENT_ON_ITEM, 1L);
        assertEquals("新评论", commentResult.getTitle());
    }

    @Test
    void mapToDto_shouldHandleNullSender() {
        // Arrange
        Notification notificationWithoutSender = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 1L, "{\"message\":\"Test\"}");
        notificationWithoutSender.setId(1L);

        when(notificationRepository.save(any(Notification.class))).thenReturn(notificationWithoutSender);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, "Test", Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        assertNull(result.getSenderId());
        assertNull(result.getSenderUsername());
    }

    @Test
    void mapToDto_shouldHandleInvalidJsonContent() {
        // Arrange
        Notification invalidJsonNotification = new Notification(user, null, Notification.NotificationType.SYSTEM_WELCOME, 1L, "invalid json content");
        invalidJsonNotification.setId(1L);

        when(notificationRepository.save(any(Notification.class))).thenReturn(invalidJsonNotification);

        // Act
        NotificationDto result = notificationService.createNotification(
                user, "invalid json content", Notification.NotificationType.SYSTEM_WELCOME, 1L);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getContent());
    }

    @Test
    void createJsonContent_shouldHandleObjectMapperException() {
        // Arrange
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(mockObjectMapper.createObjectNode()).thenThrow(new RuntimeException("ObjectMapper error"));

        NotificationServiceImpl serviceWithMockMapper = new NotificationServiceImpl(
                notificationRepository, userRepository, mockObjectMapper, notificationEventService);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> serviceWithMockMapper.createNotification(user, "test", Notification.NotificationType.SYSTEM_WELCOME, 1L));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("创建通知内容失败"));
    }
}