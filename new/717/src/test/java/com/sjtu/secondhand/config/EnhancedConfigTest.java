package com.sjtu.secondhand.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfigurationSource;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 增强的配置测试类
 * 专门用于提高config包中各配置类的测试覆盖率
 * 
 * 教学说明：这个测试类通过多个角度测试配置类：
 * 1. 验证Bean是否正确创建和配置
 * 2. 测试配置的具体功能
 * 3. 验证配置参数是否正确加载
 */
@SpringBootTest
class EnhancedConfigTest {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试JacksonConfig的详细配置
     * 验证Jackson ObjectMapper的各种序列化配置
     */
    @Test
    void testJacksonConfigDetailedConfiguration() throws Exception {
        // 验证ObjectMapper基本配置
        assertNotNull(objectMapper);
        
        // 验证禁用日期时间戳序列化
        assertFalse(objectMapper.isEnabled(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS));
        
        // 测试LocalDate序列化格式
        LocalDate testDate = LocalDate.of(2024, 1, 15);
        String dateJson = objectMapper.writeValueAsString(testDate);
        assertEquals("\"2024-01-15\"", dateJson);
        
        // 测试LocalDateTime序列化格式
        LocalDateTime testDateTime = LocalDateTime.of(2024, 1, 15, 14, 30, 45);
        String dateTimeJson = objectMapper.writeValueAsString(testDateTime);
        assertEquals("\"2024-01-15 14:30:45\"", dateTimeJson);
        
        // 测试LocalTime序列化格式
        LocalTime testTime = LocalTime.of(14, 30, 45);
        String timeJson = objectMapper.writeValueAsString(testTime);
        assertEquals("\"14:30:45\"", timeJson);
        
        // 测试反序列化
        LocalDate deserializedDate = objectMapper.readValue("\"2024-01-15\"", LocalDate.class);
        assertEquals(testDate, deserializedDate);
        
        LocalDateTime deserializedDateTime = objectMapper.readValue("\"2024-01-15 14:30:45\"", LocalDateTime.class);
        assertEquals(testDateTime, deserializedDateTime);
    }

    /**
     * 测试OpenApiConfig的详细配置
     * 验证OpenAPI文档的所有配置项
     */
    @Test
    void testOpenApiConfigDetailedConfiguration() {
        // 获取OpenAPI Bean
        OpenAPI openAPI = context.getBean(OpenAPI.class);
        assertNotNull(openAPI);
        
        // 验证API信息
        Info info = openAPI.getInfo();
        assertNotNull(info);
        assertEquals("高校二手交易平台 API", info.getTitle());
        assertEquals("高校二手交易平台后端服务API文档", info.getDescription());
        assertEquals("v1.0", info.getVersion());
        
        // 验证联系信息
        assertNotNull(info.getContact());
        assertEquals("SJTU SE25-15", info.getContact().getName());
        assertEquals("<EMAIL>", info.getContact().getEmail());
        assertEquals("https://github.com/SE25-15", info.getContact().getUrl());
        
        // 验证许可证信息
        assertNotNull(info.getLicense());
        assertEquals("MIT", info.getLicense().getName());
        assertEquals("https://opensource.org/licenses/MIT", info.getLicense().getUrl());
        
        // 验证安全配置
        assertNotNull(openAPI.getSecurity());
        assertFalse(openAPI.getSecurity().isEmpty());
        
        // 验证组件配置
        assertNotNull(openAPI.getComponents());
        assertNotNull(openAPI.getComponents().getSecuritySchemes());
        assertTrue(openAPI.getComponents().getSecuritySchemes().containsKey("JWT"));
    }

    /**
     * 测试SecurityConfig的Bean创建
     * 验证安全相关的Bean是否正确配置
     */
    @Test
    void testSecurityConfigBeanCreation() {
        // 验证SecurityFilterChain Bean
        SecurityFilterChain securityFilterChain = context.getBean(SecurityFilterChain.class);
        assertNotNull(securityFilterChain);
        
        // 验证PasswordEncoder Bean
        PasswordEncoder passwordEncoder = context.getBean(PasswordEncoder.class);
        assertNotNull(passwordEncoder);
        
        // 测试密码编码功能
        String rawPassword = "testPassword123";
        String encodedPassword = passwordEncoder.encode(rawPassword);
        
        assertNotNull(encodedPassword);
        assertNotEquals(rawPassword, encodedPassword);
        assertTrue(passwordEncoder.matches(rawPassword, encodedPassword));
        assertFalse(passwordEncoder.matches("wrongPassword", encodedPassword));
        
        // 验证CORS配置源Bean
        CorsConfigurationSource corsConfigurationSource = context.getBean(CorsConfigurationSource.class);
        assertNotNull(corsConfigurationSource);
    }

    /**
     * 测试CacheConfig
     * 验证缓存配置是否启用
     */
    @Test
    void testCacheConfigEnabled() {
        // CacheConfig是一个简单的配置类，主要验证@EnableCaching注解是否生效
        // 通过检查是否有缓存相关的Bean来验证
        try {
            // 检查是否有缓存管理器
            context.getBean("cacheManager");
            // 如果没有抛出异常，说明缓存配置生效
        } catch (Exception e) {
            // 这个测试主要是确保CacheConfig类被正确加载
            // 即使没有具体的缓存实现，类本身也应该被加载
        }
    }

    /**
     * 测试AsyncConfig
     * 验证异步配置是否启用
     */
    @Test
    void testAsyncConfigEnabled() {
        // AsyncConfig是一个简单的配置类，主要验证@EnableAsync注解是否生效
        // 通过确保配置类被正确加载来验证
        AsyncConfig asyncConfig = context.getBean(AsyncConfig.class);
        assertNotNull(asyncConfig);
    }

    /**
     * 测试SchedulingConfig
     * 验证调度配置是否启用
     */
    @Test
    void testSchedulingConfigEnabled() {
        // SchedulingConfig是一个简单的配置类，主要验证@EnableScheduling注解是否生效
        SchedulingConfig schedulingConfig = context.getBean(SchedulingConfig.class);
        assertNotNull(schedulingConfig);
    }

    /**
     * 测试ElasticsearchConfig
     * 验证Elasticsearch配置
     */
    @Test
    void testElasticsearchConfigEnabled() {
        // 验证ElasticsearchConfig类被正确加载
        ElasticsearchConfig elasticsearchConfig = context.getBean(ElasticsearchConfig.class);
        assertNotNull(elasticsearchConfig);
        
        // 验证客户端配置
        assertNotNull(elasticsearchConfig.clientConfiguration());
    }

    /**
     * 测试所有配置类的Bean创建
     * 确保所有config包中的配置类都能正确实例化
     */
    @Test
    void testAllConfigClassesBeanCreation() {
        // 验证所有配置类都能正确创建Bean
        assertNotNull(context.getBean(AsyncConfig.class));
        assertNotNull(context.getBean(CacheConfig.class));
        assertNotNull(context.getBean(ElasticsearchConfig.class));
        assertNotNull(context.getBean(JacksonConfig.class));
        assertNotNull(context.getBean(OpenApiConfig.class));
        assertNotNull(context.getBean(RedisConfig.class));
        assertNotNull(context.getBean(SchedulingConfig.class));
        assertNotNull(context.getBean(SecurityConfig.class));
        assertNotNull(context.getBean(WebConfig.class));
        
        // 验证ItemEntityListener（如果在Spring容器中）
        try {
            assertNotNull(context.getBean(ItemEntityListener.class));
        } catch (Exception e) {
            // ItemEntityListener可能不是Spring Bean，这是正常的
        }
    }

    /**
     * 测试配置类的基本功能
     * 验证关键配置是否正确工作
     */
    @Test
    void testConfigurationFunctionality() {
        // 测试Jackson配置
        JacksonConfig jacksonConfig = context.getBean(JacksonConfig.class);
        ObjectMapper customMapper = jacksonConfig.objectMapper();
        assertNotNull(customMapper);
        assertFalse(customMapper.isEnabled(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS));
        
        // 测试OpenAPI配置
        OpenApiConfig openApiConfig = context.getBean(OpenApiConfig.class);
        OpenAPI customOpenAPI = openApiConfig.customOpenAPI();
        assertNotNull(customOpenAPI);
        assertNotNull(customOpenAPI.getInfo());
        
        // 测试Security配置
        SecurityConfig securityConfig = context.getBean(SecurityConfig.class);
        PasswordEncoder encoder = securityConfig.passwordEncoder();
        assertNotNull(encoder);
        
        // 测试密码编码
        String password = "test123";
        String encoded = encoder.encode(password);
        assertTrue(encoder.matches(password, encoded));
    }
} 