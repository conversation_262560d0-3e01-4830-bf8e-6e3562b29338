package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.*;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.ItemSimilarityRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RecommendationServiceImplTest {

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private ItemSimilarityRepository itemSimilarityRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserService userService;

    @Mock
    private ItemService itemService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private RecommendationServiceImpl recommendationService;

    private User testUser;
    private Item testItem;
    private Category testCategory;
    private ItemResponse itemResponse;
    private ItemSimilarity itemSimilarity;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");

        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("Electronics");

        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("Test Item");
        testItem.setDescription("Test Description");
        testItem.setPrice(new BigDecimal("100.00"));
        testItem.setUser(testUser);
        testItem.setCategory(testCategory);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setIsVisible(true);
        testItem.setFavoriteCount(10);
        testItem.setViewCount(100);

        itemResponse = new ItemResponse();
        itemResponse.setId(1L);
        itemResponse.setName("Test Item");
        itemResponse.setPrice(new BigDecimal("100.00"));

        itemSimilarity = new ItemSimilarity();
        itemSimilarity.setItemId1(1L);
        itemSimilarity.setItemId2(2L);
        itemSimilarity.setScore(0.85);
    }

    @Test
    void getHotRecommendations_Success() {
        // Arrange
        List<Long> hotItemIds = Arrays.asList(1L);
        List<Item> hotItems = Arrays.asList(testItem);
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(10)))
            .thenReturn(hotItemIds);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(hotItems);
        when(itemService.convertItemsWithFavoriteStatus(hotItems)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getHotRecommendations(10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jdbcTemplate).queryForList(anyString(), eq(Long.class), eq(10));
        verify(itemRepository).findAllById(hotItemIds);
        verify(itemService).convertItemsWithFavoriteStatus(hotItems);
    }

    @Test
    void getHotRecommendations_EmptyResult() {
        // Arrange
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(10)))
            .thenReturn(Arrays.asList());
        when(itemRepository.findAllById(any())).thenReturn(Arrays.asList());
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(Arrays.asList());

        // Act
        List<ItemResponse> result = recommendationService.getHotRecommendations(10);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getItemCFRecommendations_NoFavorites() {
        // Arrange
        when(userService.getUserById(1L)).thenReturn(testUser);
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(1L), eq(5)))
            .thenReturn(Arrays.asList());

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(1L, 16);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getItemCFRecommendations_WithException_ReturnsEmpty() {
        // Arrange
        when(userService.getUserById(1L)).thenReturn(testUser);
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(1L), eq(5)))
            .thenThrow(new RuntimeException("Database error"));

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(1L, 16);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getContentBasedRecommendations_Success() {
        // Arrange
        List<ItemSimilarity> similarities = Arrays.asList(itemSimilarity);
        List<Item> items = Arrays.asList(testItem);
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);

        when(itemSimilarityRepository.findMostSimilarItems(1L, 10))
            .thenReturn(similarities);
        when(itemRepository.findAllById(any())).thenReturn(items);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemSimilarityRepository).findMostSimilarItems(1L, 10);
    }

    @Test
    void getContentBasedRecommendations_NoSimilarItems_UsesFallback() {
        // Arrange
        when(itemSimilarityRepository.findMostSimilarItems(1L, 10))
            .thenReturn(Arrays.asList());
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        
        List<Item> fallbackItems = Arrays.asList(testItem);
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        
        when(itemRepository.findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
            eq(1), eq(Item.ItemStatus.FOR_SALE), eq(true), eq(1L), 
            any(BigDecimal.class), any(BigDecimal.class), any(Pageable.class)))
            .thenReturn(fallbackItems);
        when(itemService.convertItemsWithFavoriteStatus(fallbackItems)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemRepository).findById(1L);
    }

    @Test
    void getContentBasedRecommendations_ItemNotFound() {
        // Arrange
        when(itemSimilarityRepository.findMostSimilarItems(1L, 10))
            .thenReturn(Arrays.asList());
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> recommendationService.getContentBasedRecommendations(1L, 10));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void calculateItemSimilarities_Success() {
        // Act & Assert - 这个方法被简化了，只验证它能正常执行
        assertDoesNotThrow(() -> recommendationService.calculateItemSimilarities());
    }

    @Test
    void getHotRecommendations_LimitTest() {
        // Arrange
        List<Long> hotItemIds = Arrays.asList(1L);
        List<Item> hotItems = Arrays.asList(testItem);
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(5)))
            .thenReturn(hotItemIds);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(hotItems);
        when(itemService.convertItemsWithFavoriteStatus(hotItems)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getHotRecommendations(5);

        // Assert
        assertNotNull(result);
        verify(jdbcTemplate).queryForList(anyString(), eq(Long.class), eq(5));
    }

    @Test
    void getContentBasedRecommendations_MultipleResults() {
        // Arrange
        ItemSimilarity similarity1 = new ItemSimilarity();
        similarity1.setItemId1(1L);
        similarity1.setItemId2(2L);
        similarity1.setScore(0.85);

        ItemSimilarity similarity2 = new ItemSimilarity();
        similarity2.setItemId1(1L);
        similarity2.setItemId2(3L);
        similarity2.setScore(0.75);

        List<ItemSimilarity> similarities = Arrays.asList(similarity1, similarity2);

        Item item2 = new Item();
        item2.setId(2L);
        item2.setName("Similar Item 2");
        item2.setStatus(Item.ItemStatus.FOR_SALE);
        item2.setIsVisible(true);

        Item item3 = new Item();
        item3.setId(3L);
        item3.setName("Similar Item 3");
        item3.setStatus(Item.ItemStatus.FOR_SALE);
        item3.setIsVisible(true);

        List<Item> items = Arrays.asList(item2, item3);
        
        ItemResponse response2 = new ItemResponse();
        response2.setId(2L);
        response2.setName("Similar Item 2");

        ItemResponse response3 = new ItemResponse();
        response3.setId(3L);
        response3.setName("Similar Item 3");

        List<ItemResponse> itemResponses = Arrays.asList(response2, response3);

        when(itemSimilarityRepository.findMostSimilarItems(1L, 10))
            .thenReturn(similarities);
        when(itemRepository.findAllById(any())).thenReturn(items);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 10);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(itemSimilarityRepository).findMostSimilarItems(1L, 10);
        verify(itemService).convertItemsWithFavoriteStatus(items);
    }

    @Test
    void getItemCFRecommendations_WithFavorites_Success() {
        // Arrange
        Long userId = 1L;
        List<Long> userFavoriteItemIds = Arrays.asList(2L, 3L, 4L);
        
        // Mock user service
        when(userService.getUserById(userId)).thenReturn(testUser);
        
        // Mock getting user favorites
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(userId), eq(5)))
            .thenReturn(userFavoriteItemIds);
        
        // Mock similarities for each favorite item
        ItemSimilarity similarity1 = new ItemSimilarity();
        similarity1.setItemId1(2L);
        similarity1.setItemId2(5L);
        similarity1.setScore(0.8);
        
        ItemSimilarity similarity2 = new ItemSimilarity();
        similarity2.setItemId1(3L);
        similarity2.setItemId2(6L);
        similarity2.setScore(0.7);
        
        when(itemSimilarityRepository.findMostSimilarItems(2L, 4))
            .thenReturn(Arrays.asList(similarity1));
        when(itemSimilarityRepository.findMostSimilarItems(3L, 4))
            .thenReturn(Arrays.asList(similarity2));
        when(itemSimilarityRepository.findMostSimilarItems(4L, 4))
            .thenReturn(Arrays.asList());
        
        // Mock recommended items
        Item recommendedItem1 = new Item();
        recommendedItem1.setId(5L);
        recommendedItem1.setName("Recommended Item 1");
        
        Item recommendedItem2 = new Item();
        recommendedItem2.setId(6L);
        recommendedItem2.setName("Recommended Item 2");
        
        List<Item> recommendedItems = Arrays.asList(recommendedItem1, recommendedItem2);
        when(itemRepository.findAllById(any())).thenReturn(recommendedItems);
        
        // Mock item service conversion
        ItemResponse response1 = new ItemResponse();
        response1.setId(5L);
        response1.setName("Recommended Item 1");
        
        ItemResponse response2 = new ItemResponse();
        response2.setId(6L);
        response2.setName("Recommended Item 2");
        
        List<ItemResponse> itemResponses = Arrays.asList(response1, response2);
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(userId, 16);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(jdbcTemplate).queryForList(anyString(), eq(Long.class), eq(userId), eq(5));
        verify(itemSimilarityRepository).findMostSimilarItems(2L, 4);
        verify(itemSimilarityRepository).findMostSimilarItems(3L, 4);
        verify(itemSimilarityRepository).findMostSimilarItems(4L, 4);
        verify(itemService).convertItemsWithFavoriteStatus(any());
    }

    @Test
    void getItemCFRecommendations_InsufficientRecommendations_FallbackToHot() {
        // Arrange
        Long userId = 1L;
        List<Long> userFavoriteItemIds = Arrays.asList(2L);
        
        // Mock user service
        when(userService.getUserById(userId)).thenReturn(testUser);
        
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(userId), eq(5)))
            .thenReturn(userFavoriteItemIds);
        
        // Mock limited similarities
        ItemSimilarity similarity1 = new ItemSimilarity();
        similarity1.setItemId1(2L);
        similarity1.setItemId2(5L);
        similarity1.setScore(0.8);
        
        when(itemSimilarityRepository.findMostSimilarItems(2L, 4))
            .thenReturn(Arrays.asList(similarity1));
        
        // Mock single recommended item
        Item recommendedItem = new Item();
        recommendedItem.setId(5L);
        recommendedItem.setName("Recommended Item");
        
        when(itemRepository.findAllById(Arrays.asList(5L)))
            .thenReturn(Arrays.asList(recommendedItem));
        
        // Mock hot items query
        List<Long> hotItemIds = Arrays.asList(1L);
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(20)))
            .thenReturn(hotItemIds);
        
        // Mock hot items for fallback
        List<Item> hotItems = Arrays.asList(testItem);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(hotItems);
        
        // Mock item service conversion
        ItemResponse response1 = new ItemResponse();
        response1.setId(5L);
        response1.setName("Recommended Item");
        
        ItemResponse response2 = new ItemResponse();
        response2.setId(1L);
        response2.setName("Hot Item");
        
        when(itemService.convertItemsWithFavoriteStatus(any()))
            .thenReturn(Arrays.asList(response1, response2));

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(userId, 10);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(jdbcTemplate).queryForList(anyString(), eq(Long.class), eq(20));
    }

    @Test
    void getContentBasedRecommendations_InsufficientAvailableItems_UsesFallback() {
        // Arrange
        List<ItemSimilarity> similarities = Arrays.asList(itemSimilarity);
        
        // Mock item that is not visible
        Item unavailableItem = new Item();
        unavailableItem.setId(2L);
        unavailableItem.setName("Unavailable Item");
        unavailableItem.setStatus(Item.ItemStatus.SOLD);
        unavailableItem.setIsVisible(false);
        
        when(itemSimilarityRepository.findMostSimilarItems(1L, 4))
            .thenReturn(similarities);
        when(itemRepository.findAllById(Arrays.asList(2L)))
            .thenReturn(Arrays.asList(unavailableItem));
        
        // Mock fallback - current item and similar items
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        
        List<Item> fallbackItems = Arrays.asList(testItem);
        when(itemRepository.findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
            eq(1), eq(Item.ItemStatus.FOR_SALE), eq(true), eq(1L), 
            any(BigDecimal.class), any(BigDecimal.class), any(Pageable.class)))
            .thenReturn(fallbackItems);
        
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 4);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemRepository).findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
            eq(1), eq(Item.ItemStatus.FOR_SALE), eq(true), eq(1L), 
            any(BigDecimal.class), any(BigDecimal.class), any(Pageable.class));
    }



    @Test
    void getContentBasedRecommendations_DefaultLimit() {
        // Arrange
        List<ItemSimilarity> similarities = Arrays.asList(itemSimilarity);
        List<Item> items = Arrays.asList(testItem);
        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);

        when(itemSimilarityRepository.findMostSimilarItems(1L, 4))
            .thenReturn(similarities);
        when(itemRepository.findAllById(any())).thenReturn(items);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act - test with limit <= 0
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 0);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemSimilarityRepository).findMostSimilarItems(1L, 4); // Should use default limit of 4
    }

    @Test
    void getItemCFRecommendations_DefaultLimit() {
        // Arrange
        when(userService.getUserById(1L)).thenReturn(testUser);
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(1L), eq(5)))
            .thenReturn(Arrays.asList());

        // Act - test with limit <= 0
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(1L, 0);

        // Assert
        assertNotNull(result);
        // Since no favorites, should return empty
        assertTrue(result.isEmpty());
    }

    @Test
    void getItemCFRecommendations_UserNotFound() {
        // Arrange
        when(userService.getUserById(999L)).thenReturn(null);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
            () -> recommendationService.getItemCFRecommendations(999L, 10));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void getItemCFRecommendations_WithFavoritesButNoSimilarities() {
        // Arrange
        Long userId = 1L;
        List<Long> userFavoriteItemIds = Arrays.asList(2L, 3L);

        // Mock user with favorites
        User userWithFavorites = new User();
        userWithFavorites.setId(userId);
        Set<Item> favoriteItems = new HashSet<>();

        Item favoriteItem1 = new Item();
        favoriteItem1.setId(2L);
        favoriteItem1.setUpdatedAt(LocalDateTime.now());
        favoriteItems.add(favoriteItem1);

        Item favoriteItem2 = new Item();
        favoriteItem2.setId(3L);
        favoriteItem2.setUpdatedAt(LocalDateTime.now().minusDays(1));
        favoriteItems.add(favoriteItem2);

        userWithFavorites.setFavoriteItems(favoriteItems);

        when(userService.getUserById(userId)).thenReturn(userWithFavorites);

        // Mock no similarities found
        when(itemSimilarityRepository.findMostSimilarItems(2L, 4))
            .thenReturn(Arrays.asList());
        when(itemSimilarityRepository.findMostSimilarItems(3L, 4))
            .thenReturn(Arrays.asList());

        // Mock hot recommendations for fallback
        List<Long> hotItemIds = Arrays.asList(1L);
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(20)))
            .thenReturn(hotItemIds);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(Arrays.asList(testItem));

        List<ItemResponse> hotResponses = Arrays.asList(itemResponse);
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(hotResponses);

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(userId, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemSimilarityRepository).findMostSimilarItems(2L, 4);
        verify(itemSimilarityRepository).findMostSimilarItems(3L, 4);
    }

    @Test
    void getContentBasedRecommendationsFallback_Success() {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        List<Item> fallbackItems = Arrays.asList(testItem);
        when(itemRepository.findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
            eq(1), eq(Item.ItemStatus.FOR_SALE), eq(true), eq(1L),
            any(BigDecimal.class), any(BigDecimal.class), any(Pageable.class)))
            .thenReturn(fallbackItems);

        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        when(itemService.convertItemsWithFavoriteStatus(fallbackItems)).thenReturn(itemResponses);

        // Act
        Object result = ReflectionTestUtils.invokeMethod(
            recommendationService, "getContentBasedRecommendationsFallback", 1L, 4);

        // Assert
        assertNotNull(result);
        assertInstanceOf(List.class, result);
        @SuppressWarnings("unchecked")
        List<ItemResponse> responses = (List<ItemResponse>) result;
        assertEquals(1, responses.size());
    }

    @Test
    void getContentBasedRecommendationsFallback_ItemNotFound() {
        // Arrange
        when(itemRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
            () -> ReflectionTestUtils.invokeMethod(
                recommendationService, "getContentBasedRecommendationsFallback", 999L, 4));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void getHotRecommendations_DatabaseException() {
        // Arrange
        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(10)))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        assertThrows(RuntimeException.class,
            () -> recommendationService.getHotRecommendations(10));
    }

    @Test
    void getContentBasedRecommendations_SortingByScore() {
        // Arrange
        ItemSimilarity similarity1 = new ItemSimilarity();
        similarity1.setItemId1(1L);
        similarity1.setItemId2(2L);
        similarity1.setScore(0.85);

        ItemSimilarity similarity2 = new ItemSimilarity();
        similarity2.setItemId1(1L);
        similarity2.setItemId2(3L);
        similarity2.setScore(0.95); // Higher score

        List<ItemSimilarity> similarities = Arrays.asList(similarity1, similarity2);

        Item item2 = new Item();
        item2.setId(2L);
        item2.setName("Similar Item 2");
        item2.setStatus(Item.ItemStatus.FOR_SALE);
        item2.setIsVisible(true);

        Item item3 = new Item();
        item3.setId(3L);
        item3.setName("Similar Item 3");
        item3.setStatus(Item.ItemStatus.FOR_SALE);
        item3.setIsVisible(true);

        List<Item> items = Arrays.asList(item2, item3);

        ItemResponse response2 = new ItemResponse();
        response2.setId(2L);
        response2.setName("Similar Item 2");

        ItemResponse response3 = new ItemResponse();
        response3.setId(3L);
        response3.setName("Similar Item 3");

        List<ItemResponse> itemResponses = Arrays.asList(response3, response2); // Should be sorted by score

        when(itemSimilarityRepository.findMostSimilarItems(1L, 10))
            .thenReturn(similarities);
        when(itemRepository.findAllById(any())).thenReturn(items);
        when(itemService.convertItemsWithFavoriteStatus(any())).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getContentBasedRecommendations(1L, 10);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // Verify that items are processed (sorting is done internally)
        verify(itemSimilarityRepository).findMostSimilarItems(1L, 10);
    }

    @Test
    void getItemCFRecommendations_FilterOutUserFavorites() {
        // Arrange
        Long userId = 1L;
        List<Long> userFavoriteItemIds = Arrays.asList(2L);

        User userWithFavorites = new User();
        userWithFavorites.setId(userId);
        Set<Item> favoriteItems = new HashSet<>();

        Item favoriteItem = new Item();
        favoriteItem.setId(2L);
        favoriteItem.setUpdatedAt(LocalDateTime.now());
        favoriteItems.add(favoriteItem);

        userWithFavorites.setFavoriteItems(favoriteItems);

        when(userService.getUserById(userId)).thenReturn(userWithFavorites);

        // Mock similarity that points back to user's favorite (should be filtered out)
        ItemSimilarity similarity1 = new ItemSimilarity();
        similarity1.setItemId1(2L);
        similarity1.setItemId2(2L); // Same as user's favorite
        similarity1.setScore(0.9);

        ItemSimilarity similarity2 = new ItemSimilarity();
        similarity2.setItemId1(2L);
        similarity2.setItemId2(5L); // Different item
        similarity2.setScore(0.8);

        when(itemSimilarityRepository.findMostSimilarItems(2L, 4))
            .thenReturn(Arrays.asList(similarity1, similarity2));

        // Mock recommended item
        Item recommendedItem = new Item();
        recommendedItem.setId(5L);
        recommendedItem.setName("Recommended Item");

        when(itemRepository.findAllById(Arrays.asList(5L)))
            .thenReturn(Arrays.asList(recommendedItem));

        ItemResponse response = new ItemResponse();
        response.setId(5L);
        response.setName("Recommended Item");

        when(itemService.convertItemsWithFavoriteStatus(any()))
            .thenReturn(Arrays.asList(response));

        // Act
        List<ItemResponse> result = recommendationService.getItemCFRecommendations(userId, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(5L, result.get(0).getId());
    }

    @Test
    void getHotRecommendations_ItemsNotFoundInRepository() {
        // Arrange
        List<Long> hotItemIds = Arrays.asList(1L, 2L, 3L);
        List<Item> foundItems = Arrays.asList(testItem); // Only one item found

        when(jdbcTemplate.queryForList(anyString(), eq(Long.class), eq(10)))
            .thenReturn(hotItemIds);
        when(itemRepository.findAllById(hotItemIds)).thenReturn(foundItems);

        List<ItemResponse> itemResponses = Arrays.asList(itemResponse);
        when(itemService.convertItemsWithFavoriteStatus(foundItems)).thenReturn(itemResponses);

        // Act
        List<ItemResponse> result = recommendationService.getHotRecommendations(10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size()); // Only found items should be returned
        verify(itemService).convertItemsWithFavoriteStatus(foundItems);
    }
}