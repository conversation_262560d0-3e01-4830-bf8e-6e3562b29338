package com.sjtu.secondhand.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.sjtu.secondhand.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OssStorageServiceImplTest {

    @Mock
    private OSS ossClient;

    @Mock
    private MultipartFile multipartFile;

    @InjectMocks
    private OssStorageServiceImpl ossStorageService;

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(ossStorageService, "bucketName", "test-bucket");
        ReflectionTestUtils.setField(ossStorageService, "bucketDomain", "test-bucket.oss-cn-shanghai.aliyuncs.com");
        ReflectionTestUtils.setField(ossStorageService, "endpoint", "oss-cn-shanghai.aliyuncs.com");
        ReflectionTestUtils.setField(ossStorageService, "accessKeyId", "test-access-key");
    }

    @Test
    void testGeneratePresignedUrl_Success() throws Exception {
        // 准备
        String filename = "test.jpg";
        String contentType = "image/jpeg";
        URL mockUrl = new URL("https://test-bucket.oss-cn-shanghai.aliyuncs.com/item_images/test_url");

        when(ossClient.generatePresignedUrl(any(GeneratePresignedUrlRequest.class)))
                .thenReturn(mockUrl);

        // 执行
        Map<String, String> result = ossStorageService.generatePresignedUrl(filename, contentType);

        // 验证
        assertNotNull(result);
        assertEquals(mockUrl.toString(), result.get("upload_url"));
        assertTrue(result.get("access_url").contains("test-bucket.oss-cn-shanghai.aliyuncs.com"));
        assertTrue(result.get("object_name").contains("item_images/"));
        assertEquals("PUT", result.get("method"));
        assertEquals(contentType, result.get("content_type"));

        verify(ossClient).generatePresignedUrl(any(GeneratePresignedUrlRequest.class));
    }

    @Test
    void testGeneratePresignedUrl_InvalidFilename() {
        // 准备
        String invalidFilename = "../test.jpg";
        String contentType = "image/jpeg";

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, () -> 
                ossStorageService.generatePresignedUrl(invalidFilename, contentType));
        
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("文件名包含无效路径序列"));
    }

    @Test
    void testGeneratePresignedUrl_Exception() {
        // 准备
        String filename = "test.jpg";
        String contentType = "image/jpeg";

        when(ossClient.generatePresignedUrl(any(GeneratePresignedUrlRequest.class)))
                .thenThrow(new RuntimeException("OSS错误"));

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, () -> 
                ossStorageService.generatePresignedUrl(filename, contentType));
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("生成预签名URL失败"));
    }

    @Test
    void testUploadFile_Success() throws IOException {
        // 准备
        String originalFilename = "test.jpg";
        String contentType = "image/jpeg";
        long fileSize = 1024L;
        byte[] fileContent = "test content".getBytes();

        when(multipartFile.getOriginalFilename()).thenReturn(originalFilename);
        when(multipartFile.getContentType()).thenReturn(contentType);
        when(multipartFile.getSize()).thenReturn(fileSize);
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(fileContent));

        // 执行
        String result = ossStorageService.uploadFile(multipartFile);

        // 验证
        assertNotNull(result);
        assertTrue(result.contains("test-bucket.oss-cn-shanghai.aliyuncs.com"));
        assertTrue(result.contains("item_images/"));

        verify(ossClient).putObject(eq("test-bucket"), anyString(), any(ByteArrayInputStream.class), any(ObjectMetadata.class));
    }

    @Test
    void testUploadFile_InvalidFilename() {
        // 准备
        String invalidFilename = "../test.jpg";
        when(multipartFile.getOriginalFilename()).thenReturn(invalidFilename);

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, () -> 
                ossStorageService.uploadFile(multipartFile));
        
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("文件名包含无效路径序列"));
    }

    @Test
    void testUploadFile_IOException() throws IOException {
        // 准备
        String originalFilename = "test.jpg";
        String contentType = "image/jpeg";
        long fileSize = 1024L;

        when(multipartFile.getOriginalFilename()).thenReturn(originalFilename);
        when(multipartFile.getContentType()).thenReturn(contentType);
        when(multipartFile.getSize()).thenReturn(fileSize);
        when(multipartFile.getInputStream()).thenThrow(new IOException("读取文件失败"));

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, () -> 
                ossStorageService.uploadFile(multipartFile));
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("上传文件到OSS失败"));
    }

    @Test
    void testDeleteFile_Success() {
        // 准备
        String objectName = "item_images/test.jpg";
        when(ossClient.doesObjectExist("test-bucket", objectName)).thenReturn(true);

        // 执行
        boolean result = ossStorageService.deleteFile(objectName);

        // 验证
        assertTrue(result);
        verify(ossClient).doesObjectExist("test-bucket", objectName);
        verify(ossClient).deleteObject("test-bucket", objectName);
    }

    @Test
    void testDeleteFile_FileNotExists() {
        // 准备
        String objectName = "item_images/nonexistent.jpg";
        when(ossClient.doesObjectExist("test-bucket", objectName)).thenReturn(false);

        // 执行
        boolean result = ossStorageService.deleteFile(objectName);

        // 验证
        assertFalse(result);
        verify(ossClient).doesObjectExist("test-bucket", objectName);
        verify(ossClient, never()).deleteObject(anyString(), anyString());
    }

    @Test
    void testDeleteFile_Exception() {
        // 准备
        String objectName = "item_images/test.jpg";
        when(ossClient.doesObjectExist("test-bucket", objectName))
                .thenThrow(new RuntimeException("OSS连接错误"));

        // 执行 & 验证
        ApiException exception = assertThrows(ApiException.class, () -> 
                ossStorageService.deleteFile(objectName));
        
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("从OSS删除文件失败"));
    }

    @Test
    void testFilenameWithoutExtension() throws Exception {
        // 准备：没有扩展名的文件
        String filename = "testfile";
        String contentType = "application/octet-stream";
        URL mockUrl = new URL("https://test-bucket.oss-cn-shanghai.aliyuncs.com/item_images/test_url");

        when(ossClient.generatePresignedUrl(any(GeneratePresignedUrlRequest.class)))
                .thenReturn(mockUrl);

        // 执行
        Map<String, String> result = ossStorageService.generatePresignedUrl(filename, contentType);

        // 验证
        assertNotNull(result);
        assertTrue(result.get("object_name").contains("item_images/"));
        verify(ossClient).generatePresignedUrl(any(GeneratePresignedUrlRequest.class));
    }

    @Test
    void testUploadFileWithoutExtension() throws IOException {
        // 准备：没有扩展名的文件
        String originalFilename = "testfile";
        String contentType = "application/octet-stream";
        long fileSize = 1024L;
        byte[] fileContent = "test content".getBytes();

        when(multipartFile.getOriginalFilename()).thenReturn(originalFilename);
        when(multipartFile.getContentType()).thenReturn(contentType);
        when(multipartFile.getSize()).thenReturn(fileSize);
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(fileContent));

        // 执行
        String result = ossStorageService.uploadFile(multipartFile);

        // 验证
        assertNotNull(result);
        assertTrue(result.contains("test-bucket.oss-cn-shanghai.aliyuncs.com"));
        assertTrue(result.contains("item_images/"));

        verify(ossClient).putObject(eq("test-bucket"), anyString(), any(ByteArrayInputStream.class), any(ObjectMetadata.class));
    }
} 