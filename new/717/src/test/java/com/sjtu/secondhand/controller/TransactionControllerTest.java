package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.TransactionCompleteRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.OfferService;
import com.sjtu.secondhand.service.OrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.lang.reflect.Method;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransactionControllerTest {

    @Mock
    private OrderService orderService;

    @Mock
    private OfferService offerService;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private TransactionController transactionController;

    @BeforeEach
    void setUp() {
        // Setup without static mocks
    }

    // Test completeTransaction - IDLE type success
    @Test
    void completeTransaction_IdleType_Success() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("IDLE");
        request.setTransaction_id(1);

        User mockUser = createMockUser();
        
        // Use reflection to call getCurrentUserId and mock its dependencies
        Method getCurrentUserIdMethod = TransactionController.class.getDeclaredMethod("getCurrentUserId");
        getCurrentUserIdMethod.setAccessible(true);
        
        // Mock the authentication manually by setting up the security context
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        // Set the security context manually 
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(200, response.getStatusCodeValue());
            assertTrue(response.getBody().isSuccess());
            assertEquals("订单已标记为完成", response.getBody().getMessage());

            verify(orderService).completeOrder(1L);
            verify(offerService, never()).completeOffer(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test completeTransaction - WANTED type success
    @Test
    void completeTransaction_WantedType_Success() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("WANTED");
        request.setTransaction_id(2);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(200, response.getStatusCodeValue());
            assertTrue(response.getBody().isSuccess());
            assertEquals("求购响应已标记为完成", response.getBody().getMessage());

            verify(offerService).completeOffer(2L);
            verify(orderService, never()).completeOrder(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test completeTransaction - Invalid transaction type
    @Test
    void completeTransaction_InvalidTransactionType() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("INVALID");
        request.setTransaction_id(1);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(400, response.getStatusCodeValue());
            assertFalse(response.getBody().isSuccess());
            assertEquals("无效的交易类型", response.getBody().getMessage());

            verify(orderService, never()).completeOrder(any());
            verify(offerService, never()).completeOffer(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test completeTransaction - User not found
    @Test
    void completeTransaction_UserNotFound() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("IDLE");
        request.setTransaction_id(1);

        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("nonexistentuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("nonexistentuser")).thenReturn(Optional.empty());
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            assertThrows(RuntimeException.class, () -> {
                transactionController.completeTransaction(request);
            });

            verify(orderService, never()).completeOrder(any());
            verify(offerService, never()).completeOffer(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test completeTransaction - OrderService throws exception  
    @Test
    void completeTransaction_OrderServiceException() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("IDLE");
        request.setTransaction_id(1);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        doThrow(new RuntimeException("Order not found")).when(orderService).completeOrder(1L);
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            assertThrows(RuntimeException.class, () -> {
                transactionController.completeTransaction(request);
            });

            verify(orderService).completeOrder(1L);
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test completeTransaction - OfferService throws exception
    @Test  
    void completeTransaction_OfferServiceException() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("WANTED");
        request.setTransaction_id(2);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        doThrow(new RuntimeException("Offer not found")).when(offerService).completeOffer(2L);
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            assertThrows(RuntimeException.class, () -> {
                transactionController.completeTransaction(request);
            });

            verify(offerService).completeOffer(2L);
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test null transaction type
    @Test
    void completeTransaction_NullTransactionType() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type(null);
        request.setTransaction_id(1);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(400, response.getStatusCodeValue());
            assertFalse(response.getBody().isSuccess());
            assertEquals("无效的交易类型", response.getBody().getMessage());

            verify(orderService, never()).completeOrder(any());
            verify(offerService, never()).completeOffer(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test empty transaction type
    @Test
    void completeTransaction_EmptyTransactionType() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("");
        request.setTransaction_id(1);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(400, response.getStatusCodeValue());
            assertFalse(response.getBody().isSuccess());
            assertEquals("无效的交易类型", response.getBody().getMessage());

            verify(orderService, never()).completeOrder(any());
            verify(offerService, never()).completeOffer(any());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test zero transaction ID
    @Test
    void completeTransaction_ZeroTransactionId() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("IDLE");
        request.setTransaction_id(0);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(200, response.getStatusCodeValue());
            assertTrue(response.getBody().isSuccess());
            assertEquals("订单已标记为完成", response.getBody().getMessage());

            verify(orderService).completeOrder(0L);
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Test negative transaction ID
    @Test
    void completeTransaction_NegativeTransactionId() throws Exception {
        TransactionCompleteRequest request = new TransactionCompleteRequest();
        request.setTransaction_type("WANTED");
        request.setTransaction_id(-1);

        User mockUser = createMockUser();
        
        Authentication mockAuth = mock(Authentication.class);
        SecurityContext mockSecurityContext = mock(SecurityContext.class);
        
        when(mockAuth.getName()).thenReturn("testuser");
        when(mockSecurityContext.getAuthentication()).thenReturn(mockAuth);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        
        SecurityContextHolder.setContext(mockSecurityContext);
        
        try {
            ResponseEntity<ApiResponse<Void>> response = transactionController.completeTransaction(request);

            assertEquals(200, response.getStatusCodeValue());
            assertTrue(response.getBody().isSuccess());
            assertEquals("求购响应已标记为完成", response.getBody().getMessage());

            verify(offerService).completeOffer(-1L);
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    // Helper method to create mock user
    private User createMockUser() {
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        return user;
    }
} 