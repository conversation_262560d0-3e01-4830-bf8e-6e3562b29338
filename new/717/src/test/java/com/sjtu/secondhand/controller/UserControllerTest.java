package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.request.UserUpdateRequest;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class UserControllerTest {

    @Mock
    private UserService userService;

    @Mock
    private ItemService itemService;

    @InjectMocks
    private UserController userController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        objectMapper = new ObjectMapper();
    }

    // Test getCurrentUser - Success
    @Test
    void getCurrentUser_Success() throws Exception {
        User mockUser = createMockUser(1L, "testuser", "<EMAIL>");

        when(userService.getCurrentUser()).thenReturn(mockUser);

        mockMvc.perform(get("/users/me"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取用户信息成功"))
                .andExpect(jsonPath("$.data.user.id").value(1L))
                .andExpect(jsonPath("$.data.user.username").value("testuser"))
                .andExpect(jsonPath("$.data.user.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.user.password").doesNotExist());

        verify(userService).getCurrentUser();
    }

    // Test getCurrentUser - Service Exception
    @Test
    void getCurrentUser_ServiceException() throws Exception {
        when(userService.getCurrentUser()).thenThrow(new RuntimeException("用户不存在"));

        mockMvc.perform(get("/users/me"))
                .andExpect(status().isInternalServerError());

        verify(userService).getCurrentUser();
    }

    // Test getUserById - Success
    @Test
    void getUserById_Success() throws Exception {
        Long userId = 1L;
        User mockUser = createMockUser(userId, "testuser", "<EMAIL>");

        when(userService.getUserById(userId)).thenReturn(mockUser);

        mockMvc.perform(get("/users/{id}", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1L))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.password").doesNotExist());

        verify(userService).getUserById(userId);
    }

    // Test getUserById - User Not Found
    @Test
    void getUserById_UserNotFound() throws Exception {
        Long userId = 999L;

        when(userService.getUserById(userId)).thenThrow(new RuntimeException("用户不存在"));

        mockMvc.perform(get("/users/{id}", userId))
                .andExpect(status().isInternalServerError());

        verify(userService).getUserById(userId);
    }

    // Test updateUser - Success
    @Test
    void updateUser_Success() throws Exception {
        User currentUser = createMockUser(1L, "oldname", "<EMAIL>");
        User updatedUser = createMockUser(1L, "newname", "<EMAIL>");
        updatedUser.setAvatarUrl("new-avatar.jpg");

        UserUpdateRequest updateRequest = new UserUpdateRequest();
        updateRequest.setUsername("newname");
        updateRequest.setAvatar_url("new-avatar.jpg");

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(userService.saveUser(any(User.class))).thenReturn(updatedUser);

        mockMvc.perform(put("/users/me")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("用户信息更新成功"))
                .andExpect(jsonPath("$.data.username").value("newname"))
                .andExpect(jsonPath("$.data.avatarUrl").value("new-avatar.jpg"))
                .andExpect(jsonPath("$.data.password").doesNotExist());

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test updateUser - Partial Update (username only)
    @Test
    void updateUser_PartialUpdate_UsernameOnly() throws Exception {
        User currentUser = createMockUser(1L, "oldname", "<EMAIL>");
        User updatedUser = createMockUser(1L, "newname", "<EMAIL>");

        UserUpdateRequest updateRequest = new UserUpdateRequest();
        updateRequest.setUsername("newname");

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(userService.saveUser(any(User.class))).thenReturn(updatedUser);

        mockMvc.perform(put("/users/me")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.username").value("newname"));

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test updateUser - Partial Update (avatar only)
    @Test
    void updateUser_PartialUpdate_AvatarOnly() throws Exception {
        User currentUser = createMockUser(1L, "testuser", "<EMAIL>");
        User updatedUser = createMockUser(1L, "testuser", "<EMAIL>");
        updatedUser.setAvatarUrl("new-avatar.jpg");

        UserUpdateRequest updateRequest = new UserUpdateRequest();
        updateRequest.setAvatar_url("new-avatar.jpg");

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(userService.saveUser(any(User.class))).thenReturn(updatedUser);

        mockMvc.perform(put("/users/me")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.avatarUrl").value("new-avatar.jpg"));

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test updateUser - Empty Update Request
    @Test
    void updateUser_EmptyRequest() throws Exception {
        User currentUser = createMockUser(1L, "testuser", "<EMAIL>");

        UserUpdateRequest updateRequest = new UserUpdateRequest();

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(userService.saveUser(any(User.class))).thenReturn(currentUser);

        mockMvc.perform(put("/users/me")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test checkIn - Success
    @Test
    void checkIn_Success() throws Exception {
        User user = createMockUser(1L, "testuser", "<EMAIL>");
        user.setPoints(50);
        user.setLastCheckInDate(LocalDate.now().minusDays(1));

        when(userService.getCurrentUser()).thenReturn(user);
        when(userService.saveUser(any(User.class))).thenReturn(user);

        mockMvc.perform(post("/users/me/check-in"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("签到成功，获得10积分"));

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test checkIn - Already Checked In Today
    @Test
    void checkIn_AlreadyCheckedIn() throws Exception {
        User user = createMockUser(1L, "testuser", "<EMAIL>");
        user.setLastCheckInDate(LocalDate.now());

        when(userService.getCurrentUser()).thenReturn(user);

        mockMvc.perform(post("/users/me/check-in"))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("今日已签到"));

        verify(userService).getCurrentUser();
        verify(userService, never()).saveUser(any(User.class));
    }

    // Test checkIn - First Time Check In (null points)
    @Test
    void checkIn_FirstTime_NullPoints() throws Exception {
        User user = createMockUser(1L, "testuser", "<EMAIL>");
        user.setPoints(null);
        user.setLastCheckInDate(null);

        when(userService.getCurrentUser()).thenReturn(user);
        when(userService.saveUser(any(User.class))).thenReturn(user);

        mockMvc.perform(post("/users/me/check-in"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("签到成功，获得10积分"));

        verify(userService).getCurrentUser();
        verify(userService).saveUser(any(User.class));
    }

    // Test getFavorites - Success
    @Test
    void getFavorites_Success() throws Exception {
        User currentUser = createMockUser(1L, "testuser", "<EMAIL>");
        List<ItemResponse> favoriteItems = Arrays.asList(
            createMockItemResponse(1L, "收藏商品1"),
            createMockItemResponse(2L, "收藏商品2")
        );

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(itemService.getFavoriteItems(currentUser.getId())).thenReturn(favoriteItems);

        mockMvc.perform(get("/users/me/favorites"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取收藏列表成功"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content.length()").value(2))
                .andExpect(jsonPath("$.data.content[0].title").value("收藏商品1"))
                .andExpect(jsonPath("$.data.content[1].title").value("收藏商品2"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1));

        verify(userService).getCurrentUser();
        verify(itemService).getFavoriteItems(currentUser.getId());
    }

    // Test getFavorites - With Custom Pagination
    @Test
    void getFavorites_CustomPagination() throws Exception {
        User currentUser = createMockUser(1L, "testuser", "<EMAIL>");
        List<ItemResponse> favoriteItems = Collections.nCopies(25, createMockItemResponse(1L, "收藏商品"));

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(itemService.getFavoriteItems(currentUser.getId())).thenReturn(favoriteItems);

        mockMvc.perform(get("/users/me/favorites")
                .param("page", "2")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content.length()").value(10))
                .andExpect(jsonPath("$.data.page").value(2))
                .andExpect(jsonPath("$.data.totalElements").value(25))
                .andExpect(jsonPath("$.data.totalPages").value(3));

        verify(userService).getCurrentUser();
        verify(itemService).getFavoriteItems(currentUser.getId());
    }

    // Test getFavorites - Empty List
    @Test
    void getFavorites_EmptyList() throws Exception {
        User currentUser = createMockUser(1L, "testuser", "<EMAIL>");

        when(userService.getCurrentUser()).thenReturn(currentUser);
        when(itemService.getFavoriteItems(currentUser.getId())).thenReturn(Collections.emptyList());

        mockMvc.perform(get("/users/me/favorites"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content.length()").value(0))
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));

        verify(userService).getCurrentUser();
        verify(itemService).getFavoriteItems(currentUser.getId());
    }

    // Test Invalid User ID in Path
    @Test
    void getUserById_InvalidId() throws Exception {
        mockMvc.perform(get("/users/{id}", "invalid"))
                .andExpect(status().isBadRequest());
    }

    // Test malformed JSON in update request
    @Test
    void updateUser_MalformedJson() throws Exception {
        mockMvc.perform(put("/users/me")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json}"))
                .andExpect(status().isBadRequest());
    }

    // Helper methods
    private User createMockUser(Long id, String username, String email) {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword("password");
        user.setPoints(100);
        return user;
    }

    private ItemResponse createMockItemResponse(Long id, String title) {
        ItemResponse response = new ItemResponse();
        response.setId(id);
        response.setTitle(title);
        response.setDescription("测试描述");
        return response;
    }
} 