package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Footprint;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.FootprintRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FootprintServiceImplTest {

    @Mock
    private FootprintRepository footprintRepository;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private FootprintServiceImpl footprintService;

    private User testUser;
    private Item testItem;
    private Category testCategory;
    private Footprint footprint;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");

        testCategory = new Category();
        testCategory.setId(1);
        testCategory.setName("Electronics");

        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("Test Item");
        testItem.setDescription("Test Description");
        testItem.setPrice(new BigDecimal("100.00"));
        testItem.setUser(testUser);
        testItem.setCategory(testCategory);
        testItem.setStatus(Item.ItemStatus.FOR_SALE);
        testItem.setIsVisible(true);

        footprint = new Footprint();
        footprint.setId(1L);
        footprint.setUser(testUser);
        footprint.setItem(testItem);
        footprint.setViewTime(LocalDateTime.now());
    }

    @Test
    void recordFootprint_NewFootprint_Success() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(footprintRepository.findByUserAndItemId(testUser, 1L)).thenReturn(Optional.empty());
        when(footprintRepository.save(any(Footprint.class))).thenReturn(footprint);

        // Act
        boolean result = footprintService.recordFootprint(1L, 1L);

        // Assert
        assertTrue(result);
        verify(footprintRepository).save(any(Footprint.class));
    }

    @Test
    void recordFootprint_ExistingFootprint_UpdateViewTime() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(footprintRepository.findByUserAndItemId(testUser, 1L)).thenReturn(Optional.of(footprint));
        when(footprintRepository.save(any(Footprint.class))).thenReturn(footprint);

        // Act
        boolean result = footprintService.recordFootprint(1L, 1L);

        // Assert
        assertTrue(result);
        verify(footprintRepository).save(footprint);
    }

    @Test
    void recordFootprint_UserNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> footprintService.recordFootprint(1L, 1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void recordFootprint_ItemNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> footprintService.recordFootprint(1L, 1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("商品不存在", exception.getMessage());
    }

    @Test
    void recordFootprint_WithException_ReturnsFalse() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(footprintRepository.findByUserAndItemId(testUser, 1L))
            .thenThrow(new RuntimeException("Database error"));

        // Act
        boolean result = footprintService.recordFootprint(1L, 1L);

        // Assert
        assertFalse(result);
    }

    @Test
    void getUserFootprints_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Footprint> footprintsPage = new PageImpl<>(Arrays.asList(footprint));
        
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(footprintRepository.findByUserOrderByViewTimeDesc(testUser, pageable))
            .thenReturn(footprintsPage);

        // Act
        ItemPageResponse result = footprintService.getUserFootprints(1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalPages());
        assertEquals(1, result.getTotalItems());
        verify(footprintRepository).findByUserOrderByViewTimeDesc(testUser, pageable);
    }

    @Test
    void getUserFootprints_UserNotFound() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> footprintService.getUserFootprints(999L, pageable));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void getUserFootprints_EmptyResult() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Footprint> emptyPage = new PageImpl<>(Arrays.asList());
        
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(footprintRepository.findByUserOrderByViewTimeDesc(testUser, pageable))
            .thenReturn(emptyPage);

        // Act
        ItemPageResponse result = footprintService.getUserFootprints(1L, pageable);

        // Assert
        assertNotNull(result);
        assertTrue(result.getItems().isEmpty());
        assertEquals(0, result.getTotalItems());
    }

    @Test
    void deleteFootprint_Success() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

        // Act
        boolean result = footprintService.deleteFootprint(1L, 1L);

        // Assert
        assertTrue(result);
        verify(footprintRepository).deleteByUserAndItemId(testUser, 1L);
    }

    @Test
    void deleteFootprint_UserNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> footprintService.deleteFootprint(1L, 1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void deleteFootprint_WithException_ReturnsFalse() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        doThrow(new RuntimeException("Database error"))
            .when(footprintRepository).deleteByUserAndItemId(testUser, 1L);

        // Act
        boolean result = footprintService.deleteFootprint(1L, 1L);

        // Assert
        assertFalse(result);
    }

    @Test
    void clearAllFootprints_Success() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

        // Act
        boolean result = footprintService.clearAllFootprints(1L);

        // Assert
        assertTrue(result);
        verify(footprintRepository).deleteAllByUser(testUser);
    }

    @Test
    void clearAllFootprints_UserNotFound() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
            () -> footprintService.clearAllFootprints(1L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("用户不存在", exception.getMessage());
    }

    @Test
    void clearAllFootprints_WithException_ReturnsFalse() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        doThrow(new RuntimeException("Database error"))
            .when(footprintRepository).deleteAllByUser(testUser);

        // Act
        boolean result = footprintService.clearAllFootprints(1L);

        // Assert
        assertFalse(result);
    }

    @Test
    void getUserFootprints_VerifyResponseStructure() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 5);
        
        LocalDateTime now = LocalDateTime.now();
        
        Footprint footprint1 = new Footprint();
        footprint1.setId(1L);
        footprint1.setUser(testUser);
        footprint1.setItem(testItem);
        footprint1.setViewTime(now);
        
        Page<Footprint> footprintsPage = new PageImpl<>(Arrays.asList(footprint1), pageable, 1);
        
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(footprintRepository.findByUserOrderByViewTimeDesc(testUser, pageable))
            .thenReturn(footprintsPage);

        // Act
        ItemPageResponse result = footprintService.getUserFootprints(1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalPages());
        assertEquals(1L, result.getTotalItems());
        assertEquals(0, result.getCurrentPage());
        
        // 验证物品响应包含查看时间
        ItemResponse itemResponse = result.getItems().get(0);
        assertNotNull(itemResponse);
        assertEquals(testItem.getId(), itemResponse.getId());
        assertEquals(now, itemResponse.getViewTime());
    }
} 