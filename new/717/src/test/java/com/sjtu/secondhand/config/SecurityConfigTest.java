package com.sjtu.secondhand.config;

import com.sjtu.secondhand.security.JwtTokenProvider;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
class SecurityConfigTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private JwtTokenProvider jwtTokenProvider; // Mock
                                               // JwtTokenProvider以避免处理真实的Token验证逻辑

    @Test
    void shouldAllowPublicAccessToAuthEndpoints() throws Exception {
        mockMvc.perform(get("/auth/login"))
                // 这里我们不关心返回什么，只关心状态码不是403 Forbidden
                // 如果是404 Not Found或405 Method Not Allowed，说明Spring
                // Security已经放行了请求
                .andExpect(status().is(405)); // 登录是POST请求，所以GET会返回405
    }

    @Test
    void shouldDenyAccessToProtectedEndpointWithoutAuth() throws Exception {
        // me controller需要认证
        mockMvc.perform(get("/users/me"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(username = "testuser", roles = { "USER" })
    void shouldAllowAccessToProtectedEndpointWithAuth() throws Exception {
        mockMvc.perform(get("/users/me"))
                .andExpect(status().isOk());
    }
} 