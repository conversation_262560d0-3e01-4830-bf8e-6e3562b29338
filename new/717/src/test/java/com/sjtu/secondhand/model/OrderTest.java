package com.sjtu.secondhand.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class OrderTest {

    private Order order;
    private Item testItem;
    private User testBuyer;
    private User testSeller;

    @BeforeEach
    void setUp() {
        order = new Order();
        
        // Create test Item
        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("Test Item");
        
        // Create test Buyer User
        testBuyer = new User();
        testBuyer.setId(1L);
        testBuyer.setUsername("buyer");
        
        // Create test Seller User
        testSeller = new User();
        testSeller.setId(2L);
        testSeller.setUsername("seller");
    }

    @Test
    void testDefaultConstructor() {
        // When
        Order newOrder = new Order();

        // Then
        assertNull(newOrder.getId());
        assertNull(newOrder.getItem());
        assertNull(newOrder.getBuyer());
        assertNull(newOrder.getSeller());
        assertNull(newOrder.getStatus());
        assertEquals(false, newOrder.getIsBuyerRated());
        assertEquals(false, newOrder.getIsSellerRated());
        assertNull(newOrder.getCreatedAt());
        assertNull(newOrder.getUpdatedAt());
    }

    @Test
    void testParameterizedConstructor() {
        // When
        Order newOrder = new Order(testItem, testBuyer, testSeller);

        // Then
        assertEquals(testItem, newOrder.getItem());
        assertEquals(testBuyer, newOrder.getBuyer());
        assertEquals(testSeller, newOrder.getSeller());
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, newOrder.getStatus());
        assertEquals(false, newOrder.getIsBuyerRated());
        assertEquals(false, newOrder.getIsSellerRated());
    }

    @Test
    void testIdGetterAndSetter() {
        // Given
        Long testId = 123L;

        // When
        order.setId(testId);

        // Then
        assertEquals(testId, order.getId());
    }

    @Test
    void testItemGetterAndSetter() {
        // When
        order.setItem(testItem);

        // Then
        assertEquals(testItem, order.getItem());
    }

    @Test
    void testBuyerGetterAndSetter() {
        // When
        order.setBuyer(testBuyer);

        // Then
        assertEquals(testBuyer, order.getBuyer());
    }

    @Test
    void testSellerGetterAndSetter() {
        // When
        order.setSeller(testSeller);

        // Then
        assertEquals(testSeller, order.getSeller());
    }

    @Test
    void testStatusGetterAndSetter() {
        // Given
        Order.OrderStatus status = Order.OrderStatus.CONFIRMED;

        // When
        order.setStatus(status);

        // Then
        assertEquals(status, order.getStatus());
    }

    @Test
    void testIsBuyerRatedGetterAndSetter() {
        // When
        order.setIsBuyerRated(true);

        // Then
        assertTrue(order.getIsBuyerRated());

        // When
        order.setIsBuyerRated(false);

        // Then
        assertFalse(order.getIsBuyerRated());
    }

    @Test
    void testIsSellerRatedGetterAndSetter() {
        // When
        order.setIsSellerRated(true);

        // Then
        assertTrue(order.getIsSellerRated());

        // When
        order.setIsSellerRated(false);

        // Then
        assertFalse(order.getIsSellerRated());
    }

    @Test
    void testCreatedAtGetterAndSetter() {
        // Given
        LocalDateTime testDate = LocalDateTime.now();

        // When
        order.setCreatedAt(testDate);

        // Then
        assertEquals(testDate, order.getCreatedAt());
    }

    @Test
    void testUpdatedAtGetterAndSetter() {
        // Given
        LocalDateTime testDate = LocalDateTime.now();

        // When
        order.setUpdatedAt(testDate);

        // Then
        assertEquals(testDate, order.getUpdatedAt());
    }

    @Test
    void testOnCreateMethod() throws Exception {
        // Given
        LocalDateTime beforeCreate = LocalDateTime.now();
        
        // When
        Method onCreateMethod = Order.class.getDeclaredMethod("onCreate");
        onCreateMethod.setAccessible(true);
        onCreateMethod.invoke(order);
        
        LocalDateTime afterCreate = LocalDateTime.now();

        // Then
        assertNotNull(order.getCreatedAt());
        assertNotNull(order.getUpdatedAt());
        assertTrue(order.getCreatedAt().isAfter(beforeCreate) || order.getCreatedAt().isEqual(beforeCreate));
        assertTrue(order.getCreatedAt().isBefore(afterCreate) || order.getCreatedAt().isEqual(afterCreate));
        // Check timestamps are within reasonable range (same second)
        assertTrue(Math.abs(order.getCreatedAt().getNano() - order.getUpdatedAt().getNano()) < 1000000); // within 1ms
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, order.getStatus());
    }

    @Test
    void testOnCreateMethodWithExistingStatus() throws Exception {
        // Given
        order.setStatus(Order.OrderStatus.CONFIRMED);
        
        // When
        Method onCreateMethod = Order.class.getDeclaredMethod("onCreate");
        onCreateMethod.setAccessible(true);
        onCreateMethod.invoke(order);

        // Then
        assertEquals(Order.OrderStatus.CONFIRMED, order.getStatus());
    }

    @Test
    void testOnUpdateMethod() throws Exception {
        // Given
        LocalDateTime originalCreatedAt = LocalDateTime.now().minusHours(1);
        order.setCreatedAt(originalCreatedAt);
        LocalDateTime beforeUpdate = LocalDateTime.now();
        
        // When
        Method onUpdateMethod = Order.class.getDeclaredMethod("onUpdate");
        onUpdateMethod.setAccessible(true);
        onUpdateMethod.invoke(order);
        
        LocalDateTime afterUpdate = LocalDateTime.now();

        // Then
        assertEquals(originalCreatedAt, order.getCreatedAt()); // createdAt should not change
        assertNotNull(order.getUpdatedAt());
        assertTrue(order.getUpdatedAt().isAfter(beforeUpdate) || order.getUpdatedAt().isEqual(beforeUpdate));
        assertTrue(order.getUpdatedAt().isBefore(afterUpdate) || order.getUpdatedAt().isEqual(afterUpdate));
    }

    @Test
    void testOrderStatusEnum() {
        // Test all enum values exist
        assertEquals(6, Order.OrderStatus.values().length);
        
        // Test specific values
        assertNotNull(Order.OrderStatus.PENDING_CONFIRMATION);
        assertNotNull(Order.OrderStatus.REJECTED);
        assertNotNull(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);
        assertNotNull(Order.OrderStatus.CONFIRMED);
        assertNotNull(Order.OrderStatus.COMPLETED);
        assertNotNull(Order.OrderStatus.CANCELLED);
        
        // Test enum name conversion
        assertEquals("PENDING_CONFIRMATION", Order.OrderStatus.PENDING_CONFIRMATION.name());
        assertEquals("REJECTED", Order.OrderStatus.REJECTED.name());
        assertEquals("AWAITING_ACKNOWLEDGEMENT", Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT.name());
        assertEquals("CONFIRMED", Order.OrderStatus.CONFIRMED.name());
        assertEquals("COMPLETED", Order.OrderStatus.COMPLETED.name());
        assertEquals("CANCELLED", Order.OrderStatus.CANCELLED.name());
    }

    @Test
    void testOrderStatusValueOf() {
        // Test valueOf functionality
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, Order.OrderStatus.valueOf("PENDING_CONFIRMATION"));
        assertEquals(Order.OrderStatus.REJECTED, Order.OrderStatus.valueOf("REJECTED"));
        assertEquals(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT, Order.OrderStatus.valueOf("AWAITING_ACKNOWLEDGEMENT"));
        assertEquals(Order.OrderStatus.CONFIRMED, Order.OrderStatus.valueOf("CONFIRMED"));
        assertEquals(Order.OrderStatus.COMPLETED, Order.OrderStatus.valueOf("COMPLETED"));
        assertEquals(Order.OrderStatus.CANCELLED, Order.OrderStatus.valueOf("CANCELLED"));
    }

    @Test
    void testDefaultBooleanValues() {
        // Given
        Order newOrder = new Order();

        // Then
        assertEquals(false, newOrder.getIsBuyerRated());
        assertEquals(false, newOrder.getIsSellerRated());
    }

    @Test
    void testNullSafety() {
        // Test that setters accept null values
        assertDoesNotThrow(() -> {
            order.setId(null);
            order.setItem(null);
            order.setBuyer(null);
            order.setSeller(null);
            order.setStatus(null);
            order.setIsBuyerRated(null);
            order.setIsSellerRated(null);
            order.setCreatedAt(null);
            order.setUpdatedAt(null);
        });

        // Verify null values are stored
        assertNull(order.getId());
        assertNull(order.getItem());
        assertNull(order.getBuyer());
        assertNull(order.getSeller());
        assertNull(order.getStatus());
        assertNull(order.getIsBuyerRated());
        assertNull(order.getIsSellerRated());
        assertNull(order.getCreatedAt());
        assertNull(order.getUpdatedAt());
    }

    @Test
    void testFullWorkflow() {
        // Given
        Order workflowOrder = new Order(testItem, testBuyer, testSeller);
        
        // Test initial state
        assertEquals(Order.OrderStatus.PENDING_CONFIRMATION, workflowOrder.getStatus());
        
        // Test status progression
        workflowOrder.setStatus(Order.OrderStatus.CONFIRMED);
        assertEquals(Order.OrderStatus.CONFIRMED, workflowOrder.getStatus());
        
        workflowOrder.setStatus(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);
        assertEquals(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT, workflowOrder.getStatus());
        
        workflowOrder.setStatus(Order.OrderStatus.COMPLETED);
        assertEquals(Order.OrderStatus.COMPLETED, workflowOrder.getStatus());
        
        // Test rating flags
        assertFalse(workflowOrder.getIsBuyerRated());
        assertFalse(workflowOrder.getIsSellerRated());
        
        workflowOrder.setIsBuyerRated(true);
        workflowOrder.setIsSellerRated(true);
        
        assertTrue(workflowOrder.getIsBuyerRated());
        assertTrue(workflowOrder.getIsSellerRated());
    }

    @Test
    void testRejectionWorkflow() {
        // Given
        Order rejectedOrder = new Order(testItem, testBuyer, testSeller);
        
        // Test rejection flow
        rejectedOrder.setStatus(Order.OrderStatus.REJECTED);
        assertEquals(Order.OrderStatus.REJECTED, rejectedOrder.getStatus());
        
        // Verify rating flags remain false for rejected orders
        assertFalse(rejectedOrder.getIsBuyerRated());
        assertFalse(rejectedOrder.getIsSellerRated());
    }

    @Test
    void testCancellationWorkflow() {
        // Given
        Order cancelledOrder = new Order(testItem, testBuyer, testSeller);
        
        // Test cancellation flow
        cancelledOrder.setStatus(Order.OrderStatus.CANCELLED);
        assertEquals(Order.OrderStatus.CANCELLED, cancelledOrder.getStatus());
        
        // Verify rating flags remain false for cancelled orders
        assertFalse(cancelledOrder.getIsBuyerRated());
        assertFalse(cancelledOrder.getIsSellerRated());
    }
} 