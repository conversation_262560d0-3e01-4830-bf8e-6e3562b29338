package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ItemServiceImplTest {

    @Mock
    private ItemRepository itemRepository;
    @Mock
    private UserService userService;
    @Mock
    private UserRepository userRepository;
    @Mock
    private CategoryRepository categoryRepository;
    @Mock
    private OrderRepository orderRepository;

    @InjectMocks
    private ItemServiceImpl itemService;

    private User user;
    private Category category;
    private Item item;
    private ItemRequest itemRequest;

    @BeforeEach
    void setUp() {
        user = new User("seller", "password", "<EMAIL>");
        user.setId(1L);

        category = new Category("电子产品");
        category.setId(1);

        item = new Item(user, category, "iPhone 13", "A great phone", new BigDecimal("5000.00"), Item.ItemCondition.LIKE_NEW);
        item.setId(1L);
        item.setViewCount(10);

        itemRequest = new ItemRequest();
        itemRequest.setName("MacBook Pro");
        itemRequest.setDescription("A powerful laptop");
        itemRequest.setPrice(new BigDecimal("10000.00"));
        itemRequest.setCategory_id(1);
        itemRequest.setCondition("BRAND_NEW");
        itemRequest.setItem_type("IDLE");
    }

    @Test
    void createItem_shouldCreateAndReturnItemResponse() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        ItemResponse response = itemService.createItem(itemRequest);

        // Assert
        assertNotNull(response);
        assertEquals("MacBook Pro", response.getName());
        assertEquals(user.getUsername(), response.getUser().get("username"));
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void updateItem_shouldUpdateAndReturnItemResponse() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setName("Updated Name");
        updateRequest.setDescription("Updated Description");

        // Act
        ItemResponse response = itemService.updateItem(1L, updateRequest);

        // Assert
        assertNotNull(response);
        assertEquals("Updated Name", item.getName());
        assertEquals("Updated Description", item.getDescription());
        verify(itemRepository).save(item);
    }

    @Test
    void updateItem_shouldThrowException_whenItemNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(999L)).thenReturn(Optional.empty());

        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setName("Updated Name");

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.updateItem(999L, updateRequest));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void updateItem_shouldThrowException_whenUserNotOwner() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setUsername("another");

        when(userService.getCurrentUser()).thenReturn(anotherUser);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setName("Updated Name");

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.updateItem(1L, updateRequest));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("您不是该物品的卖家"));
    }

    @Test
    void getItemById_shouldReturnItemAndIncrementViewCount() {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        int originalViewCount = item.getViewCount();

        // Act
        ItemResponse response = itemService.getItemById(1L);

        // Assert
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals(originalViewCount + 1, item.getViewCount());
        verify(itemRepository).save(item);
    }

    @Test
    void getItemById_shouldThrowException_whenItemNotFound() {
        // Arrange
        when(itemRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.getItemById(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void getAllItems_shouldReturnPageOfItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(itemRepository.findAll(pageable)).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.getAllItems(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).findAll(pageable);
    }

    @Test
    void deleteItem_shouldDeleteItem_whenUserIsOwner() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.findByItemId(1L)).thenReturn(Arrays.asList());
        when(userRepository.findAll()).thenReturn(Arrays.asList(user));

        // Act
        assertDoesNotThrow(() -> itemService.deleteItem(1L));

        // Assert
        verify(itemRepository).delete(item);
    }

    @Test
    void deleteItem_shouldThrowException_whenUserNotOwner() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setUsername("another");

        when(userService.getCurrentUser()).thenReturn(anotherUser);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.deleteItem(1L));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertTrue(exception.getMessage().contains("您不是该物品的卖家"));
    }

    @Test
    void deleteItem_shouldThrowException_whenItemNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(999L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.deleteItem(999L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void markItemAsSold_shouldUpdateItemStatus() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        // Act
        ItemResponse response = itemService.markItemAsSold(1L);

        // Assert
        assertNotNull(response);
        assertEquals(Item.ItemStatus.SOLD, item.getStatus());
        verify(itemRepository).save(item);
    }

    @Test
    void addToFavorites_shouldAddItemToUserFavorites() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setFavoriteItems(new HashSet<>());

        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(userService.getUserById(2L)).thenReturn(anotherUser);
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        int originalFavoriteCount = item.getFavoriteCount();

        // Act
        itemService.addToFavorites(1L, 2L);

        // Assert
        assertTrue(anotherUser.getFavoriteItems().contains(item));
        assertEquals(originalFavoriteCount + 1, item.getFavoriteCount());
        verify(itemRepository).save(item);
    }

    @Test
    void removeFromFavorites_shouldRemoveItemFromUserFavorites() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setFavoriteItems(new HashSet<>());
        anotherUser.getFavoriteItems().add(item);
        item.setFavoriteCount(1);

        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(userService.getUserById(2L)).thenReturn(anotherUser);
        when(itemRepository.save(any(Item.class))).thenReturn(item);

        // Act
        itemService.removeFromFavorites(1L, 2L);

        // Assert
        assertFalse(anotherUser.getFavoriteItems().contains(item));
        assertEquals(0, item.getFavoriteCount());
        verify(itemRepository).save(item);
    }

    @Test
    void getFavoriteItems_shouldReturnUserFavoriteItems() {
        // Arrange
        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setFavoriteItems(new HashSet<>(Arrays.asList(item)));

        when(userService.getUserById(2L)).thenReturn(anotherUser);

        // Act
        List<ItemResponse> result = itemService.getFavoriteItems(2L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void createItem_shouldThrowException_whenCategoryNotFound() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(999)).thenReturn(Optional.empty());

        itemRequest.setCategory_id(999);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.createItem(itemRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("分类不存在"));
    }

    @Test
    void createItem_shouldHandleInvalidCondition() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        itemRequest.setCondition("INVALID_CONDITION");

        // Act
        ItemResponse response = itemService.createItem(itemRequest);

        // Assert
        assertNotNull(response);
        // 应该使用默认值BRAND_NEW
    }

    @Test
    void createItem_shouldHandleInvalidItemType() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));

        itemRequest.setItem_type("INVALID_TYPE");

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.createItem(itemRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertTrue(exception.getMessage().contains("无效的物品类型"));
    }

    @Test
    void searchItems_shouldReturnSearchResults() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(categoryRepository.findByName("电子产品")).thenReturn(Optional.of(category));
        when(itemRepository.searchItems(any(), any(), any(), any())).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.searchItems("电子产品", "iPhone", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
    }

    @Test
    void searchItems_shouldThrowException_whenCategoryNotFound() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(categoryRepository.findByName("不存在的分类")).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.searchItems("不存在的分类", "keyword", pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("分类不存在", exception.getMessage());
    }



    @Test
    void getItemById_shouldReturnItemResponse_whenItemExists() {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(itemRepository.save(any(Item.class))).thenReturn(item); // 模拟保存浏览量增加后的item
        when(userService.getCurrentUser()).thenReturn(user); // 模拟一个用户，用于收藏状态判断

        // Act
        ItemResponse response = itemService.getItemById(1L);

        // Assert
        assertNotNull(response);
        assertEquals("iPhone 13", response.getName());
        assertEquals(11, item.getViewCount()); // 验证浏览量增加了
        verify(itemRepository).save(item);
    }

    @Test
    void getItemById_shouldThrowApiException_whenItemNotFound() {
        // Arrange
        when(itemRepository.findById(99L)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, () -> itemService.getItemById(99L));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("物品不存在", exception.getMessage());
    }

    @Test
    void fullTextSearch_shouldReturnPageOfItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(itemRepository.fullTextSearch("iPhone", Item.ItemStatus.FOR_SALE, pageable)).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.fullTextSearch("iPhone", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).fullTextSearch("iPhone", Item.ItemStatus.FOR_SALE, pageable);
    }

    @Test
    void advancedSearch_shouldReturnPageOfItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(categoryRepository.findByName("电子产品")).thenReturn(Optional.of(category));
        when(itemRepository.advancedSearch(
                eq("iPhone"), 
                eq(category), 
                eq(1000.0), 
                eq(10000.0),
                eq(Item.ItemCondition.LIKE_NEW),
                eq(Item.ItemStatus.FOR_SALE),
                eq(pageable)))
                .thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.advancedSearch("iPhone", "电子产品", 1000.0, 10000.0, "LIKE_NEW", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).advancedSearch(
                eq("iPhone"), 
                eq(category), 
                eq(1000.0), 
                eq(10000.0),
                eq(Item.ItemCondition.LIKE_NEW),
                eq(Item.ItemStatus.FOR_SALE),
                eq(pageable));
    }

    @Test
    void advancedSearch_shouldThrowException_whenCategoryNotFound() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(categoryRepository.findByName("不存在分类")).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.advancedSearch("keyword", "不存在分类", 1000.0, 10000.0, "LIKE_NEW", pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("分类不存在", exception.getMessage());
    }

    @Test
    void advancedSearch_shouldThrowException_whenInvalidCondition() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.advancedSearch("keyword", null, 1000.0, 10000.0, "INVALID_CONDITION", pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的物品状况", exception.getMessage());
    }

    @Test
    void getItemsByCategory_shouldReturnPageOfItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(categoryRepository.findByName("电子产品")).thenReturn(Optional.of(category));
        when(itemRepository.findByCategory(category, pageable)).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.getItemsByCategory("电子产品", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).findByCategory(category, pageable);
    }

    @Test
    void getItemsByCategory_shouldThrowException_whenCategoryNotFound() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(categoryRepository.findByName("不存在分类")).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.getItemsByCategory("不存在分类", pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("分类不存在", exception.getMessage());
    }

    @Test
    void getItemsByUser_shouldReturnPageOfItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(userService.getUserById(1L)).thenReturn(user);
        when(itemRepository.findByUser(user, pageable)).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.getItemsByUser(1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).findByUser(user, pageable);
    }

    @Test
    void getMyItems_shouldReturnCurrentUserItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findByUser(user, pageable)).thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.getMyItems(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).findByUser(user, pageable);
    }

    @Test
    void getRecommendedItems_shouldReturnRecommendedItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);

        when(categoryRepository.findById(1)).thenReturn(Optional.of(category));
        when(itemRepository.findTop5ByCategoryAndStatusOrderByCreatedAtDesc(
                category, Item.ItemStatus.FOR_SALE)).thenReturn(items);

        // Act
        List<ItemResponse> result = itemService.getRecommendedItems(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(itemRepository).findTop5ByCategoryAndStatusOrderByCreatedAtDesc(
                category, Item.ItemStatus.FOR_SALE);
    }

    @Test
    void getRecommendedItems_shouldThrowException_whenCategoryNotFound() {
        // Arrange
        when(categoryRepository.findById(999)).thenReturn(Optional.empty());

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.getRecommendedItems(999L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("分类不存在", exception.getMessage());
    }

    @Test
    void getFilteredItems_shouldReturnFilteredItems() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Page<Item> itemPage = new PageImpl<>(items);
        Pageable pageable = PageRequest.of(0, 10);

        when(itemRepository.findItemsWithFilters(
                eq(Item.ItemType.IDLE), 
                eq(1), 
                eq(1000.0), 
                eq(10000.0), 
                eq("iPhone"), 
                eq(1L), 
                eq(pageable)))
                .thenReturn(itemPage);

        // Act
        Page<Item> result = itemService.getFilteredItems(
                "IDLE", "1", 1000.0, 10000.0, "iPhone", 1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(itemRepository).findItemsWithFilters(
                eq(Item.ItemType.IDLE), 
                eq(1), 
                eq(1000.0), 
                eq(10000.0), 
                eq("iPhone"), 
                eq(1L), 
                eq(pageable));
    }

    @Test
    void getFilteredItems_shouldThrowException_whenInvalidItemType() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.getFilteredItems("INVALID_TYPE", null, null, null, null, null, pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的物品类型", exception.getMessage());
    }

    @Test
    void getFilteredItems_shouldThrowException_whenInvalidCategoryId() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class, 
                () -> itemService.getFilteredItems(null, "invalid", null, null, null, null, pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("分类ID格式无效", exception.getMessage());
    }

    @Test
    void convertItemsWithFavoriteStatus_shouldReturnItemResponsesWithFavoriteStatus() {
        // Arrange
        List<Item> items = Arrays.asList(item);
        Set<Item> favoriteItems = new HashSet<>();
        favoriteItems.add(item);
        user.setFavoriteItems(favoriteItems);

        when(userService.getCurrentUser()).thenReturn(user);

        // Act
        List<ItemResponse> result = itemService.convertItemsWithFavoriteStatus(items);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getIsFavorited());
    }

    @Test
    void convertItemsWithFavoriteStatus_shouldHandleNoCurrentUser() {
        // Arrange
        List<Item> items = Arrays.asList(item);

        when(userService.getCurrentUser()).thenThrow(new RuntimeException("User not logged in"));

        // Act
        List<ItemResponse> result = itemService.convertItemsWithFavoriteStatus(items);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertFalse(result.get(0).getIsFavorited());
    }

    @Test
    void createItem_shouldHandleNullCondition() {
        // Arrange
        itemRequest.setCondition(null);
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        ItemResponse response = itemService.createItem(itemRequest);

        // Assert
        assertNotNull(response);
        assertEquals("MacBook Pro", response.getName());
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void createItem_shouldHandleEmptyCondition() {
        // Arrange
        itemRequest.setCondition("");
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        ItemResponse response = itemService.createItem(itemRequest);

        // Assert
        assertNotNull(response);
        assertEquals("MacBook Pro", response.getName());
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void createItem_shouldHandleImageUrls() {
        // Arrange
        itemRequest.setImage_urls(Arrays.asList("http://example.com/image1.jpg", "/api/image2.jpg", "", null));
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        ItemResponse response = itemService.createItem(itemRequest);

        // Assert
        assertNotNull(response);
        assertEquals("MacBook Pro", response.getName());
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void createItem_shouldHandlePriceException() {
        // Arrange
        itemRequest.setPrice(null);
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenThrow(new NullPointerException("Cannot invoke getId() because savedItem is null"));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.createItem(itemRequest));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("保存商品失败"));
    }

    @Test
    void createItem_shouldHandleSaveException() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(categoryRepository.findById(anyInt())).thenReturn(Optional.of(category));
        when(itemRepository.save(any(Item.class))).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.createItem(itemRequest));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("保存商品失败"));
    }

    @Test
    void updateItem_shouldUpdateAllFields() {
        // Arrange
        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setName("Updated MacBook");
        updateRequest.setDescription("Updated description");
        updateRequest.setCategory_id(2);
        updateRequest.setPrice(new BigDecimal("12000.00"));
        updateRequest.setPrice_min(new BigDecimal("11000.00"));
        updateRequest.setPrice_max(new BigDecimal("13000.00"));
        updateRequest.setCondition("LIKE_NEW");
        updateRequest.setItem_type("WANTED");
        updateRequest.setImage_urls(Arrays.asList("http://example.com/new_image.jpg"));

        Category newCategory = new Category("新分类");
        newCategory.setId(2);

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(categoryRepository.findById(2)).thenReturn(Optional.of(newCategory));
        when(itemRepository.save(any(Item.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        ItemResponse response = itemService.updateItem(1L, updateRequest);

        // Assert
        assertNotNull(response);
        assertEquals("Updated MacBook", response.getName());
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void updateItem_shouldHandleInvalidCondition() {
        // Arrange
        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setCondition("INVALID_CONDITION");

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.updateItem(1L, updateRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的物品状况", exception.getMessage());
    }

    @Test
    void updateItem_shouldHandleInvalidItemType() {
        // Arrange
        ItemRequest updateRequest = new ItemRequest();
        updateRequest.setItem_type("INVALID_TYPE");

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.updateItem(1L, updateRequest));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的物品类型", exception.getMessage());
    }

    @Test
    void deleteItem_shouldHandleOrdersExist() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        List<Order> orders = Arrays.asList(order);

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.findByItemId(1L)).thenReturn(orders);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.deleteItem(1L));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("该物品已有关联订单，无法删除", exception.getMessage());
    }

    @Test
    void deleteItem_shouldHandleFavoriteItems() {
        // Arrange
        User favoriteUser = new User("favorite", "password", "<EMAIL>");
        favoriteUser.setId(2L);
        Set<Item> favoriteItems = new HashSet<>();
        favoriteItems.add(item);
        favoriteUser.setFavoriteItems(favoriteItems);

        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.findByItemId(1L)).thenReturn(Arrays.asList());
        when(userRepository.findAll()).thenReturn(Arrays.asList(user, favoriteUser));

        // Act
        itemService.deleteItem(1L);

        // Assert
        verify(userRepository).save(favoriteUser);
        verify(itemRepository).delete(item);
    }

    @Test
    void deleteItem_shouldHandleDeleteException() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(user);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(orderRepository.findByItemId(1L)).thenReturn(Arrays.asList());
        when(userRepository.findAll()).thenReturn(Arrays.asList());
        doThrow(new RuntimeException("Database error")).when(itemRepository).delete(item);

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.deleteItem(1L));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("删除物品失败"));
    }

    @Test
    void addToFavorites_shouldNotAddDuplicate() {
        // Arrange
        User favoriteUser = new User("favorite", "password", "<EMAIL>");
        favoriteUser.setId(2L);
        Set<Item> favoriteItems = new HashSet<>();
        favoriteItems.add(item);
        favoriteUser.setFavoriteItems(favoriteItems);

        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(userService.getUserById(2L)).thenReturn(favoriteUser);

        // Act
        itemService.addToFavorites(1L, 2L);

        // Assert
        verify(userService, never()).saveUser(favoriteUser);
        verify(itemRepository, never()).save(item);
    }

    @Test
    void removeFromFavorites_shouldNotRemoveIfNotFavorited() {
        // Arrange
        User favoriteUser = new User("favorite", "password", "<EMAIL>");
        favoriteUser.setId(2L);
        favoriteUser.setFavoriteItems(new HashSet<>());

        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(userService.getUserById(2L)).thenReturn(favoriteUser);

        // Act
        itemService.removeFromFavorites(1L, 2L);

        // Assert
        verify(userService, never()).saveUser(favoriteUser);
        verify(itemRepository, never()).save(item);
    }

    @Test
    void removeFromFavorites_shouldHandleZeroFavoriteCount() {
        // Arrange
        User favoriteUser = new User("favorite", "password", "<EMAIL>");
        favoriteUser.setId(2L);
        Set<Item> favoriteItems = new HashSet<>();
        favoriteItems.add(item);
        favoriteUser.setFavoriteItems(favoriteItems);
        item.setFavoriteCount(0); // 设置为0

        when(itemRepository.findById(1L)).thenReturn(Optional.of(item));
        when(userService.getUserById(2L)).thenReturn(favoriteUser);

        // Act
        itemService.removeFromFavorites(1L, 2L);

        // Assert
        verify(userService).saveUser(favoriteUser);
        verify(itemRepository, never()).save(item); // 不应该保存item，因为收藏量已经是0
    }

    @Test
    void advancedSearch_shouldHandleInvalidCondition() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(categoryRepository.findByName("电子产品")).thenReturn(Optional.of(category));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.advancedSearch("keyword", "电子产品", 100.0, 1000.0, "INVALID_CONDITION", pageable));
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("无效的物品状况", exception.getMessage());
    }

    @Test
    void getFilteredItems_shouldHandleRepositoryException() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(itemRepository.findItemsWithFilters(any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        ApiException exception = assertThrows(ApiException.class,
                () -> itemService.getFilteredItems("IDLE", "1", 100.0, 1000.0, "keyword", 1L, pageable));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        assertTrue(exception.getMessage().contains("获取物品列表失败"));
    }
}