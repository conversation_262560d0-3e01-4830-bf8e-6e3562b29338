package com.sjtu.secondhand.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

import static org.junit.jupiter.api.Assertions.*;

public class DefaultAvatarUtilTest {

    // 预期的头像URL列表（与实际代码中的列表保持一致）
    private static final String[] EXPECTED_AVATAR_URLS = {
        "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/alice.png",
        "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/lily.png",
        "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/bob.png",
        "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/kevin.png"
    };

    @Test
    public void testConstructor() {
        // 测试构造函数以提高覆盖率
        // 虽然这是工具类不应该被实例化，但为了覆盖率我们需要测试构造函数
        assertDoesNotThrow(() -> {
            DefaultAvatarUtil util = new DefaultAvatarUtil();
            assertNotNull(util);
        });
    }

    @Test
    public void testConstructorUsingReflection() throws Exception {
        // 使用反射测试构造函数，确保100%覆盖率
        Constructor<DefaultAvatarUtil> constructor = DefaultAvatarUtil.class.getDeclaredConstructor();
        assertTrue(constructor.isAccessible() || constructor.canAccess(null));
        
        assertDoesNotThrow(() -> {
            DefaultAvatarUtil instance = constructor.newInstance();
            assertNotNull(instance);
        });
    }

    @Test
    public void testGetRandomDefaultAvatarUrl() {
        // 测试随机获取的头像URL是否在预期的URL列表中
        String avatarUrl = DefaultAvatarUtil.getRandomDefaultAvatarUrl();
        assertNotNull(avatarUrl);
        assertTrue(avatarUrl.startsWith("https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/"));
        assertTrue(isValidAvatarUrl(avatarUrl));
    }

    @RepeatedTest(10)
    public void testGetRandomDefaultAvatarUrlRepeated() {
        // 重复测试随机获取头像URL，确保每次都返回有效结果
        String avatarUrl = DefaultAvatarUtil.getRandomDefaultAvatarUrl();
        assertNotNull(avatarUrl);
        assertTrue(isValidAvatarUrl(avatarUrl));
    }

    @Test
    public void testGetRandomDefaultAvatarUrlDistribution() {
        // 测试随机性 - 多次调用应该产生不同的结果（概率上）
        Set<String> uniqueUrls = new HashSet<>();
        for (int i = 0; i < 100; i++) {
            String url = DefaultAvatarUtil.getRandomDefaultAvatarUrl();
            uniqueUrls.add(url);
            assertTrue(isValidAvatarUrl(url));
        }
        
        // 100次调用中应该出现至少2种不同的头像（概率极高）
        assertTrue(uniqueUrls.size() >= 2, "Random method should produce multiple different URLs over 100 calls");
    }

    @Test
    public void testGetDefaultAvatarUrlByUsername() {
        // 测试相同用户名总是获得相同的头像URL
        String username1 = "testuser1";
        String username2 = "testuser2";
        
        String avatar1First = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username1);
        String avatar1Second = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username1);
        String avatar2 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username2);
        
        assertNotNull(avatar1First);
        assertNotNull(avatar1Second);
        assertNotNull(avatar2);
        
        // 相同用户名应该获得相同的头像
        assertEquals(avatar1First, avatar1Second);
        
        // 检查所有返回的URL是否在预期的URL列表中
        assertTrue(isValidAvatarUrl(avatar1First));
        assertTrue(isValidAvatarUrl(avatar2));
    }

    @Test
    public void testGetDefaultAvatarUrlByUsernameWithNullInput() {
        // 测试null用户名的处理
        assertThrows(NullPointerException.class, () -> {
            DefaultAvatarUtil.getDefaultAvatarUrlByUsername(null);
        });
    }

    @Test
    public void testGetDefaultAvatarUrlByUsernameWithEmptyString() {
        // 测试空字符串用户名
        String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername("");
        assertNotNull(avatar);
        assertTrue(isValidAvatarUrl(avatar));
        
        // 空字符串应该总是返回相同的结果
        String avatar2 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername("");
        assertEquals(avatar, avatar2);
    }

    @Test
    public void testGetDefaultAvatarUrlByUsernameWithSpecialCharacters() {
        // 测试包含特殊字符的用户名
        String[] specialUsernames = {
            "<EMAIL>",
            "用户123",
            "user with spaces",
            "user-with-dashes",
            "user_with_underscores",
            "123456",
            "!@#$%^&*()",
            "a",
            "VeryLongUsernameWithManyCharactersToTestHashingBehavior123456789"
        };
        
        for (String username : specialUsernames) {
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertNotNull(avatar, "Avatar should not be null for username: " + username);
            assertTrue(isValidAvatarUrl(avatar), "Avatar should be valid for username: " + username);
            
            // 确保一致性
            String avatar2 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertEquals(avatar, avatar2, "Avatar should be consistent for username: " + username);
        }
    }

    @Test
    public void testGetDefaultAvatarUrlByUsernameWithVariousHashCodes() {
        // 测试各种hashCode情况，包括可能导致Math.abs问题的情况
        // 我们测试一些已知的字符串，它们会产生不同的hashCode值
        String[] testUsernames = {
            "test_negative_hash", // 可能产生负hashCode
            "another_test", // 可能产生正hashCode
            "极端测试", // Unicode字符
            "999999999999999", // 长数字字符串
            "@#$%^&*()", // 特殊字符
            "", // 空字符串
            " ", // 空格
            "a", // 单字符
            "0" // 数字字符
        };
        
        for (String username : testUsernames) {
            assertDoesNotThrow(() -> {
                String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
                assertNotNull(avatar, "Avatar should not be null for username: " + username);
                assertTrue(isValidAvatarUrl(avatar), "Avatar should be valid for username: " + username);
                
                // 测试一致性
                String avatar2 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
                assertEquals(avatar, avatar2, "Avatar should be consistent for username: " + username);
            }, "Should handle username '" + username + "' without exception");
        }
    }

    @Test
    public void testMathAbsBoundaryConditions() {
        // 专门测试可能导致Math.abs问题的边界条件
        // 虽然我们不能直接控制hashCode，但我们可以测试大量不同的字符串
        // 以确保代码能处理各种hashCode值
        
        Set<String> allResults = new HashSet<>();
        
        // 生成各种可能产生不同hashCode的字符串
        for (int i = 0; i < 1000; i++) {
            String username = "boundary_test_" + i + "_" + Integer.toHexString(i);
            
            assertDoesNotThrow(() -> {
                String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
                assertNotNull(avatar);
                assertTrue(isValidAvatarUrl(avatar));
                allResults.add(avatar);
            }, "Should handle boundary test case " + i);
        }
        
        // 应该使用所有4种头像
        assertEquals(4, allResults.size(), "Should use all 4 avatars across 1000 test cases");
    }

    @Test
    public void testGetDefaultAvatarUrlByUsernameConsistency() {
        // 测试一致性 - 相同用户名多次调用应该返回相同结果
        String username = "consistencyTest";
        String firstResult = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
        
        for (int i = 0; i < 10; i++) {
            String result = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertEquals(firstResult, result, "Username should always return the same avatar");
        }
    }

    @Test
    public void testDistribution() {
        // 测试多个用户名的头像分配是否均匀分布
        int aliceCount = 0;
        int lilyCount = 0;
        int bobCount = 0;
        int kevinCount = 0;
        
        // 生成100个不同的用户名并统计每个头像的分配次数
        Set<String> usedAvatars = new HashSet<>();
        for (int i = 0; i < 100; i++) {
            String username = "user" + i;
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            usedAvatars.add(avatar);
            
            if (avatar.equals("https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/alice.png")) {
                aliceCount++;
            } else if (avatar.equals("https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/lily.png")) {
                lilyCount++;
            } else if (avatar.equals("https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/bob.png")) {
                bobCount++;
            } else if (avatar.equals("https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/kevin.png")) {
                kevinCount++;
            }
        }
        
        // 确保所有头像都被使用了
        assertTrue(usedAvatars.size() > 1);
        
        // 确保总数正确
        assertEquals(100, aliceCount + lilyCount + bobCount + kevinCount);
        
        System.out.println("头像分配统计：");
        System.out.println("Alice: " + aliceCount);
        System.out.println("Lily: " + lilyCount);
        System.out.println("Bob: " + bobCount);
        System.out.println("Kevin: " + kevinCount);
    }

    @Test
    public void testEnhancedDistribution() {
        // 更大规模的分布测试
        Set<String> usedAvatars = new HashSet<>();
        ConcurrentHashMap<String, Integer> avatarCounts = new ConcurrentHashMap<>();
        
        // 初始化计数器
        for (String url : EXPECTED_AVATAR_URLS) {
            avatarCounts.put(url, 0);
        }
        
        // 测试1000个不同的用户名
        for (int i = 0; i < 1000; i++) {
            String username = "testuser_" + i + "_" + System.nanoTime();
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            usedAvatars.add(avatar);
            avatarCounts.merge(avatar, 1, Integer::sum);
        }
        
        // 确保所有4个头像都被使用
        assertEquals(4, usedAvatars.size(), "All 4 avatars should be used with 1000 different usernames");
        
        // 每个头像至少被使用一次
        for (String url : EXPECTED_AVATAR_URLS) {
            assertTrue(avatarCounts.get(url) > 0, "Avatar " + url + " should be used at least once");
        }
    }

    @Test
    public void testConcurrency() throws InterruptedException {
        // 测试并发访问的安全性
        int threadCount = 10;
        int operationsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        ConcurrentHashMap<String, String> results = new ConcurrentHashMap<>();
        
        // 启动多个线程并发调用方法
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 测试随机方法
                        String randomAvatar = DefaultAvatarUtil.getRandomDefaultAvatarUrl();
                        assertNotNull(randomAvatar);
                        assertTrue(isValidAvatarUrl(randomAvatar));
                        
                        // 测试基于用户名的方法
                        String username = "thread" + threadId + "_user" + j;
                        String userAvatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
                        assertNotNull(userAvatar);
                        assertTrue(isValidAvatarUrl(userAvatar));
                        
                        // 存储结果以验证一致性
                        String previousResult = results.put(username, userAvatar);
                        if (previousResult != null) {
                            assertEquals(previousResult, userAvatar, "Results should be consistent across threads");
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        latch.await();
        executor.shutdown();
        
        // 验证结果的一致性
        for (String username : results.keySet()) {
            String storedResult = results.get(username);
            String newResult = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertEquals(storedResult, newResult, "Results should remain consistent after concurrent access");
        }
    }

    @Test
    public void testHashCodeStability() {
        // 测试相同字符串的hashCode是否稳定
        String username = "stableHashTest";
        String avatar1 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
        
        // 多次调用应该返回相同结果（基于hashCode的稳定性）
        for (int i = 0; i < 50; i++) {
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertEquals(avatar1, avatar, "Avatar should be stable across multiple calls");
        }
    }

    @Test
    public void testUnicodeUsernames() {
        // 测试Unicode字符用户名
        String[] unicodeUsernames = {
            "用户",
            "пользователь",
            "ユーザー",
            "사용자",
            "مستخدم",
            "🚀💫⭐",
            "Åse_Øst_Ålund"
        };
        
        for (String username : unicodeUsernames) {
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertNotNull(avatar, "Avatar should not be null for Unicode username: " + username);
            assertTrue(isValidAvatarUrl(avatar), "Avatar should be valid for Unicode username: " + username);
            
            // 确保一致性
            String avatar2 = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertEquals(avatar, avatar2, "Avatar should be consistent for Unicode username: " + username);
        }
    }

    @Test
    public void testLargeScaleUniqueness() {
        // 测试大规模用户名的唯一性处理
        Set<String> avatars = new HashSet<>();
        
        // 生成10000个不同的用户名
        for (int i = 0; i < 10000; i++) {
            String username = "user_" + i + "_" + Long.toHexString(System.nanoTime());
            String avatar = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(username);
            assertTrue(isValidAvatarUrl(avatar));
            avatars.add(avatar);
        }
        
        // 应该只有4种不同的头像
        assertEquals(4, avatars.size(), "Should only have 4 different avatar URLs");
    }



    /**
     * 辅助方法：检查头像URL是否有效
     */
    private boolean isValidAvatarUrl(String url) {
        if (url == null) return false;
        
        for (String expectedUrl : EXPECTED_AVATAR_URLS) {
            if (expectedUrl.equals(url)) {
                return true;
            }
        }
        return false;
    }
} 