package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.response.ItemResponse;

import com.sjtu.secondhand.service.RecommendationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class RecommendationControllerTest {

    @Mock
    private RecommendationService recommendationService;

    @InjectMocks
    private RecommendationController recommendationController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(recommendationController).build();
        objectMapper = new ObjectMapper();
    }

    // Test getHotRecommendations - Success with default limit
    @Test
    void getHotRecommendations_Success() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门商品1", new BigDecimal("100.00")),
            createMockItemResponse(2L, "热门商品2", new BigDecimal("200.00"))
        );

        when(recommendationService.getHotRecommendations(16))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].title").value("热门商品1"))
                .andExpect(jsonPath("$.data[1].title").value("热门商品2"));

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getHotRecommendations - Custom limit
    @Test
    void getHotRecommendations_CustomLimit() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "热门商品", new BigDecimal("100.00"))
        );

        when(recommendationService.getHotRecommendations(10))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getHotRecommendations(10);
    }

    // Test getHotRecommendations - Empty results
    @Test
    void getHotRecommendations_EmptyResults() throws Exception {
        when(recommendationService.getHotRecommendations(16))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getHotRecommendations(16);
    }

    // Test getHotRecommendations - Large limit
    @Test
    void getHotRecommendations_LargeLimit() throws Exception {
        when(recommendationService.getHotRecommendations(1000))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "1000"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(1000);
    }



    // Test getSimilarItems - Success with default limit
    @Test
    void getSimilarItems_Success() throws Exception {
        Long itemId = 1L;
        List<ItemResponse> mockSimilarItems = Arrays.asList(
            createMockItemResponse(2L, "相似商品1", new BigDecimal("90.00")),
            createMockItemResponse(3L, "相似商品2", new BigDecimal("110.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(mockSimilarItems);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].title").value("相似商品1"))
                .andExpect(jsonPath("$.data[1].title").value("相似商品2"));

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test getSimilarItems - Custom limit
    @Test
    void getSimilarItems_CustomLimit() throws Exception {
        Long itemId = 1L;
        List<ItemResponse> mockSimilarItems = Arrays.asList(
            createMockItemResponse(2L, "相似商品", new BigDecimal("90.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId, 8))
            .thenReturn(mockSimilarItems);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId)
                .param("limit", "8"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getContentBasedRecommendations(itemId, 8);
    }

    // Test getSimilarItems - Empty results
    @Test
    void getSimilarItems_EmptyResults() throws Exception {
        Long itemId = 1L;

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }





    // Test triggerSimilarityCalculation - Success (skip auth check in unit test)
    @Test
    void triggerSimilarityCalculation_Success() throws Exception {
        doNothing().when(recommendationService).calculateItemSimilarities();

        mockMvc.perform(post("/recommendations/admin/recalculate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"));

        verify(recommendationService).calculateItemSimilarities();
    }

    // Test boundary conditions
    @Test
    void getHotRecommendations_ZeroLimit() throws Exception {
        when(recommendationService.getHotRecommendations(0))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "0"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(0);
    }

    @Test
    void getHotRecommendations_NegativeLimit() throws Exception {
        when(recommendationService.getHotRecommendations(-1))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/hot")
                .param("limit", "-1"))
                .andExpect(status().isOk());

        verify(recommendationService).getHotRecommendations(-1);
    }

    @Test
    void getSimilarItems_InvalidItemId() throws Exception {
        mockMvc.perform(get("/recommendations/similar/{itemId}", "invalid"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getSimilarItems_NegativeItemId() throws Exception {
        Long itemId = -1L;

        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk());

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    // Test multiple similar calls
    @Test
    void getSimilarItems_MultipleCalls() throws Exception {
        Long itemId1 = 1L;
        Long itemId2 = 2L;

        List<ItemResponse> similarItems1 = Arrays.asList(
            createMockItemResponse(3L, "相似商品1", new BigDecimal("100.00"))
        );
        List<ItemResponse> similarItems2 = Arrays.asList(
            createMockItemResponse(4L, "相似商品2", new BigDecimal("200.00"))
        );

        when(recommendationService.getContentBasedRecommendations(itemId1, 4))
            .thenReturn(similarItems1);
        when(recommendationService.getContentBasedRecommendations(itemId2, 4))
            .thenReturn(similarItems2);

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId1))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].title").value("相似商品1"));

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId2))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].title").value("相似商品2"));

        verify(recommendationService).getContentBasedRecommendations(itemId1, 4);
        verify(recommendationService).getContentBasedRecommendations(itemId2, 4);
    }

    private ItemResponse createMockItemResponse(Long id, String title, BigDecimal price) {
        ItemResponse response = new ItemResponse();
        response.setId(id);
        response.setTitle(title);
        response.setPrice(price);
        response.setDescription("测试描述");
        return response;
    }

    // 新增测试：测试getForYouRecommendations方法
    @Test
    void getForYouRecommendations_WithAuthenticatedUser() throws Exception {
        // 这个测试需要模拟认证用户，但由于MockMvc的限制，我们测试未认证的情况
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "推荐商品1", new BigDecimal("100.00")),
            createMockItemResponse(2L, "推荐商品2", new BigDecimal("200.00"))
        );

        when(recommendationService.getHotRecommendations(16))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(recommendationService).getHotRecommendations(16);
    }

    @Test
    void getForYouRecommendations_WithCustomLimit() throws Exception {
        List<ItemResponse> mockRecommendations = Arrays.asList(
            createMockItemResponse(1L, "推荐商品", new BigDecimal("100.00"))
        );

        when(recommendationService.getHotRecommendations(8))
            .thenReturn(mockRecommendations);

        mockMvc.perform(get("/recommendations/for-you")
                .param("limit", "8"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(recommendationService).getHotRecommendations(8);
    }

    @Test
    void getForYouRecommendations_EmptyResults() throws Exception {
        when(recommendationService.getHotRecommendations(16))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/for-you"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getHotRecommendations(16);
    }

    // 测试服务异常情况
    @Test
    void getHotRecommendations_ServiceThrowsException() throws Exception {
        when(recommendationService.getHotRecommendations(16))
            .thenThrow(new RuntimeException("Service error"));

        mockMvc.perform(get("/recommendations/hot"))
                .andExpect(status().isInternalServerError());

        verify(recommendationService).getHotRecommendations(16);
    }

    @Test
    void getSimilarItems_ServiceThrowsException() throws Exception {
        Long itemId = 1L;
        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenThrow(new RuntimeException("Service error"));

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isInternalServerError());

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    @Test
    void triggerSimilarityCalculation_ServiceThrowsException() throws Exception {
        doThrow(new RuntimeException("Calculation error"))
            .when(recommendationService).calculateItemSimilarities();

        mockMvc.perform(post("/recommendations/admin/recalculate"))
                .andExpect(status().isInternalServerError());

        verify(recommendationService).calculateItemSimilarities();
    }

    // 测试构造函数
    @Test
    void testConstructor() {
        RecommendationController controller = new RecommendationController(recommendationService);
        assertNotNull(controller);
    }

    // 测试边界值
    @Test
    void getSimilarItems_ZeroLimit() throws Exception {
        Long itemId = 1L;
        when(recommendationService.getContentBasedRecommendations(itemId, 0))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId)
                .param("limit", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));

        verify(recommendationService).getContentBasedRecommendations(itemId, 0);
    }

    @Test
    void getSimilarItems_NegativeLimit() throws Exception {
        Long itemId = 1L;
        when(recommendationService.getContentBasedRecommendations(itemId, -1))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId)
                .param("limit", "-1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(recommendationService).getContentBasedRecommendations(itemId, -1);
    }

    @Test
    void getForYouRecommendations_ZeroLimit() throws Exception {
        when(recommendationService.getHotRecommendations(0))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/for-you")
                .param("limit", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(recommendationService).getHotRecommendations(0);
    }

    @Test
    void getForYouRecommendations_NegativeLimit() throws Exception {
        when(recommendationService.getHotRecommendations(-5))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/for-you")
                .param("limit", "-5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(recommendationService).getHotRecommendations(-5);
    }

    // 测试大数值
    @Test
    void getSimilarItems_LargeItemId() throws Exception {
        Long itemId = Long.MAX_VALUE;
        when(recommendationService.getContentBasedRecommendations(itemId, 4))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(recommendationService).getContentBasedRecommendations(itemId, 4);
    }

    @Test
    void getSimilarItems_LargeLimit() throws Exception {
        Long itemId = 1L;
        when(recommendationService.getContentBasedRecommendations(itemId, Integer.MAX_VALUE))
            .thenReturn(Collections.emptyList());

        mockMvc.perform(get("/recommendations/similar/{itemId}", itemId)
                .param("limit", String.valueOf(Integer.MAX_VALUE)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(recommendationService).getContentBasedRecommendations(itemId, Integer.MAX_VALUE);
    }

    private void assertNotNull(RecommendationController controller) {
        if (controller == null) {
            throw new AssertionError("Controller should not be null");
        }
    }
}