package com.sjtu.secondhand.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.models.OpenAPI;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ConfigTest {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void openApiBeanShouldBeConfigured() {
        // Arrange & Act
        OpenAPI openAPI = context.getBean(OpenAPI.class);

        // Assert
        assertNotNull(openAPI);
        assertEquals("高校二手交易平台 API", openAPI.getInfo().getTitle());
    }

    @Test
    void jacksonConfigShouldFormatLocalDateTime() throws JsonProcessingException {
        // Arrange
        LocalDateTime now = LocalDateTime.of(2024, 1, 1, 12, 30, 0);
        TestDto dto = new TestDto(now);

        // Act
        String json = objectMapper.writeValueAsString(dto);

        // Assert
        assertTrue(json.contains("\"testDate\":\"2024-01-01 12:30:00\""));
    }

    // Helper class for testing Jackson serialization
    private static class TestDto {
        private final LocalDateTime testDate;

        public TestDto(LocalDateTime testDate) {
            this.testDate = testDate;
        }

        public LocalDateTime getTestDate() {
            return testDate;
        }
    }
} 