package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.model.User;

import java.time.LocalDateTime;
import java.util.List;

public class CommentResponse {
    private Long id;
    
    @JsonProperty("user")
    private UserResponse userResponse;
    
    private String content;
    
    @JsonProperty("parent_id")
    private Long parentId;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    private List<CommentResponse> replies;

    public CommentResponse() {
    }

    public CommentResponse(Comment comment) {
        this.id = comment.getId();
        this.content = comment.getContent();
        this.parentId = comment.getParent() != null ? comment.getParent().getId() : null;
        this.createdAt = comment.getCreatedAt();
        
        if (comment.getUser() != null) {
            User user = comment.getUser();
            this.userResponse = new UserResponse(user);
        }
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public UserResponse getUserResponse() {
        return userResponse;
    }

    public void setUserResponse(UserResponse userResponse) {
        this.userResponse = userResponse;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public List<CommentResponse> getReplies() {
        return replies;
    }

    public void setReplies(List<CommentResponse> replies) {
        this.replies = replies;
    }
} 