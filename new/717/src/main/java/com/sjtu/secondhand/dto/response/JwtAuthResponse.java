package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class JwtAuthResponse {
    @JsonProperty("token")
    private String token;
    
    @JsonProperty("token_type")
    private String tokenType = "Bearer";
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("avatar_url")
    private String avatar;

    public JwtAuthResponse() {
    }

    public JwtAuthResponse(String token, Long id, String username) {
        this.token = token;
        this.id = id;
        this.username = username;
    }

    public JwtAuthResponse(String token, String tokenType, Long id, String username) {
        this.token = token;
        this.tokenType = tokenType;
        this.id = id;
        this.username = username;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}