package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

@RestController
@RequestMapping("/es")
@Tag(name = "全文搜索 (Elasticsearch)", description = "基于Elasticsearch的智能搜索接口")
public class ElasticsearchController {

    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchController.class);
    private final ElasticsearchSyncService elasticsearchSyncService;

    @Autowired
    public ElasticsearchController(ElasticsearchSyncService elasticsearchSyncService) {
        this.elasticsearchSyncService = elasticsearchSyncService;
    }

    @PostMapping("/sync")
    @Operation(summary = "同步所有物品到ES", description = "将数据库中的所有物品数据同步到Elasticsearch")
    public ResponseEntity<ApiResponse<String>> syncAllItems() {
        try {
            elasticsearchSyncService.syncAllItemsToElasticsearch();
            return ResponseEntity.ok(ApiResponse.success("同步所有物品到Elasticsearch成功"));
        } catch (Exception e) {
            logger.error("同步物品到ES失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_SYNC_ERROR", "同步物品到ES失败: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    @Operation(summary = "通过关键词搜索物品", description = "从ES中搜索物品，支持分页")
    public ResponseEntity<ApiResponse<Object>> search(
            @RequestParam("q") String keyword,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size
    ) {
        try {
            Object result = elasticsearchSyncService.searchItemsByKeyword(keyword, page, size);
            
            // 检查是否有回退标记
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                if ("elasticsearch_fallback".equals(resultMap.get("source"))) {
                    logger.info("检测到ES搜索回退标记");
                    return ResponseEntity.ok(ApiResponse.success("由于Elasticsearch兼容性问题，已回退到MySQL搜索", result));
                }
                if (resultMap.containsKey("error")) {
                    logger.warn("ES搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_SEARCH_ERROR", resultMap.get("error").toString(), result));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            logger.error("ES搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_SEARCH_ERROR", "搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/search/advanced")
    @Operation(summary = "高级搜索物品", description = "从ES中高级搜索物品，支持多种筛选条件和排序")
    public ResponseEntity<ApiResponse<Object>> advancedSearch(
            @RequestParam(value = "q", required = false) String keyword,
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "price_min", required = false) Double priceMin,
            @RequestParam(value = "price_max", required = false) Double priceMax,
            @RequestParam(value = "item_type", required = false) String itemType,
            @RequestParam(value = "condition", required = false) String condition,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "sort", required = false) String sort
    ) {
        try {
            Object result = elasticsearchSyncService.advancedSearch(keyword, categoryId, priceMin, priceMax, 
                                                                   itemType, condition, page, size, sort);
            
            // 检查是否有回退标记
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                if ("elasticsearch_fallback".equals(resultMap.get("source"))) {
                    logger.info("检测到ES搜索回退标记");
                    return ResponseEntity.ok(ApiResponse.success("由于Elasticsearch兼容性问题，已回退到MySQL搜索", result));
                }
                if (resultMap.containsKey("error")) {
                    logger.warn("ES高级搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_SEARCH_ERROR", resultMap.get("error").toString(), result));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            logger.error("ES高级搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_ADVANCED_SEARCH_ERROR", "高级搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/direct/search")
    @Operation(summary = "直接通过ES REST API搜索", description = "直接向Elasticsearch服务器发送REST请求进行搜索")
    public ResponseEntity<ApiResponse<Object>> directSearch(
            @RequestParam("q") String keyword,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size
    ) {
        try {
            logger.info("直接ES搜索API被调用 - 关键词: {}, 页码: {}, 大小: {}", keyword, page, size);
            Object result = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);
            
            logger.info("直接ES搜索完成，结果类型: {}", result != null ? result.getClass().getName() : "null");
            
            // 检查是否有错误信息
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                
                if (resultMap.containsKey("source")) {
                    String source = (String) resultMap.get("source");
                    logger.info("结果source标记: {}", source);
                }
                
                if (resultMap.containsKey("error")) {
                    logger.warn("直接ES搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_DIRECT_SEARCH_ERROR", resultMap.get("error").toString(), result));
                }
                
                // 将结果转换为前端期望的格式
                Map<String, Object> formattedResponse = formatSearchResponse(resultMap);
                logger.info("直接ES搜索成功返回");
                return ResponseEntity.ok(ApiResponse.success(formattedResponse));
            }
            
            logger.info("直接ES搜索成功返回");
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            logger.error("直接ES搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_DIRECT_SEARCH_ERROR", "搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/direct/search/advanced")
    @Operation(summary = "直接通过ES REST API高级搜索", description = "直接向Elasticsearch服务器发送REST请求进行高级搜索")
    public ResponseEntity<ApiResponse<Object>> directAdvancedSearch(
            @RequestParam(value = "q", required = false) String keyword,
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "price_min", required = false) Double priceMin,
            @RequestParam(value = "price_max", required = false) Double priceMax,
            @RequestParam(value = "item_type", required = false) String itemType,
            @RequestParam(value = "condition", required = false) String condition,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "sort", required = false) String sort
    ) {
        try {
            logger.info("直接ES高级搜索API被调用 - 关键词: {}, 分类: {}, 页码: {}, 大小: {}, 排序: {}", 
                       keyword, categoryId, page, size, sort);
            
            Object result = elasticsearchSyncService.directAdvancedSearch(keyword, categoryId, priceMin, priceMax, 
                                                                       itemType, condition, page, size, sort);
            
            logger.info("直接ES高级搜索完成，结果类型: {}", result != null ? result.getClass().getName() : "null");
            
            // 检查是否有错误信息
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                
                if (resultMap.containsKey("source")) {
                    String source = (String) resultMap.get("source");
                    logger.info("结果source标记: {}", source);
                }
                
                if (resultMap.containsKey("error")) {
                    logger.warn("直接ES高级搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_DIRECT_ADVANCED_SEARCH_ERROR", resultMap.get("error").toString(), result));
                }
                
                // 将结果转换为前端期望的格式
                Map<String, Object> formattedResponse = formatSearchResponse(resultMap);
                logger.info("直接ES高级搜索成功返回");
                return ResponseEntity.ok(ApiResponse.success(formattedResponse));
            }
            
            logger.info("直接ES高级搜索成功返回");
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            logger.error("直接ES高级搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_DIRECT_ADVANCED_SEARCH_ERROR", "高级搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/test/direct")
    @Operation(summary = "测试直接ES搜索", description = "简化的直接ES搜索测试端点")
    public ResponseEntity<ApiResponse<Object>> testDirectSearch(
            @RequestParam(value = "q", defaultValue = "手机") String keyword
    ) {
        try {
            logger.info("测试直接ES搜索API被调用 - 关键词: {}", keyword);
            
            // 固定参数以简化测试
            int page = 0;
            int size = 10;
            
            Object result = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);
            logger.info("测试直接ES搜索完成，结果类型: {}", result != null ? result.getClass().getName() : "null");
            
            // 检查是否有错误信息
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                
                if (resultMap.containsKey("source")) {
                    String source = (String) resultMap.get("source");
                    logger.info("结果source标记: {}", source);
                }
                
                if (resultMap.containsKey("error")) {
                    logger.warn("测试直接ES搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_TEST_ERROR", resultMap.get("error").toString(), result));
                }
                
                // 检查items是否有内容
                List<?> items = (List<?>) resultMap.get("items");
                logger.info("搜索结果物品数量: {}", items != null ? items.size() : 0);
                
                // 打印每个物品的关键字段，用于调试
                if (items != null) {
                    for (int i = 0; i < items.size() && i < 3; i++) { // 只打印前3个结果
                        Map<String, Object> item = (Map<String, Object>) items.get(i);
                        logger.info("物品 #{}: id={}, name={}", i+1, item.get("id"), item.get("name"));
                        
                        // 详细检查图片相关字段
                        logger.info("  - imageUrls字段: {}", item.get("imageUrls") != null ? "存在" : "不存在");
                        if (item.containsKey("imageUrls")) {
                            List<String> urls = (List<String>) item.get("imageUrls");
                            logger.info("    - imageUrls内容: {}", urls != null ? String.join(", ", urls) : "null");
                        }
                        
                        logger.info("  - images字段: {}", item.get("images") != null ? "存在" : "不存在");
                        if (item.containsKey("images")) {
                            List<Map<String, Object>> images = (List<Map<String, Object>>) item.get("images");
                            if (images != null && !images.isEmpty()) {
                                logger.info("    - images数量: {}", images.size());
                                logger.info("    - 第一张图URL: {}", ((Map<String, Object>)images.get(0)).get("url"));
                                logger.info("    - 第一张图isMain: {}", ((Map<String, Object>)images.get(0)).get("isMain"));
                            } else {
                                logger.info("    - images为空列表");
                            }
                        }
                        
                        logger.info("  - mainImage字段: {}", item.get("mainImage"));
                        
                        // 检查用户信息
                        logger.info("  - user字段: {}", item.get("user") != null ? "存在" : "不存在");
                        if (item.containsKey("user")) {
                            Map<String, Object> user = (Map<String, Object>) item.get("user");
                            if (user != null) {
                                logger.info("    - 用户信息: id={}, name={}, 头像: {}", 
                                          user.get("id"), user.get("username"), user.get("avatarUrl"));
                            }
                        }
                        
                        logger.info("  - seller字段: {}", item.get("seller") != null ? "存在" : "不存在");
                    }
                }
            }
            
            logger.info("测试直接ES搜索成功返回");
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            logger.error("测试直接ES搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_TEST_ERROR", "搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/test/format")
    @Operation(summary = "测试格式化后的ES搜索结果", description = "测试格式化后的ES搜索结果是否与普通搜索一致")
    public ResponseEntity<ApiResponse<Object>> testFormattedSearch(
            @RequestParam(value = "q", defaultValue = "手机") String keyword
    ) {
        try {
            logger.info("测试格式化后的ES搜索API被调用 - 关键词: {}", keyword);
            
            // 固定参数以简化测试
            int page = 0;
            int size = 10;
            
            // 获取ES搜索结果
            Object esResult = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);
            
            // 检查是否有错误信息
            if (esResult instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) esResult;
                
                if (resultMap.containsKey("error")) {
                    logger.warn("ES搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_TEST_ERROR", resultMap.get("error").toString(), resultMap));
                }
                
                // 将结果转换为前端期望的格式
                Map<String, Object> formattedResponse = formatSearchResponse(resultMap);
                
                // 检查第一个物品的关键字段
                List<Map<String, Object>> items = (List<Map<String, Object>>) formattedResponse.get("items");
                if (items != null && !items.isEmpty()) {
                    Map<String, Object> firstItem = items.get(0);
                    logger.info("格式化后第一个物品: id={}, name={}, title={}", 
                              firstItem.get("id"), firstItem.get("name"), firstItem.get("title"));
                    
                    // 检查images字段
                    Object images = firstItem.get("images");
                    if (images instanceof List) {
                        List<?> imagesList = (List<?>) images;
                        if (!imagesList.isEmpty()) {
                            Object firstImage = imagesList.get(0);
                            logger.info("images字段类型: {}, 第一个元素类型: {}", 
                                      images.getClass().getName(),
                                      firstImage != null ? firstImage.getClass().getName() : "null");
                            logger.info("images第一个元素: {}", firstImage);
                        }
                    }
                    
                    // 检查用户字段
                    Map<String, Object> user = (Map<String, Object>) firstItem.get("user");
                    if (user != null) {
                        logger.info("用户信息: id={}, username={}, avatar={}, avatarUrl={}", 
                                  user.get("id"), user.get("username"), user.get("avatar"), user.get("avatarUrl"));
                    }
                }
                
                logger.info("格式化后的ES搜索成功返回");
                return ResponseEntity.ok(ApiResponse.success("格式化后的ES搜索结果", formattedResponse));
            }
            
            return ResponseEntity.ok(ApiResponse.success("ES搜索结果", esResult));
        } catch (Exception e) {
            logger.error("测试格式化后的ES搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_TEST_ERROR", "搜索过程中出错: " + e.getMessage()));
        }
    }

    @GetMapping("/test/format2")
    @Operation(summary = "测试修改后的ES搜索结果格式", description = "验证修改后的ES搜索结果格式是否与普通搜索完全一致")
    public ResponseEntity<ApiResponse<Object>> testFormattedSearch2(
            @RequestParam(value = "q", defaultValue = "相机") String keyword
    ) {
        try {
            logger.info("测试修改后的ES搜索API被调用 - 关键词: {}", keyword);
            
            // 固定参数以简化测试
            int page = 0;
            int size = 10;
            
            // 获取ES搜索结果
            Object esResult = elasticsearchSyncService.directSearchByKeyword(keyword, page, size);
            
            // 检查是否有错误信息
            if (esResult instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) esResult;
                
                if (resultMap.containsKey("error")) {
                    logger.warn("ES搜索返回错误: {}", resultMap.get("error"));
                    return ResponseEntity.ok(ApiResponse.error("ES_TEST_ERROR", resultMap.get("error").toString(), resultMap));
                }
                
                // 将结果转换为前端期望的格式
                Map<String, Object> formattedResponse = formatSearchResponse(resultMap);
                
                // 检查第一个物品的关键字段
                List<Map<String, Object>> items = (List<Map<String, Object>>) formattedResponse.get("items");
                if (items != null && !items.isEmpty()) {
                    Map<String, Object> firstItem = items.get(0);
                    logger.info("格式化后第一个物品: id={}, name={}, title={}", 
                              firstItem.get("id"), firstItem.get("name"), firstItem.get("title"));
                    
                    // 检查images字段
                    Object images = firstItem.get("images");
                    if (images instanceof List) {
                        List<?> imagesList = (List<?>) images;
                        if (!imagesList.isEmpty()) {
                            Object firstImage = imagesList.get(0);
                            logger.info("images字段类型: {}, 第一个元素类型: {}", 
                                      images.getClass().getName(),
                                      firstImage != null ? firstImage.getClass().getName() : "null");
                            logger.info("images第一个元素: {}", firstImage);
                        }
                    }
                    
                    // 检查用户字段
                    Map<String, Object> user = (Map<String, Object>) firstItem.get("user");
                    if (user != null) {
                        logger.info("用户信息: id={}, username={}, avatar={}, avatarUrl={}", 
                                  user.get("id"), user.get("username"), user.get("avatar"), user.get("avatarUrl"));
                    }
                    
                    // 检查是否有ES特有字段
                    logger.info("检查ES特有字段: _class={}, isVisible={}, nameHighlight={}, score={}, mainImage={}", 
                              firstItem.containsKey("_class"),
                              firstItem.containsKey("isVisible"),
                              firstItem.containsKey("nameHighlight"),
                              firstItem.containsKey("score"),
                              firstItem.containsKey("mainImage"));
                    
                    // 检查所有字段
                    logger.info("物品所有字段: {}", firstItem.keySet());
                }
                
                logger.info("格式化后的ES搜索成功返回");
                return ResponseEntity.ok(ApiResponse.success("格式化后的ES搜索结果", formattedResponse));
            }
            
            return ResponseEntity.ok(ApiResponse.success("ES搜索结果", esResult));
        } catch (Exception e) {
            logger.error("测试格式化后的ES搜索错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("ES_TEST_ERROR", "搜索过程中出错: " + e.getMessage()));
        }
    }

    /**
     * 将ES搜索结果格式化为前端期望的格式
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> formatSearchResponse(Map<String, Object> esResult) {
        Map<String, Object> formattedResponse = new HashMap<>();
        
        // 提取items
        List<Map<String, Object>> items = (List<Map<String, Object>>) esResult.get("items");
        formattedResponse.put("items", items != null ? items : new ArrayList<>());
        
        // 构建分页信息
        int total = ((Number) esResult.getOrDefault("total", 0)).intValue();
        int page = ((Number) esResult.getOrDefault("page", 0)).intValue();
        int size = ((Number) esResult.getOrDefault("size", 10)).intValue();
        int totalPages = size > 0 ? (int) Math.ceil((double) total / size) : 0;
        
        // 与普通搜索保持一致的字段名
        formattedResponse.put("currentPage", page);
        formattedResponse.put("size", size);
        formattedResponse.put("totalItems", total);
        formattedResponse.put("totalPages", totalPages);
        formattedResponse.put("content", items); // 普通搜索有content字段
        formattedResponse.put("page", page); // 普通搜索同时有page和currentPage
        formattedResponse.put("totalElements", total); // 普通搜索同时有totalItems和totalElements
        
        // 普通搜索中source为null
        formattedResponse.put("source", null);
        
        // 移除ES特有的字段
        // formattedResponse.put("took", esResult.getOrDefault("took", null));
        
        return formattedResponse;
    }
} 