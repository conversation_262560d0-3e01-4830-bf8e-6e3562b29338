package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.model.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OrderService {

    /**
     * 创建订单
     * 
     * @param orderRequest 订单请求对象
     * @return 创建的订单响应
     */
    OrderResponse createOrder(OrderRequest orderRequest);

    /**
     * 获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单响应
     */
    OrderResponse getOrderById(Long orderId);

    /**
     * 获取当前用户作为买家的订单列表
     * 
     * @param pageable 分页参数
     * @return 订单分页结果
     */
    Page<OrderResponse> getMyBoughtOrders(Pageable pageable);

    /**
     * 获取当前用户作为卖家的订单列表
     * 
     * @param pageable 分页参数
     * @return 订单分页结果
     */
    Page<OrderResponse> getMySoldOrders(Pageable pageable);

    /**
     * 确认订单（卖家操作）
     * 
     * @param orderId 订单ID
     * @return 更新后的订单响应
     */
    OrderResponse confirmOrder(Long orderId);

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @return 更新后的订单响应
     */
    OrderResponse cancelOrder(Long orderId);

    /**
     * 完成订单（买家确认收货）
     * 
     * @param orderId 订单ID
     * @return 更新后的订单响应
     */
    OrderResponse completeOrder(Long orderId);

    /**
     * 按状态查询当前用户作为买家的订单
     * 
     * @param status   订单状态
     * @param pageable 分页参数
     * @return 订单分页结果
     */
    Page<OrderResponse> getMyBoughtOrdersByStatus(String status, Pageable pageable);

    /**
     * 按状态查询当前用户作为卖家的订单
     * 
     * @param status   订单状态
     * @param pageable 分页参数
     * @return 订单分页结果
     */
    Page<OrderResponse> getMySoldOrdersByStatus(String status, Pageable pageable);

    /**
     * 买家确认联系
     * 
     * @param orderId 订单ID
     * @return 更新后的订单响应
     */
    OrderResponse confirmContact(Long orderId);
}