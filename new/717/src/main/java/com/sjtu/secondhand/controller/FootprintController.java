package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.security.JwtTokenProvider;
import com.sjtu.secondhand.service.FootprintService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Tag(name = "用户足迹", description = "管理用户的浏览足迹")
public class FootprintController {

    private static final Logger log = LoggerFactory.getLogger(FootprintController.class);

    private final FootprintService footprintService;
    private final JwtTokenProvider jwtTokenProvider;
    
    @Autowired
    public FootprintController(FootprintService footprintService, JwtTokenProvider jwtTokenProvider) {
        this.footprintService = footprintService;
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Operation(summary = "记录用户足迹", description = "记录用户浏览商品的足迹")
    @PostMapping("/users/me/footprints/{itemId}")
    public ResponseEntity<ApiResponse> recordFootprint(
            @AuthenticationPrincipal UserDetails userDetails,
            @PathVariable Long itemId) {
        
        Long userId = jwtTokenProvider.getUserIdFromUsername(userDetails.getUsername());
        boolean success = footprintService.recordFootprint(userId, itemId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("足迹记录成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.failure("足迹记录失败"));
        }
    }

    @Operation(summary = "获取用户足迹", description = "获取用户的浏览足迹")
    @GetMapping("/users/me/footprints")
    public ResponseEntity<ApiResponse> getUserFootprints(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Long userId = jwtTokenProvider.getUserIdFromUsername(userDetails.getUsername());
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "viewTime"));
        ItemPageResponse response = footprintService.getUserFootprints(userId, pageRequest);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @Operation(summary = "删除足迹", description = "删除用户的某条足迹")
    @DeleteMapping("/users/me/footprints/{itemId}")
    public ResponseEntity<ApiResponse> deleteFootprint(
            @AuthenticationPrincipal UserDetails userDetails,
            @PathVariable Long itemId) {
        
        Long userId = jwtTokenProvider.getUserIdFromUsername(userDetails.getUsername());
        boolean success = footprintService.deleteFootprint(userId, itemId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("删除足迹成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.failure("删除足迹失败"));
        }
    }

    @Operation(summary = "清空足迹", description = "清空用户的所有足迹")
    @DeleteMapping("/users/me/footprints")
    public ResponseEntity<ApiResponse> clearAllFootprints(
            @AuthenticationPrincipal UserDetails userDetails) {
        
        Long userId = jwtTokenProvider.getUserIdFromUsername(userDetails.getUsername());
        boolean success = footprintService.clearAllFootprints(userId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("清空足迹成功"));
        } else {
            return ResponseEntity.ok(ApiResponse.failure("清空足迹失败"));
        }
    }
}
