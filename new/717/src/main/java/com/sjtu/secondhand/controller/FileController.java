package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.OssStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/upload")
@Tag(name = "文件上传", description = "文件上传相关接口")
public class FileController {

    private final FileStorageService fileStorageService;
    private final OssStorageService ossStorageService;

    @Value("${storage.type:local}")
    private String storageType;

    public FileController(FileStorageService fileStorageService, OssStorageService ossStorageService) {
        this.fileStorageService = fileStorageService;
        this.ossStorageService = ossStorageService;
    }

    @PostMapping("/image")
    @Operation(summary = "上传图片", description = "上传商品图片", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<String>> uploadImage(@RequestParam("file") MultipartFile file) {
        if ("oss".equals(storageType)) {
            // 使用OSS存储
            String fileUrl = ossStorageService.uploadFile(file);
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileUrl));
        } else {
            // 使用本地存储
            String fileName = fileStorageService.storeFile(file);
            String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/upload/files/")
                    .path(fileName)
                    .toUriString();
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileDownloadUri));
        }
    }

    @PostMapping("/images")
    @Operation(summary = "批量上传图片", description = "批量上传商品图片", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<String>>> uploadMultipleImages(
            @RequestParam("files") MultipartFile[] files) {
        
        if ("oss".equals(storageType)) {
            // 使用OSS存储
            List<String> fileUrls = Arrays.stream(files)
                    .map(ossStorageService::uploadFile)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileUrls));
        } else {
            // 使用本地存储
            List<String> fileDownloadUrls = Arrays.stream(files)
                    .map(file -> {
                        String fileName = fileStorageService.storeFile(file);
                        return ServletUriComponentsBuilder.fromCurrentContextPath()
                                .path("/upload/files/")
                                .path(fileName)
                                .toUriString();
                    })
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileDownloadUrls));
        }
    }

    @GetMapping("/files/{fileName:.+}")
    @Operation(summary = "下载文件", description = "下载已上传的文件")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName, HttpServletRequest request) {
        // 对于OSS存储方式，应该直接重定向到OSS的URL，不需要通过服务器下载
        // 这里保留原有逻辑，只处理本地存储的文件
        Resource resource = fileStorageService.loadFileAsResource(fileName);

        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            // 默认为二进制流
        }

        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
}