package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.es.ItemDocument;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.es.ItemDocumentRepository;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ElasticsearchSyncServiceImpl implements ElasticsearchSyncService {

    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchSyncServiceImpl.class);
    private static final String ITEMS_INDEX = "items";

    private final ItemRepository itemRepository;
    private final ItemDocumentRepository itemDocumentRepository;
    private final CategoryRepository categoryRepository;
    private final ElasticsearchOperations elasticsearchOperations;
    private final RestTemplate restTemplate;
    
    @Value("${spring.elasticsearch.uris:http://localhost:9200}")
    private String elasticsearchUri;

    @Autowired
    public ElasticsearchSyncServiceImpl(ItemRepository itemRepository,
                                       ItemDocumentRepository itemDocumentRepository,
                                       CategoryRepository categoryRepository,
                                       ElasticsearchOperations elasticsearchOperations) {
        this.itemRepository = itemRepository;
        this.itemDocumentRepository = itemDocumentRepository;
        this.categoryRepository = categoryRepository;
        this.elasticsearchOperations = elasticsearchOperations;
        this.restTemplate = new RestTemplate();
        logger.info("ElasticsearchSyncServiceImpl初始化，待ES URI注入");
    }
    
    @PostConstruct
    public void init() {
        logger.info("ElasticsearchSyncServiceImpl初始化完成，ES URI: {}", elasticsearchUri);
    }

    @Async
    @Override
    public void syncItemToElasticsearch(Item item) {
        try {
            if (item == null) {
                logger.warn("尝试同步空物品到Elasticsearch");
                return;
            }
            
            logger.info("开始同步物品到Elasticsearch, 物品ID: {}", item.getId());
            ItemDocument document = ItemDocument.fromItem(item);
            itemDocumentRepository.save(document);
            logger.info("物品同步到Elasticsearch成功, 物品ID: {}", item.getId());
        } catch (Exception e) {
            logger.error("物品同步到Elasticsearch失败, 物品ID: {}, 错误: {}", 
                        item != null ? item.getId() : "null", e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void deleteItemFromElasticsearch(Long itemId) {
        try {
            logger.info("开始从Elasticsearch删除物品, 物品ID: {}", itemId);
            itemDocumentRepository.deleteById(itemId);
            logger.info("从Elasticsearch删除物品成功, 物品ID: {}", itemId);
        } catch (Exception e) {
            logger.error("从Elasticsearch删除物品失败, 物品ID: {}, 错误: {}", itemId, e.getMessage(), e);
        }
    }

    @Override
    public void syncAllItemsToElasticsearch() {
        try {
            logger.info("开始同步所有物品到Elasticsearch");
            
            // 获取所有物品
            List<Item> allItems = itemRepository.findAll();
            logger.info("从数据库获取到 {} 个物品准备同步", allItems.size());
            
            if (allItems.isEmpty()) {
                logger.warn("数据库中没有物品可供同步");
                return;
            }
            
            // 先清空索引
            try {
                logger.info("尝试清空现有索引");
                itemDocumentRepository.deleteAll();
                logger.info("成功清空Elasticsearch索引");
            } catch (Exception e) {
                logger.warn("清空索引时出错: {}", e.getMessage());
                // 继续执行，不中断同步过程
            }
            
            // 准备文档列表
            List<ItemDocument> documents = new ArrayList<>();
            for (Item item : allItems) {
                try {
                    ItemDocument doc = ItemDocument.fromItem(item);
                    documents.add(doc);
                    logger.debug("转换物品 ID: {} 为ES文档", item.getId());
                } catch (Exception e) {
                    logger.error("转换物品 ID: {} 为ES文档时出错: {}", item.getId(), e.getMessage());
                }
            }
            
            logger.info("成功准备 {} 个文档，开始保存到Elasticsearch", documents.size());
            
            // 批量保存
            Iterable<ItemDocument> saved = itemDocumentRepository.saveAll(documents);
            
            // 统计保存结果
            long count = 0;
            for (ItemDocument doc : saved) {
                count++;
            }
            
            logger.info("同步所有物品到Elasticsearch成功, 共同步 {} 个物品", count);
        } catch (Exception e) {
            logger.error("同步所有物品到Elasticsearch失败, 错误: {}", e.getMessage(), e);
            throw new RuntimeException("同步到Elasticsearch失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object searchItemsByKeyword(String keyword, int page, int size) {
        try {
            logger.info("开始从Elasticsearch搜索物品, 关键词: {}, 页码: {}, 大小: {}", keyword, page, size);
            
            // 由于Spring Data Elasticsearch与ES 7.x版本兼容性问题
            // 返回一个带有标记的空结果，让控制器回退到MySQL搜索
            logger.info("由于Elasticsearch兼容性问题，回退到MySQL搜索");
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_fallback");
            errorResponse.put("message", "Elasticsearch兼容性问题，自动回退到MySQL搜索");
            
            return errorResponse;
            
        } catch (Exception e) {
            logger.error("从Elasticsearch搜索物品失败, 关键词: {}, 错误: {}", keyword, e.getMessage(), e);
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_error");
            errorResponse.put("error", "搜索处理过程中出错: " + e.getMessage());
            
            return errorResponse;
        }
    }

    @Override
    public Object advancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                               String itemType, String condition, int page, int size, String sort) {
        try {
            logger.info("开始从Elasticsearch高级搜索物品");
            
            // 记录关键词处理
            if (keyword != null && !keyword.isEmpty()) {
                logger.info("处理搜索关键词: {}", keyword);
            }
            
            // 由于Spring Data Elasticsearch与ES 7.x版本兼容性问题
            // 返回一个带有标记的空结果，让控制器回退到MySQL搜索
            logger.info("由于Elasticsearch兼容性问题，回退到MySQL搜索");
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_fallback");
            errorResponse.put("message", "Elasticsearch兼容性问题，自动回退到MySQL搜索");
            
            return errorResponse;
        } catch (Exception e) {
            logger.error("从Elasticsearch高级搜索物品失败: {}", e.getMessage(), e);
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_error");
            errorResponse.put("error", "搜索处理过程中出错: " + e.getMessage());
            
            return errorResponse;
        }
    }
    
    @Override
    public Object directSearchByKeyword(String keyword, int page, int size) {
        try {
            logger.info("开始直接向ES发送请求搜索物品, 关键词: {}, 页码: {}, 大小: {}", keyword, page, size);
            logger.info("使用的Elasticsearch URI: {}", elasticsearchUri);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            
            // 构建bool查询
            Map<String, Object> boolQuery = new HashMap<>();
            List<Map<String, Object>> mustClauses = new ArrayList<>();
            List<Map<String, Object>> mustNotClauses = new ArrayList<>();
            List<Map<String, Object>> filterClauses = new ArrayList<>();
            
            // 添加关键词搜索条件（must子句）
            if (keyword != null && !keyword.isEmpty()) {
                Map<String, Object> multiMatch = new HashMap<>();
                multiMatch.put("query", keyword);
                multiMatch.put("fields", List.of("name^3", "description"));
                multiMatch.put("analyzer", "ik_smart_synonym");
                
                Map<String, Object> multiMatchQuery = new HashMap<>();
                multiMatchQuery.put("multi_match", multiMatch);
                mustClauses.add(multiMatchQuery);
            }
            
            // 添加可见性过滤（filter子句）
            Map<String, Object> visibleTerm = new HashMap<>();
            visibleTerm.put("isVisible", true);
            
            Map<String, Object> visibleQuery = new HashMap<>();
            visibleQuery.put("term", visibleTerm);
            filterClauses.add(visibleQuery);
            
            // 添加状态过滤，只返回FOR_SALE状态的物品（filter子句）
            Map<String, Object> forSaleTerm = new HashMap<>();
            forSaleTerm.put("status", "FOR_SALE");
            
            Map<String, Object> forSaleQuery = new HashMap<>();
            forSaleQuery.put("term", forSaleTerm);
            filterClauses.add(forSaleQuery);
            
            // 组装bool查询
            if (!mustClauses.isEmpty()) {
                boolQuery.put("must", mustClauses);
            }
            
            if (!filterClauses.isEmpty()) {
                boolQuery.put("filter", filterClauses);
            }
            
            if (!mustNotClauses.isEmpty()) {
                boolQuery.put("must_not", mustNotClauses);
            }
            
            Map<String, Object> query = new HashMap<>();
            query.put("bool", boolQuery);
            requestBody.put("query", query);
            
            // 高亮部分
            Map<String, Object> nameHighlight = new HashMap<>();
            Map<String, Object> descHighlight = new HashMap<>();
            Map<String, Object> highlightFields = new HashMap<>();
            highlightFields.put("name", nameHighlight);
            highlightFields.put("description", descHighlight);
            
            Map<String, Object> highlight = new HashMap<>();
            highlight.put("fields", highlightFields);
            requestBody.put("highlight", highlight);
            
            // 分页
            requestBody.put("from", page * size);
            requestBody.put("size", size);
            
            // 排序
            Map<String, Object> scoreSort = new HashMap<>();
            scoreSort.put("order", "desc");
            
            Map<String, Object> createdAtSort = new HashMap<>();
            createdAtSort.put("order", "desc");
            
            List<Map<String, Object>> sorts = new ArrayList<>();
            Map<String, Object> sortScore = new HashMap<>();
            sortScore.put("_score", scoreSort);
            sorts.add(sortScore);
            
            Map<String, Object> sortCreatedAt = new HashMap<>();
            sortCreatedAt.put("createdAt", createdAtSort);
            sorts.add(sortCreatedAt);
            
            requestBody.put("sort", sorts);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            String searchUrl = elasticsearchUri + "/" + ITEMS_INDEX + "/_search";
            logger.info("发送ES搜索请求到: {}", searchUrl);
            logger.info("请求体: {}", requestBody);
            
            try {
                ResponseEntity<Map> response = restTemplate.postForEntity(searchUrl, requestEntity, Map.class);
                logger.info("ES搜索请求完成，状态码: {}", response.getStatusCode());
                
                // 处理响应
                return transformElasticsearchResponse(response.getBody(), page, size);
            } catch (Exception e) {
                logger.error("发送ES搜索请求失败: {}", e.getMessage(), e);
                throw e;
            }
            
        } catch (Exception e) {
            logger.error("直接向ES发送请求搜索物品失败, 关键词: {}, 错误: {}", keyword, e.getMessage(), e);
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_error");
            errorResponse.put("error", "搜索处理过程中出错: " + e.getMessage());
            
            return errorResponse;
        }
    }

    @Override
    public Object directAdvancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                               String itemType, String condition, int page, int size, String sort) {
        try {
            logger.info("开始直接向ES发送请求进行高级搜索");
            logger.info("使用的Elasticsearch URI: {}", elasticsearchUri);
            logger.info("搜索参数: 关键词={}, 分类ID={}, 价格范围=[{} - {}], 类型={}, 条件={}, 页码={}, 大小={}, 排序={}",
                        keyword, categoryId, minPrice, maxPrice, itemType, condition, page, size, sort);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            
            // 创建bool查询
            Map<String, Object> boolQuery = new HashMap<>();
            List<Map<String, Object>> mustClauses = new ArrayList<>();
            List<Map<String, Object>> mustNotClauses = new ArrayList<>();
            List<Map<String, Object>> filterClauses = new ArrayList<>();
            
            // 添加关键词搜索条件（must子句）
            if (keyword != null && !keyword.isEmpty()) {
                logger.info("处理搜索关键词: {}", keyword);
                Map<String, Object> multiMatch = new HashMap<>();
                multiMatch.put("query", keyword);
                multiMatch.put("fields", List.of("name^3", "description"));
                multiMatch.put("analyzer", "ik_smart_synonym");
                
                Map<String, Object> multiMatchQuery = new HashMap<>();
                multiMatchQuery.put("multi_match", multiMatch);
                mustClauses.add(multiMatchQuery);
            }
            
            // 添加分类过滤（filter子句）
            if (categoryId != null) {
                Map<String, Object> termValue = new HashMap<>();
                termValue.put("category.id", categoryId);
                
                Map<String, Object> term = new HashMap<>();
                term.put("term", termValue);
                filterClauses.add(term);
            }
            
            // 添加价格范围过滤（filter子句）
            if (minPrice != null || maxPrice != null) {
                Map<String, Object> rangeValues = new HashMap<>();
                
                if (minPrice != null) {
                    rangeValues.put("gte", minPrice);
                }
                
                if (maxPrice != null) {
                    rangeValues.put("lte", maxPrice);
                }
                
                Map<String, Object> priceRange = new HashMap<>();
                priceRange.put("price", rangeValues);
                
                Map<String, Object> range = new HashMap<>();
                range.put("range", priceRange);
                filterClauses.add(range);
            }
            
            // 添加物品类型过滤（filter子句）
            if (itemType != null && !itemType.isEmpty()) {
                Map<String, Object> termValue = new HashMap<>();
                termValue.put("itemType", itemType);
                
                Map<String, Object> term = new HashMap<>();
                term.put("term", termValue);
                filterClauses.add(term);
            }
            
            // 添加物品状况过滤（filter子句）
            if (condition != null && !condition.isEmpty()) {
                Map<String, Object> termValue = new HashMap<>();
                termValue.put("condition", condition);
                
                Map<String, Object> term = new HashMap<>();
                term.put("term", termValue);
                filterClauses.add(term);
            }
            
            // 添加可见性过滤（filter子句）
            Map<String, Object> visibleTerm = new HashMap<>();
            visibleTerm.put("isVisible", true);
            
            Map<String, Object> visibleQuery = new HashMap<>();
            visibleQuery.put("term", visibleTerm);
            filterClauses.add(visibleQuery);
            
            // 添加状态过滤，只返回FOR_SALE状态的物品（filter子句）
            Map<String, Object> forSaleTerm = new HashMap<>();
            forSaleTerm.put("status", "FOR_SALE");
            
            Map<String, Object> forSaleQuery = new HashMap<>();
            forSaleQuery.put("term", forSaleTerm);
            filterClauses.add(forSaleQuery);
            
            // 组装bool查询
            if (!mustClauses.isEmpty()) {
                boolQuery.put("must", mustClauses);
            }
            
            if (!filterClauses.isEmpty()) {
                boolQuery.put("filter", filterClauses);
            }
            
            if (!mustNotClauses.isEmpty()) {
                boolQuery.put("must_not", mustNotClauses);
            }
            
            Map<String, Object> query = new HashMap<>();
            query.put("bool", boolQuery);
            requestBody.put("query", query);
            
            // 高亮部分
            Map<String, Object> nameHighlight = new HashMap<>();
            Map<String, Object> descHighlight = new HashMap<>();
            Map<String, Object> highlightFields = new HashMap<>();
            highlightFields.put("name", nameHighlight);
            highlightFields.put("description", descHighlight);
            
            Map<String, Object> highlight = new HashMap<>();
            highlight.put("fields", highlightFields);
            requestBody.put("highlight", highlight);
            
            // 分页
            requestBody.put("from", page * size);
            requestBody.put("size", size);
            
            // 排序
            List<Map<String, Object>> sorts = new ArrayList<>();
            
            // 默认按照相关性和时间排序
            if (sort == null || sort.isEmpty() || "relevance".equals(sort)) {
                Map<String, Object> scoreSort = new HashMap<>();
                scoreSort.put("order", "desc");
                
                Map<String, Object> sortScore = new HashMap<>();
                sortScore.put("_score", scoreSort);
                sorts.add(sortScore);
                
                Map<String, Object> createdAtSort = new HashMap<>();
                createdAtSort.put("order", "desc");
                
                Map<String, Object> sortCreatedAt = new HashMap<>();
                sortCreatedAt.put("createdAt", createdAtSort);
                sorts.add(sortCreatedAt);
            } else if ("price_asc".equals(sort)) {
                Map<String, Object> priceSort = new HashMap<>();
                priceSort.put("order", "asc");
                
                Map<String, Object> sortPrice = new HashMap<>();
                sortPrice.put("price", priceSort);
                sorts.add(sortPrice);
            } else if ("price_desc".equals(sort)) {
                Map<String, Object> priceSort = new HashMap<>();
                priceSort.put("order", "desc");
                
                Map<String, Object> sortPrice = new HashMap<>();
                sortPrice.put("price", priceSort);
                sorts.add(sortPrice);
            } else if ("date_desc".equals(sort)) {
                Map<String, Object> dateSort = new HashMap<>();
                dateSort.put("order", "desc");
                
                Map<String, Object> sortDate = new HashMap<>();
                sortDate.put("createdAt", dateSort);
                sorts.add(sortDate);
            } else if ("date_asc".equals(sort)) {
                Map<String, Object> dateSort = new HashMap<>();
                dateSort.put("order", "asc");
                
                Map<String, Object> sortDate = new HashMap<>();
                sortDate.put("createdAt", dateSort);
                sorts.add(sortDate);
            }
            
            requestBody.put("sort", sorts);
            
            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            String searchUrl = elasticsearchUri + "/" + ITEMS_INDEX + "/_search";
            logger.info("发送ES高级搜索请求到: {}", searchUrl);
            logger.info("请求体: {}", requestBody);
            
            try {
                ResponseEntity<Map> response = restTemplate.postForEntity(searchUrl, requestEntity, Map.class);
                logger.info("ES高级搜索请求完成，状态码: {}", response.getStatusCode());
                
                // 处理响应
                return transformElasticsearchResponse(response.getBody(), page, size);
            } catch (Exception e) {
                logger.error("发送ES高级搜索请求失败: {}", e.getMessage(), e);
                throw e;
            }
            
        } catch (Exception e) {
            logger.error("直接向ES发送请求高级搜索物品失败: {}", e.getMessage(), e);
            
            // 创建一个空的结果集
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("items", new ArrayList<>());
            errorResponse.put("total", 0);
            errorResponse.put("page", page);
            errorResponse.put("size", size);
            errorResponse.put("source", "elasticsearch_error");
            errorResponse.put("error", "高级搜索处理过程中出错: " + e.getMessage());
            
            return errorResponse;
        }
    }
    
    // 处理ES返回的结果
    @SuppressWarnings("unchecked")
    private Map<String, Object> transformElasticsearchResponse(Map<String, Object> esResponse, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        
        if (esResponse == null) {
            logger.warn("ES返回空响应");
            result.put("items", new ArrayList<>());
            result.put("total", 0);
            result.put("page", page);
            result.put("size", size);
            result.put("source", "elasticsearch");
            result.put("error", "ES返回空响应");
            return result;
        }
        
        try {
            // 记录原始响应中的关键信息
            logger.info("ES响应 took: {}, timed_out: {}", 
                       esResponse.get("took"), esResponse.get("timed_out"));
            
            if (esResponse.containsKey("_shards")) {
                Map<String, Object> shards = (Map<String, Object>) esResponse.get("_shards");
                logger.info("ES响应 _shards: total={}, successful={}, failed={}", 
                           shards.get("total"), shards.get("successful"), shards.get("failed"));
            }
            
            // 从ES响应中提取hits
            Map<String, Object> hitsContainer = (Map<String, Object>) esResponse.get("hits");
            
            if (hitsContainer == null) {
                logger.warn("ES响应中无hits部分");
                result.put("items", new ArrayList<>());
                result.put("total", 0);
                result.put("page", page);
                result.put("size", size);
                result.put("source", "elasticsearch");
                return result;
            }
            
            // 获取总数
            Map<String, Object> totalMap = (Map<String, Object>) hitsContainer.get("total");
            int total = totalMap != null ? ((Number) totalMap.get("value")).intValue() : 0;
            logger.info("ES搜索结果总数: {}", total);
            
            // 获取命中的文档
            List<Map<String, Object>> hitsList = (List<Map<String, Object>>) hitsContainer.get("hits");
            logger.info("ES搜索结果条数: {}", hitsList != null ? hitsList.size() : 0);
            
            // 转换为ItemResponse格式
            List<Map<String, Object>> items = new ArrayList<>();
            
            if (hitsList != null) {
                for (Map<String, Object> hit : hitsList) {
                    Map<String, Object> source = (Map<String, Object>) hit.get("_source");
                    
                    if (source != null) {
                        // 处理高亮显示
                        Map<String, Object> highlight = (Map<String, Object>) hit.get("highlight");
                        if (highlight != null) {
                            List<String> nameHighlights = (List<String>) highlight.get("name");
                            if (nameHighlights != null && !nameHighlights.isEmpty()) {
                                source.put("nameHighlight", nameHighlights.get(0));
                            }
                            
                            List<String> descHighlights = (List<String>) highlight.get("description");
                            if (descHighlights != null && !descHighlights.isEmpty()) {
                                source.put("descriptionHighlight", descHighlights.get(0));
                            }
                        }
                        
                        // 添加相关性得分
                        double score = ((Number) hit.get("_score")).doubleValue();
                        source.put("score", score);
                        
                        // 记录文档ID和得分
                        logger.debug("处理命中文档: id={}, score={}", hit.get("_id"), score);
                        
                        // =========== 添加前端期望的字段 ===========
                        // 1. 处理图片 - 为保持兼容性，同时保留imageUrls字段
                        List<String> imageUrls = (List<String>) source.get("imageUrls");
                        if (imageUrls != null && !imageUrls.isEmpty()) {
                            // 普通搜索中images是字符串数组，不是对象数组
                            source.put("images", imageUrls);
                            
                            // 删除ES特有的图片字段，避免前端混淆
                            source.remove("mainImage");
                            
                            logger.debug("添加图片信息: images数量={}", imageUrls.size());
                        } else {
                            // 无图片时设置空列表
                            source.put("images", new ArrayList<>());
                        }
                        
                        // 2. 处理用户信息 - 确保同时有user和seller字段
                        Map<String, Object> user = (Map<String, Object>) source.get("user");
                        if (user != null) {
                            // 确保user中使用avatar字段而不是avatarUrl
                            if (user.containsKey("avatarUrl")) {
                                user.put("avatar", user.get("avatarUrl"));
                                user.remove("avatarUrl"); // 移除avatarUrl字段，避免前端混淆
                            }
                            
                            // 确保存在seller字段
                            if (!source.containsKey("seller")) {
                                Map<String, Object> seller = new HashMap<>(user);
                                source.put("seller", seller);
                            } else {
                                // 如果已有seller字段，确保其中使用avatar
                                Map<String, Object> seller = (Map<String, Object>) source.get("seller");
                                if (seller.containsKey("avatarUrl")) {
                                    seller.put("avatar", seller.get("avatarUrl"));
                                    seller.remove("avatarUrl"); // 移除avatarUrl字段，避免前端混淆
                                }
                            }
                        }
                        
                        // 3. 确保title字段存在（与name相同）
                        if (!source.containsKey("title") && source.containsKey("name")) {
                            source.put("title", source.get("name"));
                        }
                        
                        // 4. 添加普通搜索中的其他字段
                        if (!source.containsKey("priceMin")) {
                            source.put("priceMin", null);
                        }
                        if (!source.containsKey("priceMax")) {
                            source.put("priceMax", null);
                        }
                        if (!source.containsKey("categoryId") && source.containsKey("category")) {
                            Map<String, Object> category = (Map<String, Object>) source.get("category");
                            if (category != null && category.containsKey("id")) {
                                source.put("categoryId", category.get("id"));
                            }
                        }
                        if (!source.containsKey("categoryName") && source.containsKey("category")) {
                            Map<String, Object> category = (Map<String, Object>) source.get("category");
                            if (category != null && category.containsKey("name")) {
                                source.put("categoryName", category.get("name"));
                            }
                        }
                        if (!source.containsKey("isFavorited")) {
                            source.put("isFavorited", false);
                        }
                        
                        // 5. 格式化日期字段
                        if (source.containsKey("createdAt") && !(source.get("createdAt") instanceof String)) {
                            try {
                                String createdAt = source.get("createdAt").toString();
                                if (!createdAt.contains(" ")) {
                                    source.put("createdAt", createdAt + " 00:00:00");
                                }
                            } catch (Exception e) {
                                logger.warn("格式化createdAt字段失败: {}", e.getMessage());
                            }
                        }
                        if (source.containsKey("updatedAt") && !(source.get("updatedAt") instanceof String)) {
                            try {
                                String updatedAt = source.get("updatedAt").toString();
                                if (!updatedAt.contains(" ")) {
                                    source.put("updatedAt", updatedAt + " 00:00:00");
                                }
                            } catch (Exception e) {
                                logger.warn("格式化updatedAt字段失败: {}", e.getMessage());
                            }
                        }
                        
                        // 6. 添加viewTime和viewTimeRaw字段
                        if (!source.containsKey("viewTime")) {
                            source.put("viewTime", null);
                        }
                        if (!source.containsKey("viewTimeRaw")) {
                            source.put("viewTimeRaw", null);
                        }
                        
                        // 7. 删除ES特有的字段，避免前端混淆
                        source.remove("_class");
                        source.remove("isVisible");
                        source.remove("nameHighlight");
                        source.remove("descriptionHighlight");
                        source.remove("score");
                        
                        items.add(source);
                    }
                }
            }
            
            // 填充结果
            result.put("items", items);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("source", "elasticsearch_direct");
            result.put("took", esResponse.get("took"));
            
            logger.info("转换ES响应完成，返回 {} 个物品", items.size());
            return result;
            
        } catch (Exception e) {
            logger.error("处理ES响应时出错: {}", e.getMessage(), e);
            
            result.put("items", new ArrayList<>());
            result.put("total", 0);
            result.put("page", page);
            result.put("size", size);
            result.put("source", "elasticsearch_error");
            result.put("error", "处理ES响应时出错: " + e.getMessage());
            
            return result;
        }
    }
    
    // 辅助方法：将ItemDocument转换为Map
    private Map<String, Object> convertToMap(ItemDocument document) {
        Map<String, Object> result = new HashMap<>();
        result.put("id", document.getId());
        result.put("name", document.getName());
        result.put("title", document.getName()); // 为了兼容性
        result.put("description", document.getDescription());
        result.put("price", document.getPrice());
        result.put("priceMin", document.getPriceMin());
        result.put("priceMax", document.getPriceMax());
        result.put("itemType", document.getItemType());
        result.put("condition", document.getCondition());
        result.put("status", document.getStatus());
        result.put("viewCount", document.getViewCount());
        result.put("favoriteCount", document.getFavoriteCount());
        result.put("createdAt", document.getCreatedAt());
        result.put("updatedAt", document.getUpdatedAt());
        result.put("category", document.getCategory());
        result.put("seller", document.getUser()); // 为了兼容性
        result.put("user", document.getUser());
        result.put("images", document.getImageUrls());
        
        return result;
    }

    // 辅助方法：将Item转换为Map
    private Map<String, Object> convertItemToMap(com.sjtu.secondhand.model.Item item) {
        Map<String, Object> result = new HashMap<>();
        result.put("id", item.getId());
        result.put("name", item.getName());
        result.put("title", item.getName()); // 为了兼容性
        result.put("description", item.getDescription());
        result.put("price", item.getPrice());
        result.put("priceMin", item.getPriceMin());
        result.put("priceMax", item.getPriceMax());
        result.put("itemType", item.getItemType() != null ? item.getItemType().name() : null);
        result.put("condition", item.getCondition() != null ? item.getCondition().name() : null);
        result.put("status", item.getStatus() != null ? item.getStatus().name() : null);
        result.put("viewCount", item.getViewCount());
        result.put("favoriteCount", item.getFavoriteCount());
        result.put("createdAt", item.getCreatedAt());
        result.put("updatedAt", item.getUpdatedAt());
        
        // 转换分类
        if (item.getCategory() != null) {
            Map<String, Object> categoryMap = new HashMap<>();
            categoryMap.put("id", item.getCategory().getId());
            categoryMap.put("name", item.getCategory().getName());
            result.put("category", categoryMap);
        }
        
        // 转换用户信息
        if (item.getUser() != null) {
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("id", item.getUser().getId());
            userMap.put("username", item.getUser().getUsername());
            userMap.put("avatarUrl", item.getUser().getAvatarUrl());
            userMap.put("rating", item.getUser().getRating());
            result.put("user", userMap);
            result.put("seller", userMap); // 为了兼容性
        }
        
        // 转换图片URL
        if (item.getImages() != null && !item.getImages().isEmpty()) {
            result.put("images", item.getImages().stream()
                    .map(img -> img.getUrl())
                    .collect(Collectors.toList()));
        }
        
        return result;
    }
} 