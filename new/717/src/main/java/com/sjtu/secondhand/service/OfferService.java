package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.model.Offer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OfferService {

    /**
     * 创建求好物响应
     * 
     * @param offerRequest 响应请求对象
     * @return 创建的响应对象
     */
    OfferResponse createOffer(OfferRequest offerRequest);
    
    /**
     * 创建求好物响应（带物品ID）
     * 
     * @param wantedItemId 求购物品ID
     * @param offerRequest 响应请求对象
     * @return 创建的响应对象
     */
    OfferResponse createOffer(Long wantedItemId, OfferRequest offerRequest);

    /**
     * 获取响应详情
     * 
     * @param offerId 响应ID
     * @return 响应详情
     */
    OfferResponse getOfferById(Long offerId);

    /**
     * 获取当前用户作为响应者的响应列表
     * 
     * @param pageable 分页参数
     * @return 响应分页结果
     */
    Page<OfferResponse> getMyOffers(Pageable pageable);

    /**
     * 获取当前用户作为求购者的响应列表
     * 
     * @param pageable 分页参数
     * @return 响应分页结果
     */
    Page<OfferResponse> getMyRequests(Pageable pageable);

    /**
     * 接受响应（求购者操作）
     * 
     * @param offerId 响应ID
     * @return 更新后的响应
     */
    OfferResponse acceptOffer(Long offerId);

    /**
     * 拒绝响应（求购者操作）
     * 
     * @param offerId 响应ID
     * @return 更新后的响应
     */
    OfferResponse rejectOffer(Long offerId);

    /**
     * 确认响应（响应者操作）
     * 
     * @param offerId 响应ID
     * @return 更新后的响应
     */
    OfferResponse confirmOffer(Long offerId);

    /**
     * 完成交易（任一方操作）
     * 
     * @param offerId 响应ID
     * @return 更新后的响应
     */
    OfferResponse completeOffer(Long offerId);

    /**
     * 取消响应（响应者操作）
     * 
     * @param offerId 响应ID
     * @return 更新后的响应
     */
    OfferResponse cancelOffer(Long offerId);

    /**
     * 按状态查询当前用户作为响应者的响应
     * 
     * @param status   响应状态
     * @param pageable 分页参数
     * @return 响应分页结果
     */
    Page<OfferResponse> getMyOffersByStatus(String status, Pageable pageable);

    /**
     * 按状态查询当前用户作为求购者的响应
     * 
     * @param status   响应状态
     * @param pageable 分页参数
     * @return 响应分页结果
     */
    Page<OfferResponse> getMyRequestsByStatus(String status, Pageable pageable);
}