package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.Rating;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.RatingRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.RatingService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RatingServiceImpl implements RatingService {

    private final RatingRepository ratingRepository;
    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final UserService userService;
    private final NotificationService notificationService;

    public RatingServiceImpl(RatingRepository ratingRepository,
            UserRepository userRepository,
            OrderRepository orderRepository,
            UserService userService,
            NotificationService notificationService) {
        this.ratingRepository = ratingRepository;
        this.userRepository = userRepository;
        this.orderRepository = orderRepository;
        this.userService = userService;
        this.notificationService = notificationService;
    }

    @Override
    @Transactional
    public RatingResponse createRating(RatingRequest ratingRequest) {
        User currentUser = userService.getCurrentUser();
        Rating rating = new Rating();
        rating.setRater(currentUser);
        rating.setScore(ratingRequest.getScore().byteValue());
        
        // 设置评价类型
        Rating.TransactionType transactionType;
        if ("IDLE".equals(ratingRequest.getTransaction_type())) {
            transactionType = Rating.TransactionType.IDLE;
        } else if ("WANTED".equals(ratingRequest.getTransaction_type())) {
            transactionType = Rating.TransactionType.WANTED;
        } else {
            throw new ApiException(HttpStatus.BAD_REQUEST, "无效的交易类型");
        }
        rating.setTransactionType(transactionType);

        User ratee;
        Long transactionId = ratingRequest.getRelated_transaction_id().longValue();
        rating.setRelatedTransactionId(transactionId);

        // 处理订单评价
        Order order = orderRepository.findById(transactionId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证订单状态
        if (order.getStatus() != Order.OrderStatus.COMPLETED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只能评价已完成的订单");
        }

        // 检查是否已经评价
        if (ratingRepository.existsByRelatedTransactionIdAndRater(transactionId, currentUser)) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "您已经对该交易进行了评价");
        }

        // 根据当前用户是买家还是卖家来确定被评价人
        if (currentUser.getId().equals(order.getBuyer().getId())) {
            // 当前用户是买家，评价卖家
            ratee = order.getSeller();
            order.setIsBuyerRated(true);
        } else if (currentUser.getId().equals(order.getSeller().getId())) {
            // 当前用户是卖家，评价买家
            ratee = order.getBuyer();
            order.setIsSellerRated(true);
        } else {
            throw new ApiException(HttpStatus.FORBIDDEN, "您不是该订单的买家或卖家");
        }

        // 更新订单评价状态
        orderRepository.save(order);

        rating.setRatee(ratee);

        // 保存评价
        Rating savedRating = ratingRepository.save(rating);

        // 更新用户信用分数
        updateUserCreditScore(ratee.getId(), rating.getScore());

        // 发送通知
        notificationService.createRatingReceivedNotification(savedRating.getId(), ratee, currentUser);

        return new RatingResponse(savedRating);
    }

    @Override
    @Transactional(readOnly = true)
    public RatingResponse getRatingById(Long ratingId) {
        Rating rating = ratingRepository.findById(ratingId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "评价不存在"));

        return new RatingResponse(rating);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getMyRatings(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Rating> ratings = ratingRepository.findByRater(currentUser, pageable);

        return ratings.map(RatingResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getRatingsAboutMe(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Rating> ratings = ratingRepository.findByRatee(currentUser, pageable);

        return ratings.map(RatingResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getUserRatings(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));

        Page<Rating> ratings = ratingRepository.findByRatee(user, pageable);

        return ratings.map(RatingResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getMyRatingsByTransactionType(String type, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Rating.TransactionType transactionType = parseTransactionType(type);

        Page<Rating> ratings = ratingRepository.findByRaterAndTransactionType(currentUser, transactionType, pageable);

        return ratings.map(RatingResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getRatingsAboutMeByTransactionType(String type, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Rating.TransactionType transactionType = parseTransactionType(type);

        Page<Rating> ratings = ratingRepository.findByRateeAndTransactionType(currentUser, transactionType, pageable);

        return ratings.map(RatingResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getUserAverageRating(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));

        Double averageRating = ratingRepository.calculateAverageRating(user);

        return averageRating != null ? averageRating : 0.0;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RatingResponse> getPendingOrderRatings(Pageable pageable) {
        User currentUser = userService.getCurrentUser();

        // 获取当前用户作为买家但未评价的已完成订单
        Page<Order> buyerOrders = orderRepository.findByBuyerAndStatusAndIsBuyerRatedFalse(
                currentUser, Order.OrderStatus.COMPLETED, pageable);

        // 获取当前用户作为卖家但未评价的已完成订单
        Page<Order> sellerOrders = orderRepository.findBySellerAndStatusAndIsSellerRatedFalse(
                currentUser, Order.OrderStatus.COMPLETED, pageable);

        // 这里需要合并两个结果，但由于分页的复杂性，实际实现可能需要自定义查询
        // 这里只是一个简化的示例
        return buyerOrders.map(order -> new RatingResponse(new Rating()));
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasRatedTransaction(Long transactionId) {
        User currentUser = userService.getCurrentUser();
        return ratingRepository.existsByRelatedTransactionIdAndRater(transactionId, currentUser);
    }

    // 辅助方法：更新用户信用分数
    private void updateUserCreditScore(Long userId, Byte ratingScore) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));

        // 获取用户当前的信用分数
        Integer currentScore = user.getCreditScore();

        // 根据评分调整信用分数
        // 这里使用一个简单的算法：
        // 5分：+2分
        // 4分：+1分
        // 3分：+0分
        // 2分：-1分
        // 1分：-2分
        int adjustment;
        switch (ratingScore) {
            case 5:
                adjustment = 2;
                break;
            case 4:
                adjustment = 1;
                break;
            case 3:
                adjustment = 0;
                break;
            case 2:
                adjustment = -1;
                break;
            case 1:
                adjustment = -2;
                break;
            default:
                adjustment = 0;
        }

        // 更新信用分数，确保在0-100之间
        int newScore = Math.min(100, Math.max(0, currentScore + adjustment));
        user.setCreditScore(newScore);

        userRepository.save(user);
    }

    // 辅助方法：解析交易类型
    private Rating.TransactionType parseTransactionType(String type) {
        if ("IDLE".equalsIgnoreCase(type)) {
            return Rating.TransactionType.IDLE;
        } else if ("WANTED".equalsIgnoreCase(type)) {
            return Rating.TransactionType.WANTED;
        } else {
            try {
                return Rating.TransactionType.valueOf(type.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new ApiException(HttpStatus.BAD_REQUEST, "无效的交易类型");
            }
        }
    }
}