package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sjtu.secondhand.model.User;

/**
 * 用户公开信息响应类
 */
public class UserResponse {
    private Long id;
    
    private String username;
    
    @JsonProperty("avatar_url")
    private String avatarUrl;
    
    @JsonProperty("credit_score")
    private Integer creditScore;

    public UserResponse() {
    }

    public UserResponse(User user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.avatarUrl = user.getAvatarUrl();
        this.creditScore = user.getCreditScore();
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Integer getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }
} 