package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.response.ItemPageResponse;
import org.springframework.data.domain.Pageable;

/**
 * 用户浏览足迹服务接口
 */
public interface FootprintService {
    
    /**
     * 记录用户浏览商品的足迹
     * @param userId 用户ID
     * @param itemId 商品ID
     * @return 是否记录成功
     */
    boolean recordFootprint(Long userId, Long itemId);
    
    /**
     * 获取用户的浏览足迹
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 分页的商品列表
     */
    ItemPageResponse getUserFootprints(Long userId, Pageable pageable);
    
    /**
     * 删除用户的某条足迹
     * @param userId 用户ID
     * @param itemId 商品ID
     * @return 是否删除成功
     */
    boolean deleteFootprint(Long userId, Long itemId);
    
    /**
     * 清空用户的所有足迹
     * @param userId 用户ID
     * @return 是否清空成功
     */
    boolean clearAllFootprints(Long userId);
}
