package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/orders")
@Tag(name = "订单 (Orders)", description = "订单相关接口")
public class OrderController {

    private final OrderService orderService;

    public OrderController(OrderService orderService) {
        this.orderService = orderService;
    }

    // 创建订单
    @PostMapping
    @Operation(summary = "创建订单 (预订闲置物品)", description = "买家对一个 'IDLE' 类型的物品发起预订，创建一个订单，物品状态变为 'RESERVED'")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<OrderResponse>> createOrder(@Valid @RequestBody OrderRequest orderRequest) {
        OrderResponse orderResponse = orderService.createOrder(orderRequest);
        return new ResponseEntity<>(ApiResponse.success("订单创建成功", orderResponse), HttpStatus.CREATED);
    }

    // 获取订单详情
    @GetMapping("/{id}")
    @Operation(summary = "获取订单详情", description = "获取指定ID的订单详细信息")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderById(@PathVariable Long id) {
        OrderResponse orderResponse = orderService.getOrderById(id);
        return ResponseEntity.ok(ApiResponse.success("获取订单成功", orderResponse));
    }

    // 获取我购买的订单列表
    @GetMapping("/my/bought")
    @Operation(summary = "获取我购买的订单列表", description = "获取当前用户作为买家的订单列表")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getMyBoughtOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<OrderResponse> orders = orderService.getMyBoughtOrders(pageable);
        return ResponseEntity.ok(ApiResponse.success("获取购买订单列表成功", orders));
    }

    // 获取我出售的订单列表
    @GetMapping("/my/sold")
    @Operation(summary = "获取我出售的订单列表", description = "获取当前用户作为卖家的订单列表")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getMySoldOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<OrderResponse> orders = orderService.getMySoldOrders(pageable);
        return ResponseEntity.ok(ApiResponse.success("获取出售订单列表成功", orders));
    }

    // 确认订单（卖家操作）
    @PostMapping("/{id}/confirm")
    @Operation(summary = "[卖家] 确认订单", description = "卖家操作。将 'PENDING_CONFIRMATION' 状态的订单流转至 'AWAITING_ACKNOWLEDGEMENT'")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<OrderResponse>> confirmOrder(@PathVariable Long id) {
        OrderResponse orderResponse = orderService.confirmOrder(id);
        return ResponseEntity.ok(ApiResponse.success("订单确认成功", orderResponse));
    }

    // 取消订单
    @PostMapping("/{id}/cancel")
    @Operation(summary = "[买/卖家] 取消订单", description = "在交易完成前，由买家或卖家取消订单，状态流转至 'CANCELLED'，物品状态恢复 'FOR_SALE'")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<OrderResponse>> cancelOrder(@PathVariable Long id) {
        OrderResponse orderResponse = orderService.cancelOrder(id);
        return ResponseEntity.ok(ApiResponse.success("订单取消成功", orderResponse));
    }

    // 买家确认联系
    @PostMapping("/{id}/acknowledge")
    @Operation(summary = "[买家] 确认收到联系方式", description = "买家操作。确认已收到卖家联系方式，状态流转至 'CONFIRMED'")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<OrderResponse>> acknowledgeOrder(@PathVariable Long id) {
        OrderResponse orderResponse = orderService.confirmContact(id);
        return ResponseEntity.ok(ApiResponse.success("确认联系成功", orderResponse));
    }

    // 获取特定状态的我购买的订单
    @GetMapping("/my/bought/status/{status}")
    @Operation(summary = "获取特定状态的我购买的订单", description = "获取当前用户作为买家的特定状态订单列表")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getMyBoughtOrdersByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<OrderResponse> orders = orderService.getMyBoughtOrdersByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success("获取指定状态的购买订单成功", orders));
    }

    // 获取特定状态的我出售的订单
    @GetMapping("/my/sold/status/{status}")
    @Operation(summary = "获取特定状态的我出售的订单", description = "获取当前用户作为卖家的特定状态订单列表")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getMySoldOrdersByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 调整页码，因为API文档从1开始，而Spring Data从0开始
        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<OrderResponse> orders = orderService.getMySoldOrdersByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success("获取指定状态的出售订单成功", orders));
    }
}