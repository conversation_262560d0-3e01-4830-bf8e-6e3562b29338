package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ItemRequest {

    @NotNull(message = "物品类型不能为空")
    private String item_type; // IDLE-卖闲置, WANTED-求好物

    @NotBlank(message = "名称不能为空")
    @Size(max = 100, message = "名称长度不能超过100个字符")
    private String name;

    @NotBlank(message = "描述不能为空")
    private String description;

    @NotNull(message = "分类ID不能为空")
    private Integer category_id;

    // 当 item_type 为 'IDLE' 时必填
    private BigDecimal price;

    // 当 item_type 为 'WANTED' 时可选，心理价位下限
    private BigDecimal price_min;

    // 当 item_type 为 'WANTED' 时可选，心理价位上限
    private BigDecimal price_max;

    @NotNull(message = "物品状态不能为空")
    private String condition; // BRAND_NEW-全新, LIKE_NEW-几乎全新, FINE-轻微使用, CLEARLY_USED-明显使用

    // 图片URL列表，由上传接口返回
    private List<String> image_urls = new ArrayList<>();

    // Getters and Setters
    public String getItem_type() {
        return item_type;
    }

    public void setItem_type(String item_type) {
        this.item_type = item_type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCategory_id() {
        return category_id;
    }

    public void setCategory_id(Integer category_id) {
        this.category_id = category_id;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice_min() {
        return price_min;
    }

    public void setPrice_min(BigDecimal price_min) {
        this.price_min = price_min;
    }

    public BigDecimal getPrice_max() {
        return price_max;
    }

    public void setPrice_max(BigDecimal price_max) {
        this.price_max = price_max;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public List<String> getImage_urls() {
        return image_urls;
    }

    public void setImage_urls(List<String> image_urls) {
        this.image_urls = image_urls;
    }
}