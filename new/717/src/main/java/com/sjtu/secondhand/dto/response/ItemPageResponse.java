package com.sjtu.secondhand.dto.response;

import org.springframework.data.domain.Page;

import java.util.List;

public class ItemPageResponse {

    private List<ItemResponse> items;
    private int currentPage;
    private int size;
    private long totalItems;
    private int totalPages;
    private String source; // 添加数据来源标记

    public ItemPageResponse() {
    }

    public ItemPageResponse(List<ItemResponse> items, Page<?> page) {
        this.items = items;
        this.currentPage = page.getNumber();
        this.size = page.getSize();
        this.totalItems = page.getTotalElements();
        this.totalPages = page.getTotalPages();
    }

    // 添加一个额外的构造函数，用于直接从 Page<ItemResponse> 创建
    public ItemPageResponse(Page<ItemResponse> page) {
        this.items = page.getContent();
        this.currentPage = page.getNumber();
        this.size = page.getSize();
        this.totalItems = page.getTotalElements();
        this.totalPages = page.getTotalPages();
    }
    
    // 添加一个私有构造函数，用于Builder模式
    private ItemPageResponse(Builder builder) {
        this.items = builder.items;
        this.currentPage = builder.currentPage;
        this.size = builder.size;
        this.totalItems = builder.totalItems;
        this.totalPages = builder.totalPages;
        this.source = builder.source;
    }
    
    // 静态Builder方法
    public static Builder builder() {
        return new Builder();
    }
    
    // Builder类
    public static class Builder {
        private List<ItemResponse> items;
        private int currentPage;
        private int size;
        private long totalItems;
        private int totalPages;
        private String source;
        
        public Builder items(List<ItemResponse> items) {
            this.items = items;
            return this;
        }
        
        public Builder currentPage(int currentPage) {
            this.currentPage = currentPage;
            return this;
        }
        
        public Builder size(int size) {
            this.size = size;
            return this;
        }
        
        public Builder totalItems(long totalItems) {
            this.totalItems = totalItems;
            return this;
        }
        
        public Builder totalPages(int totalPages) {
            this.totalPages = totalPages;
            return this;
        }
        
        public Builder source(String source) {
            this.source = source;
            return this;
        }
        
        public ItemPageResponse build() {
            return new ItemPageResponse(this);
        }
    }

    public List<ItemResponse> getItems() {
        return items;
    }

    public void setItems(List<ItemResponse> items) {
        this.items = items;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }
    
    // 添加数据来源getter和setter
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    // 兼容旧版API
    public List<ItemResponse> getContent() {
        return items;
    }
    
    public void setContent(List<ItemResponse> content) {
        this.items = content;
    }
    
    public int getPage() {
        return currentPage;
    }
    
    public void setPage(int page) {
        this.currentPage = page;
    }
    
    public long getTotalElements() {
        return totalItems;
    }
    
    public void setTotalElements(long totalElements) {
        this.totalItems = totalElements;
    }
}
