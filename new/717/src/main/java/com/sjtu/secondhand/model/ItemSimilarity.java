package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 物品相似度实体，用于存储协同过滤相似度数据
 * 对应 item_similarity 表
 */
@Entity
@Table(name = "item_similarity", 
       uniqueConstraints = @UniqueConstraint(name = "uk_item_pair", columnNames = {"item_id_1", "item_id_2"}),
       indexes = @Index(name = "idx_item1_score", columnList = "item_id_1, score DESC"))
public class ItemSimilarity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "item_id_1", nullable = false)
    private Long itemId1;

    @Column(name = "item_id_2", nullable = false)
    private Long itemId2;

    @Column(name = "score", nullable = false)
    private Double score;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    // 构造函数
    public ItemSimilarity() {
    }

    public ItemSimilarity(Long itemId1, Long itemId2, Double score) {
        this.itemId1 = itemId1;
        this.itemId2 = itemId2;
        this.score = score;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemId1() {
        return itemId1;
    }

    public void setItemId1(Long itemId1) {
        this.itemId1 = itemId1;
    }

    public Long getItemId2() {
        return itemId2;
    }

    public void setItemId2(Long itemId2) {
        this.itemId2 = itemId2;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
