package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.Size;

public class UserUpdateRequest {
    
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;
    
    private String avatar_url;

    public UserUpdateRequest() {
    }

    public UserUpdateRequest(String username, String avatar_url) {
        this.username = username;
        this.avatar_url = avatar_url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAvatar_url() {
        return avatar_url;
    }

    public void setAvatar_url(String avatar_url) {
        this.avatar_url = avatar_url;
    }
} 