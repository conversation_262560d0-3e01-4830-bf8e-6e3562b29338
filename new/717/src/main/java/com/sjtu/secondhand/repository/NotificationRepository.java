package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

    // 查找用户的所有通知
    List<Notification> findByRecipient(User recipient);

    // 查找用户的所有通知（分页）
    Page<Notification> findByRecipient(User recipient, Pageable pageable);

    // 查找用户的未读通知
    List<Notification> findByRecipientAndIsReadFalse(User recipient);

    // 查找用户的未读通知（分页）
    Page<Notification> findByRecipientAndIsReadFalse(User recipient, Pageable pageable);

    // 查找用户的特定类型通知
    List<Notification> findByRecipientAndType(User recipient, Notification.NotificationType type);

    // 查找用户的特定类型通知（分页）
    Page<Notification> findByRecipientAndType(User recipient, Notification.NotificationType type, Pageable pageable);

    // 统计用户的未读通知数量
    long countByRecipientAndIsReadFalse(User recipient);
}