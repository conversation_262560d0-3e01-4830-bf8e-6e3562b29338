package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Integer> {

    /**
     * 根据父分类ID查找子分类
     * 
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<Category> findByParentId(Integer parentId);

    /**
     * 查找所有根分类（没有父分类的分类）
     * 
     * @return 根分类列表
     */
    List<Category> findByParentIdIsNull();

    /**
     * 根据名称查找分类
     * 
     * @param name 分类名称
     * @return 分类
     */
    Optional<Category> findByName(String name);
}