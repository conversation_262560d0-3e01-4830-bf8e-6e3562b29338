package com.sjtu.secondhand.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface OssStorageService {

    /**
     * 获取OSS上传签名
     *
     * @param filename 文件名
     * @param contentType 内容类型
     * @return 包含签名URL和文件访问URL的映射
     */
    Map<String, String> generatePresignedUrl(String filename, String contentType);

    /**
     * 通过服务端上传文件到OSS
     *
     * @param file 上传的文件
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file);

    /**
     * 从OSS删除文件
     *
     * @param objectName OSS中的对象名称
     * @return 是否删除成功
     */
    boolean deleteFile(String objectName);
} 