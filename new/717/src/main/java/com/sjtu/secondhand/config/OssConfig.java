package com.sjtu.secondhand.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.aliyun.oss.model.SetBucketCORSRequest.CORSRule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class OssConfig {

    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);

    @Value("${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.oss.access-key-id:LTAI5tLKLwDpsP947BPBLsnX}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret:******************************}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket-name:shareu-youyu}")
    private String bucketName;

    @Value("${aliyun.oss.bucket-domain:shareu-youyu.oss-cn-shanghai.aliyuncs.com}")
    private String bucketDomain;
    
    @Value("${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}")
    private String allowedOrigins;

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
    
    // 将CORS配置移到单独的Bean中以避免循环依赖
    @Configuration
    public static class OssCorsSetting {
        
        private static final Logger logger = LoggerFactory.getLogger(OssCorsSetting.class);
        
        @Value("${aliyun.oss.bucket-name:shareu-youyu}")
        private String bucketName;
        
        @Value("${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}")
        private String allowedOrigins;
        
        @Autowired
        private OSS ossClient;
        
        @PostConstruct
        public void initOssCorsRules() {
            try {
                logger.info("开始设置OSS CORS规则，Bucket: {}", bucketName);
                logger.info("允许的域名: {}", allowedOrigins);
                
                // 创建CORS规则
                SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);
                
                // 创建一个CORS规则
                CORSRule rule = new CORSRule();
                
                // 设置允许的来源
                List<String> origins = Arrays.asList(allowedOrigins.split(","));
                rule.setAllowedOrigins(origins);
                
                // 设置允许的方法 - 阿里云OSS只支持GET、PUT、DELETE、POST、HEAD方法
                rule.setAllowedMethods(Arrays.asList("GET", "PUT", "DELETE", "POST", "HEAD"));
                
                // 设置允许的头部
                rule.setAllowedHeaders(Arrays.asList("*"));
                
                // 设置暴露的头部
                rule.setExposeHeaders(Arrays.asList("ETag", "x-oss-request-id", "Content-Type", "Content-Length"));
                
                // 设置最大缓存时间（秒）
                rule.setMaxAgeSeconds(3600);
                
                // 创建CORS规则列表
                List<CORSRule> corsRules = new ArrayList<>();
                corsRules.add(rule);
                
                // 设置CORS规则
                request.setCorsRules(corsRules);
                
                // 设置CORS规则
                ossClient.setBucketCORS(request);
                
                logger.info("OSS CORS规则设置成功");
            } catch (Exception e) {
                logger.error("设置OSS CORS规则失败", e);
            }
        }
    }
} 