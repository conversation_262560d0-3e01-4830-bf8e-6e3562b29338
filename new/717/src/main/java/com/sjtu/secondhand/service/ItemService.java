package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.model.Item;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemService {

        /**
         * 创建新物品
         * 
         * @param itemRequest 物品请求对象
         * @return 创建的物品响应
         */
        ItemResponse createItem(ItemRequest itemRequest);

        /**
         * 根据ID获取物品详情
         * 
         * @param id 物品ID
         * @return 物品详情响应
         */
        ItemResponse getItemById(Long id);

        /**
         * 获取所有物品
         * 
         * @param pageable 分页信息
         * @return 分页物品列表
         */
        Page<Item> getAllItems(Pageable pageable);

        /**
         * 根据分类和关键词搜索物品
         * 
         * @param categoryName 分类名称
         * @param keyword      关键词
         * @param pageable     分页信息
         * @return 分页搜索结果
         */
        Page<Item> searchItems(String categoryName, String keyword, Pageable pageable);

        /**
         * 全文搜索方法 - 在名称和描述中搜索关键词
         * 
         * @param keyword  搜索关键词
         * @param pageable 分页信息
         * @return 物品分页结果
         */
        Page<Item> fullTextSearch(String keyword, Pageable pageable);

        /**
         * 高级搜索方法 - 根据多个条件进行筛选
         * 
         * @param keyword      搜索关键词（名称和描述）
         * @param categoryName 分类名称
         * @param minPrice     最低价格
         * @param maxPrice     最高价格
         * @param conditionStr 物品状况
         * @param pageable     分页信息
         * @return 物品分页结果
         */
        Page<Item> advancedSearch(
                        String keyword,
                        String categoryName,
                        Double minPrice,
                        Double maxPrice,
                        String conditionStr,
                        Pageable pageable);

        /**
         * 根据分类获取物品
         * 
         * @param categoryName 分类名称
         * @param pageable     分页信息
         * @return 分页物品列表
         */
        Page<Item> getItemsByCategory(String categoryName, Pageable pageable);

        /**
         * 获取指定用户的物品
         * 
         * @param userId   用户ID
         * @param pageable 分页信息
         * @return 分页物品列表
         */
        Page<Item> getItemsByUser(Long userId, Pageable pageable);

        /**
         * 获取当前用户的物品
         * 
         * @param pageable 分页信息
         * @return 分页物品列表
         */
        Page<Item> getMyItems(Pageable pageable);

        /**
         * 更新物品信息
         * 
         * @param id          物品ID
         * @param itemRequest 更新请求对象
         * @return 更新后的物品响应
         */
        ItemResponse updateItem(Long id, ItemRequest itemRequest);

        /**
         * 删除物品
         * 
         * @param id 物品ID
         */
        void deleteItem(Long id);

        /**
         * 标记物品为已售出
         * 
         * @param id 物品ID
         * @return 更新后的物品响应
         */
        ItemResponse markItemAsSold(Long id);

        /**
         * 获取推荐物品
         * 
         * @param categoryId 分类ID
         * @return 推荐物品列表
         */
        List<ItemResponse> getRecommendedItems(Long categoryId);

        /**
         * 将物品添加到用户的收藏列表
         * 
         * @param itemId 物品ID
         * @param userId 用户ID
         */
        void addToFavorites(Long itemId, Long userId);

        /**
         * 从用户的收藏列表中移除物品
         * 
         * @param itemId 物品ID
         * @param userId 用户ID
         */
        void removeFromFavorites(Long itemId, Long userId);

        /**
         * 获取用户的收藏物品列表
         * 
         * @param userId 用户ID
         * @return 收藏物品列表
         */
        List<ItemResponse> getFavoriteItems(Long userId);

        /**
         * 根据多个条件筛选物品
         * 
         * @param itemTypeStr 物品类型：IDLE-卖闲置, WANTED-求好物
         * @param categoryId  分类ID
         * @param priceMin    最低价格
         * @param priceMax    最高价格
         * @param keyword     搜索关键词
         * @param userId      用户ID，筛选指定用户发布的物品
         * @param pageable    分页信息
         * @return 物品分页结果
         */
        Page<Item> getFilteredItems(
                        String itemTypeStr,
                        String categoryId,
                        Double priceMin,
                        Double priceMax,
                        String keyword,
                        Long userId,
                        Pageable pageable);
                        
        /**
         * 批量处理物品列表，为每个物品设置收藏状态
         * @param items 需要处理的物品列表
         * @return 包含收藏状态的ItemResponse列表
         */
        List<ItemResponse> convertItemsWithFavoriteStatus(List<Item> items);
}
