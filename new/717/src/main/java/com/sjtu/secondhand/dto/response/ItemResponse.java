package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemCondition;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.Item.ItemType;
import com.sjtu.secondhand.model.ItemImage;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ItemResponse {

    private Long id;
    private String name;
    private String title; // 添加title字段，与name保持一致
    private String description;
    private BigDecimal price;
    private BigDecimal priceMin;
    private BigDecimal priceMax;
    private Long categoryId;
    private String categoryName;
    private Map<String, Object> category;
    private ItemCondition condition;
    private ItemType itemType;
    private ItemStatus status;
    private List<String> images;
    private Map<String, Object> seller;
    private Map<String, Object> user;
    private String createdAt;
    private String updatedAt;
    private Integer viewCount;
    private Integer favoriteCount;
    private Boolean isFavorited; // 添加isFavorited字段
    private String viewTime; // 添加浏览时间字段
    private LocalDateTime viewTimeRaw; // 添加原始浏览时间字段，用于排序

    // Constructors
    public ItemResponse() {
        this.isFavorited = false; // 默认设置为false
    }

    public ItemResponse(Item item) {
        if (item != null) {
            System.out.println(
                    "【收藏量调试】ItemResponse构造函数 - 物品ID: " + item.getId() + ", 数据库中的收藏量: " + item.getFavoriteCount());

            this.id = item.getId();
            this.name = item.getName();
            this.title = item.getName(); // 设置title与name一致
            this.description = item.getDescription();
            this.price = item.getPrice();
            this.priceMin = item.getPriceMin();
            this.priceMax = item.getPriceMax();
            if (item.getCategory() != null) {
                this.categoryId = item.getCategory().getId().longValue();
                this.categoryName = item.getCategory().getName();

                Map<String, Object> categoryMap = new HashMap<>();
                categoryMap.put("id", item.getCategory().getId());
                categoryMap.put("name", item.getCategory().getName());
                this.category = categoryMap;
            }
            this.condition = item.getCondition();
            this.itemType = item.getItemType();
            this.status = item.getStatus();

            // 转换图片列表
            if (item.getImages() != null) {
                this.images = item.getImages().stream()
                        .map(ItemImage::getUrl)
                        .collect(Collectors.toList());
            } else {
                this.images = new ArrayList<>();
            }

            // 设置用户信息
            if (item.getUser() != null) {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", item.getUser().getId());
                userMap.put("username", item.getUser().getUsername());
                userMap.put("avatar", item.getUser().getAvatarUrl());
                userMap.put("rating", item.getUser().getRating());
                this.user = userMap;

                // 为了兼容性，同时设置seller字段，确保包含avatar属性
                Map<String, Object> sellerMap = new HashMap<>();
                sellerMap.put("id", item.getUser().getId());
                sellerMap.put("username", item.getUser().getUsername());
                sellerMap.put("avatar", item.getUser().getAvatarUrl()); // 确保包含avatar属性
                sellerMap.put("rating", item.getUser().getRating());
                this.seller = sellerMap;
            }

            this.viewCount = item.getViewCount();
            this.favoriteCount = item.getFavoriteCount();

            // 初始化收藏状态为false，稍后会由专门的方法设置正确的值
            this.isFavorited = false;

            System.out.println("【收藏量调试】ItemResponse构造函数 - 设置收藏量为: " + this.favoriteCount);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.createdAt = item.getCreatedAt() != null ? item.getCreatedAt().format(formatter) : null;
            this.updatedAt = item.getUpdatedAt() != null ? item.getUpdatedAt().format(formatter) : null;
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceMin() {
        return priceMin;
    }

    public void setPriceMin(BigDecimal priceMin) {
        this.priceMin = priceMin;
    }

    public BigDecimal getPriceMax() {
        return priceMax;
    }

    public void setPriceMax(BigDecimal priceMax) {
        this.priceMax = priceMax;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Map<String, Object> getCategory() {
        return category;
    }

    public void setCategory(Map<String, Object> category) {
        this.category = category;
    }

    public ItemCondition getCondition() {
        return condition;
    }

    public void setCondition(ItemCondition condition) {
        this.condition = condition;
    }

    public ItemType getItemType() {
        return itemType;
    }

    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }

    public ItemStatus getStatus() {
        return status;
    }

    public void setStatus(ItemStatus status) {
        this.status = status;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public Map<String, Object> getSeller() {
        return seller;
    }

    public void setSeller(Map<String, Object> seller) {
        this.seller = seller;
    }

    public Map<String, Object> getUser() {
        return user;
    }

    public void setUser(Map<String, Object> user) {
        this.user = user;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        if (createdAt != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.createdAt = createdAt.format(formatter);
        } else {
            this.createdAt = null;
        }
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        if (updatedAt != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.updatedAt = updatedAt.format(formatter);
        } else {
            this.updatedAt = null;
        }
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    // 添加title的getter和setter
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    // 添加isFavorited的getter和setter
    public Boolean getIsFavorited() {
        return isFavorited;
    }

    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }

    // 新增 viewTime 相关的 getter 和 setter
    public String getViewTime() {
        return viewTime;
    }

    public void setViewTime(String viewTime) {
        this.viewTime = viewTime;
    }

    public void setViewTime(LocalDateTime viewTime) {
        if (viewTime != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.viewTime = viewTime.format(formatter);
            this.viewTimeRaw = viewTime;
        } else {
            this.viewTime = null;
            this.viewTimeRaw = null;
        }
    }

    public LocalDateTime getViewTimeRaw() {
        return viewTimeRaw;
    }

    public void setViewTimeRaw(LocalDateTime viewTimeRaw) {
        this.viewTimeRaw = viewTimeRaw;
    }
}