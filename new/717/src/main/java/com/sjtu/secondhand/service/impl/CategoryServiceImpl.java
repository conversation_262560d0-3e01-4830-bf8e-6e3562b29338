package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CategoryServiceImpl implements CategoryService {

    private final CategoryRepository categoryRepository;

    @Autowired
    public CategoryServiceImpl(CategoryRepository categoryRepository) {
        this.categoryRepository = categoryRepository;
    }

    @Override
    public List<Category> getAllCategories() {
        return categoryRepository.findAll();
    }

    @Override
    public List<Category> getCategoryTree() {
        List<Category> allCategories = categoryRepository.findAll();
        List<Category> rootCategories = new ArrayList<>();
        Map<Integer, Category> categoryMap = new HashMap<>();

        // 将所有分类添加到map中
        for (Category category : allCategories) {
            categoryMap.put(category.getId(), category);
        }

        // 构建树形结构
        for (Category category : allCategories) {
            if (category.getParent() == null) {
                rootCategories.add(category);
            } else {
                Category parent = category.getParent();
                if (parent != null) {
                    parent.addChild(category);
                }
            }
        }

        return rootCategories;
    }

    @Override
    public Category getCategoryById(Long id) {
        // 将Long类型转换为Integer类型
        Integer idInt = id.intValue();
        return categoryRepository.findById(idInt)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
    }

    @Override
    public Category createCategory(Category category) {
        return categoryRepository.save(category);
    }

    @Override
    public Category updateCategory(Long id, Category categoryDetails) {
        // 将Long类型转换为Integer类型
        Integer idInt = id.intValue();
        Category category = categoryRepository.findById(idInt)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        category.setName(categoryDetails.getName());
        category.setParent(categoryDetails.getParent());
        return categoryRepository.save(category);
    }

    @Override
    public void deleteCategory(Long id) {
        // 将Long类型转换为Integer类型
        Integer idInt = id.intValue();
        Category category = categoryRepository.findById(idInt)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        categoryRepository.delete(category);
    }
}