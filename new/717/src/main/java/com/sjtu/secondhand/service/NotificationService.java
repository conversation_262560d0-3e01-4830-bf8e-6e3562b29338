package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.response.NotificationDto;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Notification.NotificationType;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.User;

import java.util.List;

public interface NotificationService {

        // 创建通知
        NotificationDto createNotification(User recipient, String content,
                        NotificationType type, Long relatedEntityId);

        // 创建带发送者的通知
        NotificationDto createNotification(User recipient, User sender, NotificationType type,
                        Long relatedEntityId, String content);

        // 获取用户的所有通知
        List<NotificationDto> getAllNotifications(Long userId);

        // 获取用户的未读通知
        List<NotificationDto> getUnreadNotifications(Long userId);

        // 将通知标记为已读
        NotificationDto markAsRead(Long notificationId, Long userId);

        // 将用户的所有通知标记为已读
        void markAllAsRead(Long userId);

        // 删除通知
        void deleteNotification(Long notificationId, Long userId);

        // 获取用户的未读通知数量
        long countUnreadNotifications(Long userId);

        // 创建评价收到通知
        void createRatingReceivedNotification(Long ratingId, User ratee, User rater);

        // 创建新订单通知
        void createNewOrderNotification(Order order);

        // 创建订单确认通知
        void createOrderConfirmedNotification(Order order);

        // 创建订单取消通知
        void createOrderCancelledNotification(Order order, User canceller);

        // 创建订单完成通知
        void createOrderCompletedNotification(Order order);

        // 创建买家确认联系通知
        void createContactConfirmedNotification(Order order);

        // 创建新报价通知
        void createNewOfferNotification(Offer offer);

        // 创建报价接受通知
        void createOfferAcceptedNotification(Offer offer);

        // 创建报价拒绝通知
        void createOfferRejectedNotification(Offer offer);

        // 创建报价确认通知
        void createOfferConfirmedNotification(Offer offer);
}