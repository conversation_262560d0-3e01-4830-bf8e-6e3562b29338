package com.sjtu.secondhand.service;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

public interface FileStorageService {

    /**
     * 初始化文件存储目录
     */
    void init();

    /**
     * 存储上传的文件
     * 
     * @param file 上传的文件
     * @return 存储后的文件名
     */
    String storeFile(MultipartFile file);

    /**
     * 加载文件作为资源
     * 
     * @param filename 文件名
     * @return 文件资源
     */
    Resource loadFileAsResource(String filename);

    /**
     * 删除文件
     * 
     * @param filename 文件名
     * @return 是否删除成功
     */
    boolean deleteFile(String filename);
}