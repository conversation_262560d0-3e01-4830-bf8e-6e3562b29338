package com.sjtu.secondhand.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.service.NotificationEventService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知事件服务实现类
 * 基于Server-Sent Events (SSE)实现实时通知
 */
@Service
public class NotificationEventServiceImpl implements NotificationEventService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationEventServiceImpl.class);

    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher eventPublisher;

    // 存储用户ID与SSE发射器的映射关系
    private final Map<Long, SseEmitter> userEmitters = new ConcurrentHashMap<>();

    @Autowired
    public NotificationEventServiceImpl(ObjectMapper objectMapper, ApplicationEventPublisher eventPublisher) {
        this.objectMapper = objectMapper;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 注册用户的SSE发射器
     * 
     * @param userId  用户ID
     * @param emitter SSE发射器
     */
    public void addEmitter(Long userId, SseEmitter emitter) {
        userEmitters.put(userId, emitter);

        // 设置超时回调
        emitter.onTimeout(() -> {
            logger.info("SSE connection timeout for user {}", userId);
            userEmitters.remove(userId);
        });

        // 设置完成回调
        emitter.onCompletion(() -> {
            logger.info("SSE connection completed for user {}", userId);
            userEmitters.remove(userId);
        });

        // 设置错误回调
        emitter.onError((ex) -> {
            logger.error("SSE error for user {}: {}", userId, ex.getMessage());
            userEmitters.remove(userId);
        });
    }

    /**
     * 移除用户的SSE发射器
     * 
     * @param userId 用户ID
     */
    public void removeEmitter(Long userId) {
        userEmitters.remove(userId);
    }

    @Override
    public void sendOrderUpdateEvent(Order order, String eventType, String message) {
        try {
            // 向买家发送通知
            sendToUser(order.getBuyer().getId(), "order", Map.of(
                    "orderId", order.getId(),
                    "eventType", eventType,
                    "message", message,
                    "timestamp", System.currentTimeMillis()));

            // 向卖家发送通知
            sendToUser(order.getSeller().getId(), "order", Map.of(
                    "orderId", order.getId(),
                    "eventType", eventType,
                    "message", message,
                    "timestamp", System.currentTimeMillis()));
        } catch (Exception e) {
            logger.error("Failed to send order update event: {}", e.getMessage());
        }
    }

    @Override
    public void sendNotificationEvent(Notification notification) {
        try {
            // 向接收者发送通知
            sendToUser(notification.getRecipient().getId(), "notification", Map.of(
                    "id", notification.getId(),
                    "type", notification.getType().toString(),
                    "content", notification.getContent(),
                    "timestamp", notification.getCreatedAt()));
        } catch (Exception e) {
            logger.error("Failed to send notification event: {}", e.getMessage());
        }
    }

    /**
     * 向指定用户发送事件数据
     * 
     * @param userId    用户ID
     * @param eventName 事件名称
     * @param data      事件数据
     */
    private void sendToUser(Long userId, String eventName, Object data) {
        SseEmitter emitter = userEmitters.get(userId);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .name(eventName)
                        .data(data, MediaType.APPLICATION_JSON));
            } catch (IOException e) {
                logger.error("Failed to send event to user {}: {}", userId, e.getMessage());
                userEmitters.remove(userId);
            }
        }
    }
}
