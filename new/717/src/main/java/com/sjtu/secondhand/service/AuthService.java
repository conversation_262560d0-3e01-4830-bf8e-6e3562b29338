package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.request.LoginRequest;
import com.sjtu.secondhand.dto.request.RegisterRequest;
import com.sjtu.secondhand.dto.response.JwtAuthResponse;

public interface AuthService {
    
    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return JWT认证响应
     */
    JwtAuthResponse login(LoginRequest loginRequest);
    
    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     */
    String register(RegisterRequest registerRequest);
}
