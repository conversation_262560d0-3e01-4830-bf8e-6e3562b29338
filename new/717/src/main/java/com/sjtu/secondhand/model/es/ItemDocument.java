package com.sjtu.secondhand.model.es;

import com.sjtu.secondhand.model.Item;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Document(indexName = "items")
@Setting(
        settingPath = "es-settings.json"
)
public class ItemDocument {

    @Id
    private Long id;

    @Field(type = FieldType.Text, analyzer = "ik_max_word_synonym", searchAnalyzer = "ik_smart_synonym")
    private String name;

    @Field(type = FieldType.Text, analyzer = "ik_max_word_synonym", searchAnalyzer = "ik_smart_synonym")
    private String description;

    @Field(type = FieldType.Double)
    private BigDecimal price;

    @Field(type = FieldType.Double)
    private BigDecimal priceMin;

    @Field(type = FieldType.Double)
    private BigDecimal priceMax;

    @Field(type = FieldType.Keyword)
    private String itemType;

    @Field(type = FieldType.Keyword)
    private String condition;

    @Field(type = FieldType.Keyword)
    private String status;

    @Field(type = FieldType.Boolean)
    private Boolean isVisible;

    @Field(type = FieldType.Integer)
    private Integer viewCount;

    @Field(type = FieldType.Integer)
    private Integer favoriteCount;

    @Field(type = FieldType.Object)
    private CategoryInfo category;

    @Field(type = FieldType.Object)
    private UserInfo user;

    @Field(type = FieldType.Date)
    private LocalDateTime createdAt;

    @Field(type = FieldType.Date)
    private LocalDateTime updatedAt;

    @Field(type = FieldType.Keyword)
    private List<String> imageUrls;

    // 内部静态类，用于存储分类信息
    public static class CategoryInfo {
        private Long id;
        private String name;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    // 内部静态类，用于存储用户信息
    public static class UserInfo {
        private Long id;
        private String username;
        private String avatarUrl;
        private Double rating;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public Double getRating() {
            return rating;
        }

        public void setRating(Double rating) {
            this.rating = rating;
        }
    }

    // 从Item实体转换为ES文档
    public static ItemDocument fromItem(Item item) {
        ItemDocument document = new ItemDocument();
        document.setId(item.getId());
        document.setName(item.getName());
        document.setDescription(item.getDescription());
        document.setPrice(item.getPrice());
        document.setPriceMin(item.getPriceMin());
        document.setPriceMax(item.getPriceMax());
        document.setItemType(item.getItemType() != null ? item.getItemType().name() : null);
        document.setCondition(item.getCondition() != null ? item.getCondition().name() : null);
        document.setStatus(item.getStatus() != null ? item.getStatus().name() : null);
        document.setIsVisible(item.getIsVisible());
        document.setViewCount(item.getViewCount());
        document.setFavoriteCount(item.getFavoriteCount());
        document.setCreatedAt(item.getCreatedAt());
        document.setUpdatedAt(item.getUpdatedAt());

        // 设置分类信息
        if (item.getCategory() != null) {
            CategoryInfo categoryInfo = new CategoryInfo();
            categoryInfo.setId(Long.valueOf(item.getCategory().getId()));
            categoryInfo.setName(item.getCategory().getName());
            document.setCategory(categoryInfo);
        }

        // 设置用户信息
        if (item.getUser() != null) {
            UserInfo userInfo = new UserInfo();
            userInfo.setId(item.getUser().getId());
            userInfo.setUsername(item.getUser().getUsername());
            userInfo.setAvatarUrl(item.getUser().getAvatarUrl());
            userInfo.setRating(Double.valueOf(item.getUser().getRating()));
            document.setUser(userInfo);
        }

        // 设置图片URL
        if (item.getImages() != null && !item.getImages().isEmpty()) {
            document.setImageUrls(item.getImages().stream()
                    .map(img -> img.getUrl())
                    .toList());
        }

        return document;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceMin() {
        return priceMin;
    }

    public void setPriceMin(BigDecimal priceMin) {
        this.priceMin = priceMin;
    }

    public BigDecimal getPriceMax() {
        return priceMax;
    }

    public void setPriceMax(BigDecimal priceMax) {
        this.priceMax = priceMax;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    public CategoryInfo getCategory() {
        return category;
    }

    public void setCategory(CategoryInfo category) {
        this.category = category;
    }

    public UserInfo getUser() {
        return user;
    }

    public void setUser(UserInfo user) {
        this.user = user;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
} 