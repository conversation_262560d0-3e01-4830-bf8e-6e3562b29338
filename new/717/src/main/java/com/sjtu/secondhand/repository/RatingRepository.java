package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Rating;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RatingRepository extends JpaRepository<Rating, Long> {

    // 查询用户作为评价者的评价
    Page<Rating> findByRater(User rater, Pageable pageable);

    // 查询用户作为被评价者的评价
    Page<Rating> findByRatee(User ratee, Pageable pageable);

    // 按交易类型查询用户作为评价者的评价
    Page<Rating> findByRaterAndTransactionType(User rater, Rating.TransactionType transactionType, Pageable pageable);

    // 按交易类型查询用户作为被评价者的评价
    Page<Rating> findByRateeAndTransactionType(User ratee, Rating.TransactionType transactionType, Pageable pageable);

    // 查询订单的评价
    @Query("SELECT r FROM Rating r WHERE r.relatedTransactionId = :transactionId AND r.rater = :rater")
    Optional<Rating> findByRelatedTransactionIdAndRater(@Param("transactionId") Long transactionId, @Param("rater") User rater);

    // 检查用户是否已经对某个交易进行了评价
    @Query("SELECT COUNT(r) > 0 FROM Rating r WHERE r.relatedTransactionId = :transactionId AND r.rater = :rater")
    boolean existsByRelatedTransactionIdAndRater(@Param("transactionId") Long transactionId, @Param("rater") User rater);

    // 计算用户的平均评分
    @Query("SELECT AVG(r.score) FROM Rating r WHERE r.ratee = :user")
    Double calculateAverageRating(@Param("user") User user);

    // 获取用户的最近评价
    @Query("SELECT r FROM Rating r WHERE r.ratee = :user ORDER BY r.createdAt DESC")
    List<Rating> findRecentRatingsByUser(@Param("user") User user, Pageable pageable);

    // 按交易类型和评分范围查询评价
    @Query("SELECT r FROM Rating r WHERE r.transactionType = :type AND r.score BETWEEN :minScore AND :maxScore")
    Page<Rating> findByTransactionTypeAndScoreBetween(
            @Param("type") Rating.TransactionType type,
            @Param("minScore") Byte minScore,
            @Param("maxScore") Byte maxScore,
            Pageable pageable);
}