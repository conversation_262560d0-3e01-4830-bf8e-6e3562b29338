package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Order;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class OrderResponse {

    private Long id;
    private Map<String, Object> item;
    private Map<String, Object> buyer;
    private Map<String, Object> seller;
    private String status;
    private Boolean isBuyerRated;
    private Boolean isSellerRated;
    private String createdAt;
    private String updatedAt;

    public OrderResponse() {
    }

    public OrderResponse(Order order) {
        this.id = order.getId();
        this.status = order.getStatus().name();
        this.isBuyerRated = order.getIsBuyerRated();
        this.isSellerRated = order.getIsSellerRated();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        this.createdAt = order.getCreatedAt() != null ? order.getCreatedAt().format(formatter) : null;
        this.updatedAt = order.getUpdatedAt() != null ? order.getUpdatedAt().format(formatter) : null;

        // 设置商品信息
        this.item = new HashMap<>();
        this.item.put("id", order.getItem().getId());
        this.item.put("name", order.getItem().getName());
        this.item.put("description", order.getItem().getDescription());
        this.item.put("price", order.getItem().getPrice());
        if (order.getItem().getCategory() != null) {
            this.item.put("categoryId", order.getItem().getCategory().getId());
            this.item.put("categoryName", order.getItem().getCategory().getName());
        }
        this.item.put("condition", order.getItem().getCondition().name());
        if (order.getItem().getImages() != null) {
            this.item.put("images", order.getItem().getImages().stream()
                    .map(image -> image.getUrl())
                    .collect(Collectors.toList()));
        }

        // 设置买家信息
        this.buyer = new HashMap<>();
        this.buyer.put("id", order.getBuyer().getId());
        this.buyer.put("username", order.getBuyer().getUsername());
        this.buyer.put("avatar", order.getBuyer().getAvatarUrl());

        // 设置卖家信息
        this.seller = new HashMap<>();
        this.seller.put("id", order.getSeller().getId());
        this.seller.put("username", order.getSeller().getUsername());
        this.seller.put("avatar", order.getSeller().getAvatarUrl());
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Map<String, Object> getItem() {
        return item;
    }

    public void setItem(Map<String, Object> item) {
        this.item = item;
    }

    public Map<String, Object> getBuyer() {
        return buyer;
    }

    public void setBuyer(Map<String, Object> buyer) {
        this.buyer = buyer;
    }

    public Map<String, Object> getSeller() {
        return seller;
    }

    public void setSeller(Map<String, Object> seller) {
        this.seller = seller;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsBuyerRated() {
        return isBuyerRated;
    }

    public void setIsBuyerRated(Boolean isBuyerRated) {
        this.isBuyerRated = isBuyerRated;
    }

    public Boolean getIsSellerRated() {
        return isSellerRated;
    }

    public void setIsSellerRated(Boolean isSellerRated) {
        this.isSellerRated = isSellerRated;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}