package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Footprint;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.FootprintRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.FootprintService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FootprintServiceImpl implements FootprintService {

    private static final Logger log = LoggerFactory.getLogger(FootprintServiceImpl.class);

    private final FootprintRepository footprintRepository;
    private final UserRepository userRepository;
    private final ItemRepository itemRepository;
    
    public FootprintServiceImpl(FootprintRepository footprintRepository, UserRepository userRepository, ItemRepository itemRepository) {
        this.footprintRepository = footprintRepository;
        this.userRepository = userRepository;
        this.itemRepository = itemRepository;
    }

    @Override
    @Transactional
    public boolean recordFootprint(Long userId, Long itemId) {
        try {
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));

            Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "商品不存在"));

            // 查询是否已有足迹记录
            Optional<Footprint> existingFootprint = footprintRepository.findByUserAndItemId(user, itemId);
            
            if (existingFootprint.isPresent()) {
                // 更新已有记录的时间
                Footprint footprint = existingFootprint.get();
                footprint.setViewTime(LocalDateTime.now());
                footprintRepository.save(footprint);
            } else {
                // 创建新的足迹记录
                Footprint footprint = Footprint.builder()
                    .user(user)
                    .item(item)
                    .viewTime(LocalDateTime.now())
                    .build();
                footprintRepository.save(footprint);
            }
            
            return true;
        } catch (Exception e) {
            log.error("记录足迹失败", e);
            return false;
        }
    }

    @Override
    public ItemPageResponse getUserFootprints(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));

        // 查询足迹
        Page<Footprint> footprintPage = footprintRepository.findByUserOrderByViewTimeDesc(user, pageable);
        
        // 转换为商品列表
        List<ItemResponse> items = footprintPage.getContent().stream()
            .map(footprint -> {
                Item item = footprint.getItem();
                ItemResponse response = new ItemResponse(item);
                // 设置查看时间
                response.setViewTime(footprint.getViewTime());
                return response;
            })
            .collect(Collectors.toList());
            
        // 构建分页响应
        return ItemPageResponse.builder()
            .items(items)
            .totalPages(footprintPage.getTotalPages())
            .totalItems(footprintPage.getTotalElements())
            .currentPage(pageable.getPageNumber())
            .build();
    }

    @Override
    @Transactional
    public boolean deleteFootprint(Long userId, Long itemId) {
        try {
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));
                
            footprintRepository.deleteByUserAndItemId(user, itemId);
            return true;
        } catch (Exception e) {
            log.error("删除足迹失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean clearAllFootprints(Long userId) {
        try {
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "用户不存在"));
                
            footprintRepository.deleteAllByUser(user);
            return true;
        } catch (Exception e) {
            log.error("清空足迹失败", e);
            return false;
        }
    }
}
