package com.sjtu.secondhand.service;

import com.sjtu.secondhand.model.Category;
import java.util.List;

public interface CategoryService {

    /**
     * 获取所有分类
     * 
     * @return 分类列表
     */
    List<Category> getAllCategories();

    /**
     * 获取分类树（包含父子关系）
     * 
     * @return 分类树
     */
    List<Category> getCategoryTree();

    /**
     * 根据ID获取分类
     * 
     * @param id 分类ID
     * @return 分类
     */
    Category getCategoryById(Long id);

    /**
     * 创建分类
     * 
     * @param category 分类
     * @return 创建的分类
     */
    Category createCategory(Category category);

    /**
     * 更新分类
     * 
     * @param id       分类ID
     * @param category 分类
     * @return 更新的分类
     */
    Category updateCategory(Long id, Category category);

    /**
     * 删除分类
     * 
     * @param id 分类ID
     */
    void deleteCategory(Long id);
}