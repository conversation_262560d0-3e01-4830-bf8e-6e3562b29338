package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.service.OfferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Tag(name = "求购响应 (Offers)", description = "求购响应相关接口")
public class OfferController {

    private final OfferService offerService;

    public OfferController(OfferService offerService) {
        this.offerService = offerService;
    }

    @PostMapping("/items/{wanted_item_id}/offers")
    @Operation(summary = "响应求购贴 (提交Offer)", description = "有物者对一个 'WANTED' 类型的物品（求购贴）进行响应，创建一个Offer。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<OfferResponse>> createOffer(
            @PathVariable("wanted_item_id") Long wantedItemId,
            @Valid @RequestBody OfferRequest offerRequest) {
        // 直接将求购物品ID传递给服务层
        OfferResponse offerResponse = offerService.createOffer(wantedItemId, offerRequest);
        return new ResponseEntity<>(ApiResponse.success("Offer提交成功", offerResponse), HttpStatus.CREATED);
    }

    @PostMapping("/offers/{id}/accept")
    @Operation(summary = "[求购者] 接受Offer", description = "求购者操作。接受某个Offer，将 'PENDING_ACCEPTANCE' 状态的Offer流转至 'ACCEPTED'，同时物品状态变为 'RESERVED'。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<OfferResponse>> acceptOffer(@PathVariable("id") Long offerId) {
        OfferResponse offerResponse = offerService.acceptOffer(offerId);
        return ResponseEntity.ok(ApiResponse.success("操作成功", offerResponse));
    }

    @PostMapping("/offers/{id}/reject")
    @Operation(summary = "[求购者] 拒绝Offer", description = "求购者操作。拒绝此Offer，状态流转至 'REJECTED'。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<OfferResponse>> rejectOffer(@PathVariable("id") Long offerId) {
        OfferResponse offerResponse = offerService.rejectOffer(offerId);
        return ResponseEntity.ok(ApiResponse.success("操作成功", offerResponse));
    }

    @PostMapping("/offers/{id}/confirm")
    @Operation(summary = "[响应者] 确认交易", description = "响应者（有物者）操作。在求购者接受后进行最终确认，状态流转至 'CONFIRMED'。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<OfferResponse>> confirmOffer(@PathVariable("id") Long offerId) {
        OfferResponse offerResponse = offerService.confirmOffer(offerId);
        return ResponseEntity.ok(ApiResponse.success("操作成功", offerResponse));
    }

    @PostMapping("/offers/{id}/cancel")
    @Operation(summary = "[求购/响应者] 取消Offer", description = "在交易完成前取消Offer，状态流转至 'CANCELLED'。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<OfferResponse>> cancelOffer(@PathVariable("id") Long offerId) {
        OfferResponse offerResponse = offerService.cancelOffer(offerId);
        return ResponseEntity.ok(ApiResponse.success("操作成功", offerResponse));
    }
} 