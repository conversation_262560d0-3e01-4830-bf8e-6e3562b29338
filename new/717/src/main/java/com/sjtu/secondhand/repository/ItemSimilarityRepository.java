package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.ItemSimilarity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 物品相似度存储库接口
 */
@Repository
public interface ItemSimilarityRepository extends JpaRepository<ItemSimilarity, Long> {

    /**
     * 查找指定物品的最相似物品列表
     * 
     * @param itemId 基准物品ID
     * @param limit  结果数量限制
     * @return 相似物品列表，按相似度分数降序排序
     */
    @Query(value = "SELECT * FROM item_similarity WHERE item_id_1 = :itemId ORDER BY score DESC LIMIT :limit", nativeQuery = true)
    List<ItemSimilarity> findMostSimilarItems(@Param("itemId") Long itemId, @Param("limit") int limit);

    /**
     * 查找多个物品的相似物品列表
     * 
     * @param itemIds 物品ID列表
     * @param limit   结果数量限制
     * @return 相似物品列表，按相似度分数降序排序
     */
    @Query(value = "SELECT * FROM item_similarity WHERE item_id_1 IN :itemIds ORDER BY score DESC LIMIT :limit", nativeQuery = true)
    List<ItemSimilarity> findSimilarItemsForMultipleItems(@Param("itemIds") List<Long> itemIds,
            @Param("limit") int limit);
}
