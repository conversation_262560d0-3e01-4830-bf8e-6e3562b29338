package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Notification;

import java.time.LocalDateTime;

public class NotificationDto {

    private Long id;
    private Notification.NotificationType type;
    private String content;
    private Boolean isRead;
    private LocalDateTime createdAt;
    private Long relatedEntityId;
    private Long senderId;
    private String senderUsername;

    // 添加前端期望的字段
    private String title; // 通知标题
    private boolean is_read; // 与前端字段名保持一致

    // 添加订单确认通知所需的字段
    private String sellerEmail; // 卖家邮箱
    private String sellerUsername; // 卖家用户名
    private String itemName; // 商品名称

    // Constructors
    public NotificationDto() {
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Notification.NotificationType getType() {
        return type;
    }

    public void setType(Notification.NotificationType type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
        this.is_read = isRead; // 同时更新前端期望的字段
    }

    // 前端期望的is_read getter和setter
    public boolean getIs_read() {
        return is_read;
    }

    public void setIs_read(boolean is_read) {
        this.is_read = is_read;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Long getRelatedEntityId() {
        return relatedEntityId;
    }

    public void setRelatedEntityId(Long relatedEntityId) {
        this.relatedEntityId = relatedEntityId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSenderUsername() {
        return senderUsername;
    }

    public void setSenderUsername(String senderUsername) {
        this.senderUsername = senderUsername;
    }

    // 标题的getter和setter
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    // 卖家邮箱的getter和setter
    public String getSellerEmail() {
        return sellerEmail;
    }

    public void setSellerEmail(String sellerEmail) {
        this.sellerEmail = sellerEmail;
    }

    // 卖家用户名的getter和setter
    public String getSellerUsername() {
        return sellerUsername;
    }

    public void setSellerUsername(String sellerUsername) {
        this.sellerUsername = sellerUsername;
    }

    // 商品名称的getter和setter
    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}