package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

    Page<Order> findByBuyer(User buyer, Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.seller = :seller")
    Page<Order> findBySeller(@Param("seller") User seller, Pageable pageable);

    Optional<Order> findByItemAndBuyer(Item item, User buyer);

    @Query("SELECT o FROM Order o WHERE o.seller = :seller AND o.status = :status")
    Page<Order> findBySellerAndStatus(@Param("seller") User seller, @Param("status") Order.OrderStatus status,
            Pageable pageable);

    Page<Order> findByBuyerAndStatus(User buyer, Order.OrderStatus status, Pageable pageable);

    boolean existsByItemAndStatus(Item item, Order.OrderStatus status);

    @Query("SELECT COUNT(o) > 0 FROM Order o WHERE o.item = :item AND o.status IN ('PENDING_CONFIRMATION', 'AWAITING_ACKNOWLEDGEMENT', 'CONFIRMED')")
    boolean existsActiveOrderForItem(@Param("item") Item item);

    List<Order> findByItemId(Long itemId);

    @Modifying
    @Query("DELETE FROM Order o WHERE o.item.id = :itemId")
    void deleteByItemId(@Param("itemId") Long itemId);

    // 查询买家未评价的已完成订单
    Page<Order> findByBuyerAndStatusAndIsBuyerRatedFalse(User buyer, Order.OrderStatus status, Pageable pageable);

    // 查询卖家未评价的已完成订单
    Page<Order> findBySellerAndStatusAndIsSellerRatedFalse(User seller, Order.OrderStatus status, Pageable pageable);
}