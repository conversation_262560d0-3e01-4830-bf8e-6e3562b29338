package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.security.JwtTokenProvider;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.UserService;
import com.sjtu.secondhand.service.impl.NotificationEventServiceImpl;

import io.jsonwebtoken.JwtException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/events")
@Tag(name = "事件流 (Event Stream)", description = "Server-Sent Events (SSE) 实时通知接口")
public class EventStreamController {

    private static final Logger logger = LoggerFactory.getLogger(EventStreamController.class);
    private final UserService userService;
    private final NotificationEventServiceImpl notificationEventService;
    private final JwtTokenProvider jwtTokenProvider;
    private final UserDetailsService userDetailsService;

    // SSE连接超时设置，2小时
    private static final long SSE_TIMEOUT_MS = TimeUnit.HOURS.toMillis(2);

    @Autowired
    public EventStreamController(UserService userService,
            NotificationEventServiceImpl notificationEventService,
            JwtTokenProvider jwtTokenProvider,
            UserDetailsService userDetailsService) {
        this.userService = userService;
        this.notificationEventService = notificationEventService;
        this.jwtTokenProvider = jwtTokenProvider;
        this.userDetailsService = userDetailsService;
    }

    /**
     * 创建SSE连接，为当前登录用户建立实时通知流
     * 支持从URL参数和Authorization头获取token
     * 
     * @param token JWT token (可选，如果不提供则从Authorization头获取)
     * @return SseEmitter - 服务器发送事件发射器
     */
    @GetMapping(path = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "订阅实时通知", description = "创建一个Server-Sent Events连接，接收实时通知和订单更新")
    public SseEmitter stream(@RequestParam(required = false) String token) {
        User currentUser = null;

        try {
            // 首先尝试获取当前认证的用户
            currentUser = userService.getCurrentUser();
        } catch (Exception e) {
            // 如果从SecurityContext获取失败，尝试从URL参数token获取
            if (token != null && !token.isEmpty()) {
                try {
                    if (jwtTokenProvider.validateToken(token)) {
                        String username = jwtTokenProvider.getUsername(token);
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                        Long userId = jwtTokenProvider.getUserId(token);
                        currentUser = userService.getUserById(userId);
                        logger.info("通过URL参数token获取用户: {}", username);
                    }
                } catch (JwtException e2) {
                    logger.error("JWT Token验证失败: {}", e2.getMessage());
                }
            }
        }

        // 如果仍然获取不到用户，抛出异常
        if (currentUser == null) {
            logger.warn("未认证的用户尝试创建SSE连接");
            throw new IllegalStateException("用户未认证");
        }

        Long userId = currentUser.getId();
        logger.info("用户 {} 创建了SSE连接", userId);

        // 创建SSE发射器，设置超时时间
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_MS);

        // 注册到通知事件服务
        notificationEventService.addEmitter(userId, emitter);

        // 发送初始事件表示连接成功
        try {
            emitter.send(SseEmitter.event()
                    .name("connect")
                    .data("{\"status\":\"connected\",\"userId\":" + userId + ",\"timestamp\":"
                            + System.currentTimeMillis() + "}"));
        } catch (Exception e) {
            logger.error("发送SSE连接确认事件失败: {}", e.getMessage());
        }

        return emitter;
    }
}
