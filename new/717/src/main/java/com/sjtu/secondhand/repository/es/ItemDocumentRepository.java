package com.sjtu.secondhand.repository.es;

import com.sjtu.secondhand.model.es.ItemDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ItemDocumentRepository extends ElasticsearchRepository<ItemDocument, Long> {

    /**
     * 全文搜索物品
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 物品分页结果
     */
    @Query("{\"bool\": {\"must\": [{\"bool\": {\"should\": [" +
           "{\"match\": {\"name\": {\"query\": \"?0\", \"boost\": 3.0}}}, " +
           "{\"match\": {\"description\": {\"query\": \"?0\", \"boost\": 1.0}}}, " + 
           "{\"match_phrase\": {\"name\": {\"query\": \"?0\", \"boost\": 5.0}}}, " +
           "{\"match_phrase\": {\"description\": {\"query\": \"?0\", \"boost\": 2.0}}}" +
           "], \"minimum_should_match\": 1}}, {\"term\": {\"isVisible\": true}}]}}")
    Page<ItemDocument> searchByKeyword(String keyword, Pageable pageable);

    /**
     * 高级搜索物品
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param itemType 物品类型
     * @param condition 物品状况
     * @param pageable 分页参数
     * @return 物品分页结果
     */
    Page<ItemDocument> findByIsVisibleTrueAndNameContainingOrDescriptionContaining(String name, String description, Pageable pageable);
} 