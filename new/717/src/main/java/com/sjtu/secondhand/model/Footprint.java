package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户浏览足迹实体类
 */
@Entity
@Table(name = "footprints", uniqueConstraints = {
    @UniqueConstraint(name = "uk_user_item", columnNames = {"user_id", "item_id"})
})
public class Footprint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

    @Column(name = "view_time", nullable = false)
    private LocalDateTime viewTime;

    @PrePersist
    protected void onCreate() {
        viewTime = LocalDateTime.now();
    }
    
    // 默认构造函数
    public Footprint() {
    }
    
    // 全参数构造函数
    public Footprint(Long id, User user, Item item, LocalDateTime viewTime) {
        this.id = id;
        this.user = user;
        this.item = item;
        this.viewTime = viewTime;
    }
    
    // 静态 builder 方法
    public static FootprintBuilder builder() {
        return new FootprintBuilder();
    }
    
    // Builder 类
    public static class FootprintBuilder {
        private Long id;
        private User user;
        private Item item;
        private LocalDateTime viewTime;
        
        public FootprintBuilder id(Long id) {
            this.id = id;
            return this;
        }
        
        public FootprintBuilder user(User user) {
            this.user = user;
            return this;
        }
        
        public FootprintBuilder item(Item item) {
            this.item = item;
            return this;
        }
        
        public FootprintBuilder viewTime(LocalDateTime viewTime) {
            this.viewTime = viewTime;
            return this;
        }
        
        public Footprint build() {
            return new Footprint(id, user, item, viewTime);
        }
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public Item getItem() {
        return item;
    }
    
    public void setItem(Item item) {
        this.item = item;
    }
    
    public LocalDateTime getViewTime() {
        return viewTime;
    }
    
    public void setViewTime(LocalDateTime viewTime) {
        this.viewTime = viewTime;
    }
}
