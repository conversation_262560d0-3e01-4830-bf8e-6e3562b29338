package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {

    /**
     * 查找物品的所有一级评论（没有父评论的评论）
     * 
     * @param item 物品
     * @return 评论列表
     */
    List<Comment> findByItemAndParentIsNullOrderByCreatedAtDesc(Item item);

    /**
     * 分页查询物品的所有一级评论
     * 
     * @param item     物品
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> findByItemAndParentIsNullOrderByCreatedAtDesc(Item item, Pageable pageable);

    /**
     * 查找评论的所有回复
     * 
     * @param parent 父评论
     * @return 回复列表
     */
    List<Comment> findByParentOrderByCreatedAtAsc(Comment parent);

    /**
     * 查找用户发表的所有评论
     * 
     * @param user 用户
     * @return 评论列表
     */
    List<Comment> findByUserOrderByCreatedAtDesc(User user);

    /**
     * 分页查询用户发表的所有评论
     * 
     * @param user     用户
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);

    /**
     * 统计物品的评论数量
     * 
     * @param item 物品
     * @return 评论数量
     */
    long countByItem(Item item);

    /**
     * 删除物品的所有评论
     * 
     * @param item 物品
     */
    void deleteByItem(Item item);
}