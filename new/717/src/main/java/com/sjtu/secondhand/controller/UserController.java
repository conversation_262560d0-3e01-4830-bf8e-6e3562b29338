package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.request.UserUpdateRequest;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/users")
@Tag(name = "用户", description = "用户相关接口")
@SecurityRequirement(name = "JWT")
public class UserController {

    private final UserService userService;
    private final ItemService itemService;

    public UserController(UserService userService, ItemService itemService) {
        this.userService = userService;
        this.itemService = itemService;
    }

    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<Object>> getCurrentUser() {
        User user = userService.getCurrentUser();
        // 不返回密码
        user.setPassword(null);
        // 包装成前端期望的格式，添加兼容性属性
        return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", Map.of(
                "user", user)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户信息")
    public ResponseEntity<ApiResponse<User>> getUserById(@PathVariable Long id) {
        User user = userService.getUserById(id);
        // 不返回密码
        user.setPassword(null);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @PutMapping("/me")
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的信息")
    public ResponseEntity<ApiResponse<User>> updateUser(@RequestBody UserUpdateRequest userUpdateRequest) {
        User currentUser = userService.getCurrentUser();

        if (userUpdateRequest.getUsername() != null) {
            currentUser.setUsername(userUpdateRequest.getUsername());
        }

        if (userUpdateRequest.getAvatar_url() != null) {
            currentUser.setAvatarUrl(userUpdateRequest.getAvatar_url());
        }

        User updatedUser = userService.saveUser(currentUser);
        // 不返回密码
        updatedUser.setPassword(null);
        return ResponseEntity.ok(ApiResponse.success("用户信息更新成功", updatedUser));
    }

    @PostMapping("/me/check-in")
    @Operation(summary = "每日签到", description = "用户每日签到以获取平台活跃度积分")
    public ResponseEntity<ApiResponse<Void>> checkIn() {
        User user = userService.getCurrentUser();
        LocalDate today = LocalDate.now();

        // 检查用户是否已经签到
        if (user.getLastCheckInDate() != null && user.getLastCheckInDate().equals(today)) {
            return ResponseEntity.status(409).body(ApiResponse.error("CONFLICT", "今日已签到"));
        }

        // 更新签到日期和积分
        user.setLastCheckInDate(today);

        // 假设每次签到获得10积分
        Integer currentPoints = user.getPoints() != null ? user.getPoints() : 0;
        user.setPoints(currentPoints + 10);

        userService.saveUser(user);

        return ResponseEntity.ok(ApiResponse.success("签到成功，获得10积分", null));
    }

    @GetMapping("/me/favorites")
    @Operation(summary = "获取当前用户的收藏列表", description = "获取当前登录用户收藏的所有物品列表，支持分页")
    public ResponseEntity<ApiResponse<ItemPageResponse>> getFavorites(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {

        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 获取收藏列表
        List<ItemResponse> favoriteItems = itemService.getFavoriteItems(currentUser.getId());

        // 创建分页响应对象
        // 这里简化处理分页，实际上可能需要更复杂的分页逻辑
        int totalItems = favoriteItems.size();
        int totalPages = (int) Math.ceil((double) totalItems / size);

        // 调整页码，API文档从1开始，而Java代码从0开始
        page = page > 0 ? page - 1 : 0;

        // 计算当前页的内容
        int start = Math.min(page * size, totalItems);
        int end = Math.min(start + size, totalItems);
        List<ItemResponse> pageContent = favoriteItems.subList(start, end);

        // 创建分页响应
        ItemPageResponse response = new ItemPageResponse();
        response.setContent(pageContent);
        response.setPage(page + 1);
        response.setTotalElements(totalItems);
        response.setTotalPages(totalPages);

        return ResponseEntity.ok(ApiResponse.success("获取收藏列表成功", response));
    }
}
