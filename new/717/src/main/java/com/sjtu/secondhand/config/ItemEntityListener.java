package com.sjtu.secondhand.config;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;
import javax.persistence.PostRemove;
import java.util.Optional;

/**
 * Item实体监听器，用于同步数据到Elasticsearch
 * 
 * 这个监听器会在Item实体的生命周期事件（创建、更新、删除）发生时
 * 自动同步数据到Elasticsearch搜索引擎
 */
@Component
public class ItemEntityListener {

    private static final Logger logger = LoggerFactory.getLogger(ItemEntityListener.class);
    
    // 使用非静态字段，提高可测试性
    private ApplicationContext applicationContext;
    
    // 缓存服务实例，避免重复查找
    private ElasticsearchSyncService elasticsearchSyncService;
    
    /**
     * 设置应用上下文
     * @param applicationContext Spring应用上下文
     */
    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        logger.debug("ApplicationContext已设置");
    }

    /**
     * 物品保存或更新后触发，同步到Elasticsearch
     * @param item 被操作的Item实体
     */
    @PostPersist
    @PostUpdate
    public void onSaveOrUpdate(Item item) {
        if (item == null) {
            logger.warn("尝试同步空的Item实体到Elasticsearch");
            return;
        }
        
        logger.debug("开始同步Item到Elasticsearch: itemId={}", item.getId());
        
        try {
            getElasticsearchSyncService().ifPresentOrElse(
                service -> {
                    service.syncItemToElasticsearch(item);
                    logger.info("成功同步Item到Elasticsearch: itemId={}", item.getId());
                },
                () -> logger.error("无法获取ElasticsearchSyncService，同步失败: itemId={}", item.getId())
            );
        } catch (Exception e) {
            logger.error("同步Item到Elasticsearch时发生异常: itemId={}", item.getId(), e);
        }
    }

    /**
     * 物品删除后触发，从Elasticsearch中删除
     * @param item 被操作的Item实体
     */
    @PostRemove
    public void postRemove(Item item) {
        if (item == null) {
            logger.warn("尝试从Elasticsearch删除空的Item实体");
            return;
        }
        
        if (item.getId() == null) {
            logger.warn("尝试从Elasticsearch删除没有ID的Item实体");
            return;
        }
        
        logger.debug("开始从Elasticsearch删除Item: itemId={}", item.getId());
        
        try {
            getElasticsearchSyncService().ifPresentOrElse(
                service -> {
                    service.deleteItemFromElasticsearch(item.getId());
                    logger.info("成功从Elasticsearch删除Item: itemId={}", item.getId());
                },
                () -> logger.error("无法获取ElasticsearchSyncService，删除失败: itemId={}", item.getId())
            );
        } catch (Exception e) {
            logger.error("从Elasticsearch删除Item时发生异常: itemId={}", item.getId(), e);
        }
    }

    /**
     * 获取ElasticsearchSyncService的辅助方法
     * 使用缓存机制，避免重复查找Bean
     * @return 一个包含ElasticsearchSyncService的Optional
     */
    private Optional<ElasticsearchSyncService> getElasticsearchSyncService() {
        // 如果已经缓存了服务实例，直接返回
        if (elasticsearchSyncService != null) {
            return Optional.of(elasticsearchSyncService);
        }
        
        // 检查应用上下文是否可用
        if (applicationContext == null) {
            logger.error("ApplicationContext未初始化，无法获取ElasticsearchSyncService");
            return Optional.empty();
        }
        
        try {
            // 从Spring容器中获取服务实例
            elasticsearchSyncService = applicationContext.getBean(ElasticsearchSyncService.class);
            logger.debug("成功获取ElasticsearchSyncService实例");
            return Optional.of(elasticsearchSyncService);
        } catch (BeansException e) {
            logger.error("从Spring容器获取ElasticsearchSyncService失败", e);
            return Optional.empty();
        }
    }
    
    /**
     * 重置缓存的服务实例（主要用于测试）
     */
    protected void resetServiceCache() {
        this.elasticsearchSyncService = null;
        logger.debug("ElasticsearchSyncService缓存已重置");
    }
    
    /**
     * 检查服务是否可用（主要用于测试和监控）
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isServiceAvailable() {
        return getElasticsearchSyncService().isPresent();
    }
}