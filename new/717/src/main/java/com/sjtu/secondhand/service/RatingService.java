package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.model.Rating;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface RatingService {

    /**
     * 创建评价
     * 
     * @param ratingRequest 评价请求对象
     * @return 创建的评价响应
     */
    RatingResponse createRating(RatingRequest ratingRequest);

    /**
     * 获取评价详情
     * 
     * @param ratingId 评价ID
     * @return 评价响应
     */
    RatingResponse getRatingById(Long ratingId);

    /**
     * 获取当前用户作为评价者的评价列表
     * 
     * @param pageable 分页参数
     * @return 评价分页结果
     */
    Page<RatingResponse> getMyRatings(Pageable pageable);

    /**
     * 获取当前用户作为被评价者的评价列表
     * 
     * @param pageable 分页参数
     * @return 评价分页结果
     */
    Page<RatingResponse> getRatingsAboutMe(Pageable pageable);

    /**
     * 获取指定用户的评价列表
     * 
     * @param userId   用户ID
     * @param pageable 分页参数
     * @return 评价分页结果
     */
    Page<RatingResponse> getUserRatings(Long userId, Pageable pageable);

    /**
     * 按交易类型获取当前用户作为评价者的评价列表
     * 
     * @param type     交易类型
     * @param pageable 分页参数
     * @return 评价分页结果
     */
    Page<RatingResponse> getMyRatingsByTransactionType(String type, Pageable pageable);

    /**
     * 按交易类型获取当前用户作为被评价者的评价列表
     * 
     * @param type     交易类型
     * @param pageable 分页参数
     * @return 评价分页结果
     */
    Page<RatingResponse> getRatingsAboutMeByTransactionType(String type, Pageable pageable);

    /**
     * 获取用户的平均评分
     * 
     * @param userId 用户ID
     * @return 平均评分
     */
    Double getUserAverageRating(Long userId);

    /**
     * 获取待评价的订单列表
     * 
     * @param pageable 分页参数
     * @return 订单分页结果
     */
    Page<RatingResponse> getPendingOrderRatings(Pageable pageable);

    /**
     * 检查用户是否已经对交易进行了评价
     * 
     * @param transactionId 交易ID
     * @return 是否已评价
     */
    boolean hasRatedTransaction(Long transactionId);
}