package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.model.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.math.BigDecimal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemRepository extends JpaRepository<Item, Long> {

        // 修改默认查询方法，添加可见性条件
        @Query("SELECT i FROM Item i WHERE i.isVisible = true")
        Page<Item> findAll(Pageable pageable);

        // 修改名称搜索方法
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND LOWER(i.name) LIKE LOWER(CONCAT('%', :name, '%'))")
        Page<Item> findByNameContainingIgnoreCase(@Param("name") String name, Pageable pageable);

        // 修改分类名称搜索方法
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.category = :category AND LOWER(i.name) LIKE LOWER(CONCAT('%', :name, '%'))")
        Page<Item> findByCategoryAndNameContainingIgnoreCase(
                        @Param("category") Category category,
                        @Param("name") String name,
                        Pageable pageable);

        // 修改分类查询方法
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.category = :category")
        Page<Item> findByCategory(@Param("category") Category category, Pageable pageable);

        // 修改用户物品查询方法
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.user = :user")
        Page<Item> findByUser(@Param("user") User user, Pageable pageable);

        // 已有isVisible条件的方法保持不变
        @Query("SELECT i FROM Item i WHERE i.status = :status AND (:category IS NULL OR i.category = :category) AND (:keyword IS NULL OR LOWER(i.name) LIKE LOWER(CONCAT('%',:keyword,'%'))) AND i.isVisible = true")
        Page<Item> searchItems(@Param("category") Category category, @Param("keyword") String keyword,
                        @Param("status") Item.ItemStatus status, Pageable pageable);

        /**
         * 全文搜索方法 - 在名称和描述中搜索关键词
         * 
         * @param keyword  搜索关键词
         * @param pageable 分页信息
         * @return 物品分页结果
         */
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.status = :status AND (LOWER(i.name) LIKE LOWER(CONCAT('%',:keyword,'%')) OR LOWER(i.description) LIKE LOWER(CONCAT('%',:keyword,'%')))")
        Page<Item> fullTextSearch(@Param("keyword") String keyword, @Param("status") Item.ItemStatus status,
                        Pageable pageable);

        /**
         * 增强的全文搜索方法 - 包含更多语义匹配和同义词
         * 
         * @param keyword  搜索关键词，支持同义词匹配（如"苹果"可以匹配"ipad"）
         * @param status   物品状态
         * @param pageable 分页信息
         * @return 物品分页结果
         */
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.status = :status " +
                        "AND ((LOWER(i.name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                        "OR LOWER(i.description) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                        "OR (:keyword = '苹果' AND (LOWER(i.name) LIKE LOWER('%iphone%') OR LOWER(i.name) LIKE LOWER('%ipad%') "
                        +
                        "OR LOWER(i.name) LIKE LOWER('%macbook%') OR LOWER(i.description) LIKE LOWER('%apple%'))) " +
                        "OR (:keyword = 'apple' AND (LOWER(i.name) LIKE LOWER('%iphone%') OR LOWER(i.name) LIKE LOWER('%ipad%') "
                        +
                        "OR LOWER(i.name) LIKE LOWER('%macbook%') OR LOWER(i.description) LIKE LOWER('%苹果%'))) " +
                        "OR (:keyword = '华为' AND (LOWER(i.name) LIKE LOWER('%huawei%') OR LOWER(i.name) LIKE LOWER('%荣耀%') "
                        +
                        "OR LOWER(i.name) LIKE LOWER('%honor%') OR LOWER(i.description) LIKE LOWER('%华为%')))))")
        Page<Item> enhancedKeywordSearch(@Param("keyword") String keyword, @Param("status") Item.ItemStatus status,
                        Pageable pageable);

        /**
         * 高级搜索方法 - 根据多个条件进行筛选
         * 
         * @param keyword   搜索关键词（名称和描述）
         * @param category  类别
         * @param minPrice  最低价格
         * @param maxPrice  最高价格
         * @param condition 物品状况
         * @param pageable  分页信息
         * @return 物品分页结果
         */
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.status = :status " +
                        "AND (:keyword IS NULL OR LOWER(i.name) LIKE LOWER(CONCAT('%',:keyword,'%')) OR LOWER(i.description) LIKE LOWER(CONCAT('%',:keyword,'%'))) "
                        +
                        "AND (:category IS NULL OR i.category = :category) " +
                        "AND (:minPrice IS NULL OR i.price >= :minPrice) " +
                        "AND (:maxPrice IS NULL OR i.price <= :maxPrice) " +
                        "AND (:condition IS NULL OR i.condition = :condition)")
        Page<Item> advancedSearch(
                        @Param("keyword") String keyword,
                        @Param("category") Category category,
                        @Param("minPrice") Double minPrice,
                        @Param("maxPrice") Double maxPrice,
                        @Param("condition") Item.ItemCondition condition,
                        @Param("status") Item.ItemStatus status,
                        Pageable pageable);

        // 修改推荐物品查询
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.category = :category AND i.status = :status ORDER BY i.createdAt DESC")
        List<Item> findTop5ByCategoryAndStatusOrderByCreatedAtDesc(
                        @Param("category") Category category,
                        @Param("status") Item.ItemStatus status);

        /**
         * 根据API文档中的筛选条件查询物品
         * 
         * @param itemType   物品类型：IDLE-卖闲置, WANTED-求好物
         * @param categoryId 分类ID
         * @param priceMin   最低价格
         * @param priceMax   最高价格
         * @param keyword    搜索关键词
         * @param userId     用户ID，筛选指定用户发布的物品
         * @param pageable   分页信息
         * @return 物品分页结果
         */
        @Query("SELECT i FROM Item i WHERE 1=1 " +
                        "AND i.isVisible = true " +
                        "AND i.status = 'FOR_SALE' " +
                        "AND (:itemType IS NULL OR i.itemType = :itemType) " +
                        "AND (:categoryId IS NULL OR CAST(i.category.id AS string) = CAST(:categoryId AS string)) " +
                        "AND (:priceMin IS NULL OR i.price >= :priceMin) " +
                        "AND (:priceMax IS NULL OR i.price <= :priceMax) " +
                        "AND (:userId IS NULL OR i.user.id = :userId) " +
                        "AND (:keyword IS NULL OR LOWER(i.name) LIKE LOWER(CONCAT('%',:keyword,'%')) OR LOWER(i.description) LIKE LOWER(CONCAT('%',:keyword,'%')))")
        Page<Item> findItemsWithFilters(
                        @Param("itemType") Item.ItemType itemType,
                        @Param("categoryId") Integer categoryId,
                        @Param("priceMin") Double priceMin,
                        @Param("priceMax") Double priceMax,
                        @Param("keyword") String keyword,
                        @Param("userId") Long userId,
                        Pageable pageable);

        /**
         * 根据用户ID查询该用户发布的所有物品
         * 
         * @param userId 用户ID
         * @return 该用户发布的所有物品列表
         */
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.user.id = :userId")
        List<Item> findByUserId(@Param("userId") Long userId);

        /**
         * 根据用户ID和物品类型查询该用户发布的物品
         * 
         * @param userId   用户ID
         * @param itemType 物品类型
         * @return 该用户发布的指定类型的物品列表
         */
        @Query("SELECT i FROM Item i WHERE i.isVisible = true AND i.user.id = :userId AND i.itemType = :itemType")
        List<Item> findByUserIdAndItemType(@Param("userId") Long userId, @Param("itemType") Item.ItemType itemType);

        /**
         * 根据分类ID、状态、可见性、ID、价格区间查找商品，用于内容推荐
         * 
         * @param categoryId 分类ID
         * @param status     物品状态
         * @param isVisible  是否可见
         * @param id         当前物品ID (排除)
         * @param minPrice   最低价格
         * @param maxPrice   最高价格
         * @param pageable   分页信息
         * @return 符合条件的物品列表
         */
        List<Item> findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
                        Integer categoryId,
                        Item.ItemStatus status,
                        Boolean isVisible,
                        Long id,
                        BigDecimal minPrice,
                        BigDecimal maxPrice,
                        Pageable pageable);

        /**
         * 查询热门物品，基于收藏数和浏览数
         * 
         * @param status    物品状态
         * @param isVisible 是否可见
         * @param pageable  分页信息
         * @return 热门物品列表
         */
        @Query("SELECT i FROM Item i WHERE i.status = :status AND i.isVisible = :isVisible ORDER BY (i.favoriteCount * 5 + i.viewCount) DESC")
        List<Item> findHotItems(
                        @Param("status") Item.ItemStatus status,
                        @Param("isVisible") Boolean isVisible,
                        Pageable pageable);
}
