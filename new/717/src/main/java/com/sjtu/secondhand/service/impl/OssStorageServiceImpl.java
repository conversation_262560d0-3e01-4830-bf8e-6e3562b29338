package com.sjtu.secondhand.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PolicyConditions;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.service.OssStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class OssStorageServiceImpl implements OssStorageService {

    private static final Logger logger = LoggerFactory.getLogger(OssStorageServiceImpl.class);

    private final OSS ossClient;

    @Value("${aliyun.oss.bucket-name:shareu-youyu}")
    private String bucketName;

    @Value("${aliyun.oss.bucket-domain:shareu-youyu.oss-cn-shanghai.aliyuncs.com}")
    private String bucketDomain;
    
    @Value("${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}")
    private String endpoint;
    
    @Value("${aliyun.oss.access-key-id:LTAI5tLKLwDpsP947BPBLsnX}")
    private String accessKeyId;

    // 图片上传文件夹路径
    private static final String IMAGE_FOLDER = "item_images/";
    
    // OSS URL有效期设置为3600秒（1小时）
    private static final long URL_EXPIRATION_TIME = 3600L;

    public OssStorageServiceImpl(OSS ossClient) {
        this.ossClient = ossClient;
    }

    /**
     * 生成唯一的文件名，使用时间戳+UUID的方式
     * 
     * @param fileExtension 文件扩展名，包含点号，如 ".jpg"
     * @return 唯一的文件名
     */
    private String generateUniqueObjectName(String fileExtension) {
        // 生成时间戳，格式为yyyyMMddHHmmss
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = dateFormat.format(new Date());
        
        // 生成UUID
        String uuid = UUID.randomUUID().toString();
        
        // 返回时间戳+UUID+文件扩展名
        return timestamp + "_" + uuid + fileExtension;
    }

    @Override
    public Map<String, String> generatePresignedUrl(String filename, String contentType) {
        String originalFilename = StringUtils.cleanPath(filename);
        if (originalFilename.contains("..")) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "文件名包含无效路径序列 " + originalFilename);
        }

        String fileExtension = "";
        if (originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成唯一对象名称，并添加到item_images文件夹
        String objectName = IMAGE_FOLDER + generateUniqueObjectName(fileExtension);

        try {
            logger.info("生成OSS预签名URL，文件名: {}, 内容类型: {}, 对象名: {}", filename, contentType, objectName);
            
            // 使用预签名URL方式
            // 设置请求参数
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName);
    
            // 设置HTTP方法为PUT（用于上传）
            request.setMethod(com.aliyun.oss.HttpMethod.PUT);
    
            // 设置Content-Type
            request.setContentType(contentType);
    
            // 设置URL过期时间（1小时）
            Date expiration = new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME * 1000);
            request.setExpiration(expiration);
    
            // 生成预签名URL
            URL url = ossClient.generatePresignedUrl(request);
            
            logger.info("生成的预签名URL: {}", url.toString());
    
            // 构建结果
            Map<String, String> result = new HashMap<>();
            result.put("upload_url", url.toString());
            result.put("access_url", "https://" + bucketDomain + "/" + objectName);
            result.put("object_name", objectName);
            result.put("method", "PUT");
            result.put("content_type", contentType);
    
            return result;
        } catch (Exception e) {
            logger.error("生成预签名URL失败", e);
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "生成预签名URL失败: " + e.getMessage());
        }
    }

    @Override
    public String uploadFile(MultipartFile file) {
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        if (originalFilename.contains("..")) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "文件名包含无效路径序列 " + originalFilename);
        }

        String fileExtension = "";
        if (originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成唯一对象名称，并添加到item_images文件夹
        String objectName = IMAGE_FOLDER + generateUniqueObjectName(fileExtension);

        try {
            logger.info("开始上传文件到OSS，文件名: {}, 大小: {} bytes", originalFilename, file.getSize());
            
            // 设置元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            metadata.setContentLength(file.getSize());

            // 上传文件到OSS
            ossClient.putObject(bucketName, objectName, file.getInputStream(), metadata);
            
            String fileUrl = "https://" + bucketDomain + "/" + objectName;
            logger.info("文件上传成功，访问URL: {}", fileUrl);

            // 返回访问URL
            return fileUrl;
        } catch (IOException ex) {
            logger.error("上传文件到OSS失败", ex);
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "上传文件到OSS失败: " + ex.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String objectName) {
        try {
            logger.info("尝试删除OSS文件: {}", objectName);
            
            // 检查文件是否存在
            boolean exists = ossClient.doesObjectExist(bucketName, objectName);
            if (!exists) {
                logger.warn("要删除的文件不存在: {}", objectName);
                return false;
            }

            // 删除文件
            ossClient.deleteObject(bucketName, objectName);
            logger.info("文件删除成功: {}", objectName);
            return true;
        } catch (Exception ex) {
            logger.error("从OSS删除文件失败: {}", objectName, ex);
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "从OSS删除文件失败: " + ex.getMessage());
        }
    }
} 