package com.sjtu.secondhand.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.CORSConfiguration;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.OssStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/upload")
@Tag(name = "文件上传 (Upload)", description = "文件上传相关接口")
public class UploadController {

    private static final Logger logger = LoggerFactory.getLogger(UploadController.class);

    private final FileStorageService fileStorageService;
    private final OssStorageService ossStorageService;
    
    @Autowired
    private OSS ossClient;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;

    @Value("${storage.type:local}")
    private String storageType;
    
    @Value("${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}")
    private String allowedOrigins;
    
    @Value("${aliyun.oss.bucket-name:shareu-youyu}")
    private String bucketName;
    
    @Value("${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}")
    private String endpoint;

    public UploadController(FileStorageService fileStorageService, OssStorageService ossStorageService) {
        this.fileStorageService = fileStorageService;
        this.ossStorageService = ossStorageService;
    }

    @GetMapping("/signature")
    @Operation(summary = "获取上传签名", description = "获取用于直传的签名URL", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Map<String, String>>> getUploadSignature(
            @RequestParam("filename") String filename,
            @RequestParam("content_type") String contentType) {

        logger.info("获取上传签名请求，文件名: {}, 内容类型: {}, 存储类型: {}", filename, contentType, storageType);

        if ("oss".equals(storageType)) {
            try {
                // 使用OSS生成预签名URL
                Map<String, String> signatureData = ossStorageService.generatePresignedUrl(filename, contentType);
                
                // 检查上传URL是否存在
                if (signatureData.get("upload_url") == null) {
                    logger.error("生成的上传URL为空");
                    return ResponseEntity.ok(ApiResponse.error("SIGNATURE_ERROR", "生成的上传URL为空", null));
                }
                
                logger.info("OSS签名生成成功，上传URL: {}", signatureData.get("upload_url"));
                
                // 为前端提供额外信息，帮助调试
                signatureData.put("current_origin", allowedOrigins);
                
                return ResponseEntity.ok(ApiResponse.success("签名生成成功", signatureData));
            } catch (Exception e) {
                logger.error("生成OSS签名失败", e);
                return ResponseEntity.ok(ApiResponse.error("SIGNATURE_ERROR", "生成OSS签名失败: " + e.getMessage(), null));
            }
        } else {
            // 本地存储模式，返回上传端点
            Map<String, String> result = new HashMap<>();
            result.put("upload_url", contextPath + "/upload/file");
            result.put("method", "POST");
            return ResponseEntity.ok(ApiResponse.success("签名生成成功", result));
        }
    }

    @PostMapping("/file")
    @Operation(summary = "上传文件", description = "上传文件到服务器", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<String>> uploadFile(@RequestParam("file") MultipartFile file) {
        if ("oss".equals(storageType)) {
            // 使用OSS存储
            String fileUrl = ossStorageService.uploadFile(file);
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileUrl));
        } else {
            // 使用本地存储
            String fileName = fileStorageService.storeFile(file);
            String fileDownloadUri = contextPath + "/upload/files/" + fileName;
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", fileDownloadUri));
        }
    }

    @DeleteMapping("/file")
    @Operation(summary = "删除文件", description = "删除已上传的文件", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Boolean>> deleteFile(@RequestParam("objectName") String objectName) {
        if ("oss".equals(storageType)) {
            // 使用OSS存储
            boolean deleted = ossStorageService.deleteFile(objectName);
            return ResponseEntity.ok(ApiResponse.success("文件删除" + (deleted ? "成功" : "失败，文件不存在"), deleted));
        } else {
            // 使用本地存储
            boolean deleted = fileStorageService.deleteFile(objectName);
            return ResponseEntity.ok(ApiResponse.success("文件删除" + (deleted ? "成功" : "失败，文件不存在"), deleted));
        }
    }
    
    /**
     * 获取OSS配置信息，用于诊断
     */
    @GetMapping("/oss-info")
    @Operation(summary = "获取OSS配置信息", description = "获取OSS配置信息，包括CORS设置", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOssInfo() {
        if (!"oss".equals(storageType)) {
            return ResponseEntity.ok(ApiResponse.error("NOT_OSS", "当前存储类型不是OSS", null));
        }
        
        try {
            Map<String, Object> info = new HashMap<>();
            info.put("bucketName", bucketName);
            info.put("endpoint", endpoint);
            info.put("allowedOrigins", allowedOrigins);
            
            // 获取CORS配置
            List<SetBucketCORSRequest.CORSRule> corsRules = ossClient.getBucketCORSRules(bucketName);
            List<Map<String, Object>> corsRulesList = new ArrayList<>();
            
            if (corsRules != null) {
                for (SetBucketCORSRequest.CORSRule rule : corsRules) {
                    Map<String, Object> ruleMap = new HashMap<>();
                    ruleMap.put("allowedOrigins", rule.getAllowedOrigins());
                    ruleMap.put("allowedMethods", rule.getAllowedMethods());
                    ruleMap.put("allowedHeaders", rule.getAllowedHeaders());
                    ruleMap.put("exposeHeaders", rule.getExposeHeaders());
                    ruleMap.put("maxAgeSeconds", rule.getMaxAgeSeconds());
                    corsRulesList.add(ruleMap);
                }
            }
            
            info.put("corsRules", corsRulesList);
            
            return ResponseEntity.ok(ApiResponse.success("获取OSS配置信息成功", info));
        } catch (Exception e) {
            logger.error("获取OSS配置信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("OSS_INFO_ERROR", "获取OSS配置信息失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 更新OSS CORS配置
     */
    @PostMapping("/update-cors")
    @Operation(summary = "更新OSS CORS配置", description = "更新OSS CORS配置", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Boolean>> updateOssCors() {
        if (!"oss".equals(storageType)) {
            return ResponseEntity.ok(ApiResponse.error("NOT_OSS", "当前存储类型不是OSS", null));
        }
        
        try {
            // 创建CORS规则
            SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);
            
            // 创建一个CORS规则
            SetBucketCORSRequest.CORSRule rule = new SetBucketCORSRequest.CORSRule();
            
            // 设置允许的来源
            List<String> allowedOriginsList = Arrays.asList(allowedOrigins.split(","));
            rule.setAllowedOrigins(allowedOriginsList);
            
            // 设置允许的方法
            List<String> allowedMethods = Arrays.asList("GET", "PUT", "DELETE", "POST", "HEAD");
            rule.setAllowedMethods(allowedMethods);
            
            // 设置允许的头
            List<String> allowedHeaders = Arrays.asList("*");
            rule.setAllowedHeaders(allowedHeaders);
            
            // 设置暴露的头
            List<String> exposeHeaders = Arrays.asList("ETag", "x-oss-request-id");
            rule.setExposeHeaders(exposeHeaders);
            
            // 设置最大缓存时间
            rule.setMaxAgeSeconds(3600);
            
            // 添加规则到请求
            request.setCorsRules(new ArrayList<>(Collections.singletonList(rule)));
            
            // 设置CORS规则
            ossClient.setBucketCORS(request);
            
            logger.info("OSS CORS规则更新成功");
            return ResponseEntity.ok(ApiResponse.success("OSS CORS规则更新成功", true));
        } catch (Exception e) {
            logger.error("更新OSS CORS规则失败", e);
            return ResponseEntity.ok(ApiResponse.error("UPDATE_CORS_ERROR", "更新OSS CORS规则失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 请求头诊断端点
     */
    @GetMapping("/request-info")
    @Operation(summary = "获取请求头信息", description = "获取当前请求的头信息，用于诊断", security = @SecurityRequirement(name = "JWT"))
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRequestInfo(HttpServletRequest request) {
        Map<String, Object> info = new HashMap<>();
        
        // 收集请求头信息
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        info.put("headers", headers);
        
        // 收集请求信息
        info.put("method", request.getMethod());
        info.put("requestURI", request.getRequestURI());
        info.put("queryString", request.getQueryString());
        info.put("remoteAddr", request.getRemoteAddr());
        info.put("remoteHost", request.getRemoteHost());
        info.put("remotePort", request.getRemotePort());
        info.put("localAddr", request.getLocalAddr());
        info.put("localPort", request.getLocalPort());
        info.put("serverName", request.getServerName());
        info.put("serverPort", request.getServerPort());
        info.put("scheme", request.getScheme());
        
        return ResponseEntity.ok(ApiResponse.success("获取请求信息成功", info));
    }
}