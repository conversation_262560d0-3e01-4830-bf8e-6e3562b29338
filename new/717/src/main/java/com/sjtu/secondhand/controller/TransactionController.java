package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.TransactionCompleteRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.OfferService;
import com.sjtu.secondhand.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/transactions")
@Tag(name = "交易 (Transactions)", description = "交易相关接口")
public class TransactionController {

    private final OrderService orderService;
    private final OfferService offerService;
    private final UserRepository userRepository;

    @Autowired
    public TransactionController(OrderService orderService, OfferService offerService, UserRepository userRepository) {
        this.orderService = orderService;
        this.offerService = offerService;
        this.userRepository = userRepository;
    }

    @PostMapping("/complete")
    @Operation(summary = "[通用] 标记交易完成", description = "交易双方在线下完成交易后，由任意一方调用此接口，将订单或Offer状态更新为 'COMPLETED'，物品状态更新为 'SOLD'。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<Void>> completeTransaction(
            @Valid @RequestBody TransactionCompleteRequest request) {
        Long userId = getCurrentUserId();

        if ("IDLE".equals(request.getTransaction_type())) {
            orderService.completeOrder(request.getTransaction_id().longValue());
            return ResponseEntity.ok(ApiResponse.success("订单已标记为完成", null));
        } else if ("WANTED".equals(request.getTransaction_type())) {
            offerService.completeOffer(request.getTransaction_id().longValue());
            return ResponseEntity.ok(ApiResponse.success("求购响应已标记为完成", null));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.error("无效的交易类型"));
        }
    }

    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"))
                .getId();
    }
}