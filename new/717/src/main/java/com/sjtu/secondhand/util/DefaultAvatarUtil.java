package com.sjtu.secondhand.util;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 默认头像工具类
 * 用于在用户注册时随机分配默认头像
 */
public class DefaultAvatarUtil {

    // 默认头像URL列表
    private static final List<String> DEFAULT_AVATAR_URLS = Arrays.asList(
            "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/alice.png",
            "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/lily.png",
            "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/bob.png",
            "https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/kevin.png"
    );

    private static final Random random = new Random();

    /**
     * 获取随机默认头像URL
     * 
     * @return 随机选择的默认头像URL
     */
    public static String getRandomDefaultAvatarUrl() {
        int index = random.nextInt(DEFAULT_AVATAR_URLS.size());
        return DEFAULT_AVATAR_URLS.get(index);
    }
    
    /**
     * 根据用户名获取默认头像URL
     * 使用用户名的哈希值来确定头像，确保同一用户名总是获得同一头像
     * 
     * @param username 用户名
     * @return 基于用户名确定的默认头像URL
     */
    public static String getDefaultAvatarUrlByUsername(String username) {
        int index = Math.abs(username.hashCode() % DEFAULT_AVATAR_URLS.size());
        return DEFAULT_AVATAR_URLS.get(index);
    }
} 