package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.LoginRequest;
import com.sjtu.secondhand.dto.request.RegisterRequest;
import com.sjtu.secondhand.dto.response.JwtAuthResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.security.JwtTokenProvider;
import com.sjtu.secondhand.service.AuthService;
import com.sjtu.secondhand.util.DefaultAvatarUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthServiceImpl implements AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider jwtTokenProvider;

    @Autowired
    public AuthServiceImpl(
            UserRepository userRepository,
            PasswordEncoder passwordEncoder,
            AuthenticationManager authenticationManager,
            JwtTokenProvider jwtTokenProvider) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.authenticationManager = authenticationManager;
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Override
    public JwtAuthResponse login(LoginRequest loginRequest) {
        // 尝试认证用户
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()));

        // 设置认证信息到上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 获取用户信息
        User user = userRepository.findByUsername(loginRequest.getUsername())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, "用户不存在"));

        // 生成JWT令牌
        String token = jwtTokenProvider.createToken(user.getUsername(), user.getId());

        // 创建响应对象
        JwtAuthResponse response = new JwtAuthResponse();
        response.setToken(token);
        response.setTokenType("Bearer");
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setAvatar(user.getAvatarUrl());

        return response;
    }

    @Override
    public String register(RegisterRequest registerRequest) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "用户名已被占用");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "邮箱已被注册");
        }

        // 验证邮箱后缀是否为sjtu.edu.cn
        if (!registerRequest.getEmail().endsWith("@sjtu.edu.cn")) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "请使用上海交通大学邮箱注册");
        }

        // 创建新用户对象
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setEmail(registerRequest.getEmail());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        
        // 为用户分配默认头像
        // 使用基于用户名的头像分配，确保同一用户名总是获得相同头像
        String defaultAvatarUrl = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(registerRequest.getUsername());
        user.setAvatarUrl(defaultAvatarUrl);

        // 保存用户
        userRepository.save(user);

        return "用户注册成功";
    }
}