package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class RatingRequest {

    @NotNull(message = "交易类型不能为空")
    private String transaction_type; // IDLE 或 WANTED

    @NotNull(message = "交易ID不能为空")
    private Integer related_transaction_id;

    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1分")
    @Max(value = 5, message = "评分最高为5分")
    private Integer score;

    public RatingRequest() {
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Integer getRelated_transaction_id() {
        return related_transaction_id;
    }

    public void setRelated_transaction_id(Integer related_transaction_id) {
        this.related_transaction_id = related_transaction_id;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }
}