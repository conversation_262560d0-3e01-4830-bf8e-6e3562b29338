package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Footprint;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FootprintRepository extends JpaRepository<Footprint, Long> {
    
    /**
     * 查找用户的浏览足迹，按照浏览时间倒序
     * @param user 用户
     * @param pageable 分页参数
     * @return 分页的足迹列表
     */
    Page<Footprint> findByUserOrderByViewTimeDesc(User user, Pageable pageable);
    
    /**
     * 查询用户是否已经浏览过某个商品
     * @param user 用户
     * @param itemId 商品ID
     * @return 足迹记录（如果存在）
     */
    @Query("SELECT f FROM Footprint f WHERE f.user = :user AND f.item.id = :itemId")
    Optional<Footprint> findByUserAndItemId(@Param("user") User user, @Param("itemId") Long itemId);
    
    /**
     * 删除用户的某条足迹
     * @param user 用户
     * @param itemId 商品ID
     */
    void deleteByUserAndItemId(User user, Long itemId);
    
    /**
     * 删除用户的所有足迹
     * @param user 用户
     */
    void deleteAllByUser(User user);
}
