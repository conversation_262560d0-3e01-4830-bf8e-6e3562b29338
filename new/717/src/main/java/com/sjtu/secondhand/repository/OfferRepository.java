package com.sjtu.secondhand.repository;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OfferRepository extends JpaRepository<Offer, Long> {

    Page<Offer> findByOfferer(User offerer, Pageable pageable);

    Page<Offer> findByRequester(User requester, Pageable pageable);

    @Query("SELECT o FROM Offer o WHERE o.wantedItem = :item")
    List<Offer> findByWantedItem(@Param("item") Item item);

    @Query("SELECT o FROM Offer o WHERE o.wantedItem.id = :itemId")
    List<Offer> findByWantedItemId(@Param("itemId") Long itemId);

    Optional<Offer> findByWantedItemAndOfferer(Item wantedItem, User offerer);

    @Query("SELECT o FROM Offer o WHERE o.offerer = :user AND o.status = :status")
    Page<Offer> findByOffererAndStatus(@Param("user") User user, @Param("status") Offer.OfferStatus status,
            Pageable pageable);

    @Query("SELECT o FROM Offer o WHERE o.requester = :user AND o.status = :status")
    Page<Offer> findByRequesterAndStatus(@Param("user") User user, @Param("status") Offer.OfferStatus status,
            Pageable pageable);
}