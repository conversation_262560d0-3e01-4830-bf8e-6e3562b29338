package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存测试控制器
 * 仅用于测试环境，提供Redis缓存状态查询接口
 */
@RestController
@RequestMapping("/cache-test")
public class CacheTestController {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public CacheTestController(CacheManager cacheManager, RedisTemplate<String, Object> redisTemplate) {
        this.cacheManager = cacheManager;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取缓存状态
     * 
     * @return 缓存状态信息
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();

        // 检查Redis连接
        boolean redisConnected = false;
        try {
            redisConnected = redisTemplate.getConnectionFactory().getConnection().ping() != null;
            status.put("redisConnected", redisConnected);
        } catch (Exception e) {
            status.put("redisConnected", false);
            status.put("redisError", e.getMessage());
        }

        // 获取缓存名称列表
        if (redisConnected) {
            try {
                Set<String> cacheNames = redisTemplate.keys("*");
                status.put("cacheNames", cacheNames);

                // 获取物品缓存的键
                Set<String> itemCacheKeys = redisTemplate.keys("items::*");
                status.put("itemCacheKeys", itemCacheKeys);

                // 获取物品缓存数量
                status.put("itemCacheCount", itemCacheKeys != null ? itemCacheKeys.size() : 0);
            } catch (Exception e) {
                status.put("keysError", e.getMessage());
            }
        }

        return ApiResponse.success("缓存状态信息", status);
    }

    /**
     * 清除所有缓存
     * 
     * @return 操作结果
     */
    @GetMapping("/clear")
    public ApiResponse<Void> clearAllCaches() {
        try {
            // 清除所有缓存
            Set<String> cacheNames = redisTemplate.keys("*");
            if (cacheNames != null) {
                for (String name : cacheNames) {
                    redisTemplate.delete(name);
                }
            }
            return ApiResponse.success("所有缓存已清除", null);
        } catch (Exception e) {
            return ApiResponse.error("清除缓存失败: " + e.getMessage());
        }
    }
}