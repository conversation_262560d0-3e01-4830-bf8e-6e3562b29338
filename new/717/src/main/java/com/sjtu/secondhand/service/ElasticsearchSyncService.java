package com.sjtu.secondhand.service;

import com.sjtu.secondhand.model.Item;

public interface ElasticsearchSyncService {

    /**
     * 将单个物品同步到Elasticsearch
     * 
     * @param item 物品实体
     */
    void syncItemToElasticsearch(Item item);
    
    /**
     * 从Elasticsearch中删除物品
     * 
     * @param itemId 物品ID
     */
    void deleteItemFromElasticsearch(Long itemId);
    
    /**
     * 同步所有物品到Elasticsearch
     */
    void syncAllItemsToElasticsearch();
    
    /**
     * 通过关键词在ES中搜索物品
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    Object searchItemsByKeyword(String keyword, int page, int size);
    
    /**
     * 高级搜索
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param itemType 物品类型
     * @param condition 物品状况
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序方式
     * @return 搜索结果
     */
    Object advancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                          String itemType, String condition, int page, int size, String sort);
    
    /**
     * 直接向ES服务器发送HTTP请求，通过关键词搜索物品
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    Object directSearchByKeyword(String keyword, int page, int size);
    
    /**
     * 直接向ES服务器发送HTTP请求，进行高级搜索
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param itemType 物品类型
     * @param condition 物品状况
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序方式
     * @return 搜索结果
     */
    Object directAdvancedSearch(String keyword, Long categoryId, Double minPrice, Double maxPrice,
                               String itemType, String condition, int page, int size, String sort);
} 