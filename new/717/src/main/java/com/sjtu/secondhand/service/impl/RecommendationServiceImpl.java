package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.ItemSimilarity;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.ItemSimilarityRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.RecommendationService;
import com.sjtu.secondhand.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐系统服务实现
 */
@Service
public class RecommendationServiceImpl implements RecommendationService {

    private static final Logger logger = LoggerFactory.getLogger(RecommendationServiceImpl.class);

    // 热度分计算权重
    private static final double W1 = 5.0;  // 收藏权重
    private static final double W2 = 1.0;  // 浏览权重
    private static final double W3 = 0.1;  // 用户信用分权重
    private static final double W4 = 0.1;  // 用户积分权重
    
    // 推荐参数
    private static final int MAX_USER_FAVORITE_ITEMS = 5;  // 用户最多收藏物品数量
    private static final int SIMILAR_ITEMS_PER_ITEM = 4;   // 每个物品的相似物品数量
    private static final int MAX_RECOMMENDATIONS = 16;     // 最大推荐数量
    
    // 物品相似度计算参数
    private static final int MIN_INTERACTIONS = 3;  // 最小交互数
    private static final double SIMILARITY_THRESHOLD = 0.01;  // 相似度阈值
    
    // 内容推荐价格范围参数
    private static final double PRICE_RANGE_FACTOR = 0.3;  // ±30%价格范围
    
    private final ItemRepository itemRepository;
    private final ItemSimilarityRepository itemSimilarityRepository;
    private final UserRepository userRepository;
    private final UserService userService;
    private final ItemService itemService;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public RecommendationServiceImpl(
            ItemRepository itemRepository,
            ItemSimilarityRepository itemSimilarityRepository,
            UserRepository userRepository,
            UserService userService,
            ItemService itemService,
            JdbcTemplate jdbcTemplate) {
        this.itemRepository = itemRepository;
        this.itemSimilarityRepository = itemSimilarityRepository;
        this.userRepository = userRepository;
        this.userService = userService;
        this.itemService = itemService;
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    @Cacheable(value = "hotRecommendations", key = "#limit")
    public List<ItemResponse> getHotRecommendations(int limit) {
        logger.info("获取热门推荐，限制数量: {}", limit);

        // 使用SQL计算热度分并获取Top-N物品
        String sql = "SELECT i.id " +
                "FROM items i JOIN users u ON i.user_id = u.id " +
                "WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE " +
                "ORDER BY (" + W1 + " * i.favorite_count + " + W2 + " * i.view_count + " 
                + W3 + " * u.credit_score + " + W4 + " * u.points) DESC " +
                "LIMIT ?";

        List<Long> hotItemIds = jdbcTemplate.queryForList(sql, Long.class, limit);

        // 获取物品详情
        List<Item> hotItems = itemRepository.findAllById(hotItemIds);

        // 排序以确保顺序与查询结果匹配
        Map<Long, Item> itemMap = hotItems.stream().collect(Collectors.toMap(Item::getId, item -> item));
        List<Item> sortedItems = hotItemIds.stream()
                .map(itemMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 转换为ItemResponse并返回
        return itemService.convertItemsWithFavoriteStatus(sortedItems);
    }

    @Override
    @Cacheable(value = "itemCFRecommendations", key = "#userId")
    public List<ItemResponse> getItemCFRecommendations(Long userId, int limit) {
        logger.info("获取用户{}的个性化推荐，限制数量: {}", userId, limit);

        // 设置默认推荐数量（如果未提供）
        if (limit <= 0) {
            limit = MAX_RECOMMENDATIONS;
        }

        // 获取用户
        User user = userService.getUserById(userId);
        if (user == null) {
            throw new ApiException(HttpStatus.NOT_FOUND, "用户不存在");
        }

        // 获取用户最近的收藏（最多5个）
        Set<Item> userItems = user.getFavoriteItems();
        if (userItems.isEmpty()) {
            // 冷启动：如果用户没有收藏，返回热门推荐
            logger.info("用户{}没有收藏项，返回热门推荐", userId);
            return getHotRecommendations(limit);
        }

        // 获取用户收藏的物品ID（最多5个，按最新收藏排序）
        List<Long> userItemIds = userItems.stream()
                .sorted(Comparator.comparing(Item::getUpdatedAt).reversed())
                .map(Item::getId)
                .limit(MAX_USER_FAVORITE_ITEMS)
                .collect(Collectors.toList());

        logger.info("用户{}的收藏物品IDs: {}", userId, userItemIds);

        // 对每个收藏物品，获取4个最相似的物品
        Map<Long, Double> recommendScores = new HashMap<>();
        for (Long itemId : userItemIds) {
            List<ItemSimilarity> similarities = itemSimilarityRepository
                    .findMostSimilarItems(itemId, SIMILAR_ITEMS_PER_ITEM);
            
            logger.info("物品{}的相似物品数量: {}", itemId, similarities.size());
            
            // 累加每个候选物品的相似度分数
            for (ItemSimilarity similarity : similarities) {
                Long recommendItemId = similarity.getItemId2();
                // 跳过用户已收藏的物品
                if (userItemIds.contains(recommendItemId)) {
                    continue;
                }
                // 加总相似度分数
                recommendScores.put(recommendItemId,
                        recommendScores.getOrDefault(recommendItemId, 0.0) + similarity.getScore());
            }
        }

        logger.info("候选推荐物品数量: {}", recommendScores.size());

        // 按分数降序排序，获取Top-N
        List<Long> recommendItemIds = recommendScores.entrySet().stream()
                .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
                .limit(limit)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 如果推荐数量不足，用热门物品填充
        if (recommendItemIds.size() < limit) {
            int remainingCount = limit - recommendItemIds.size();
            logger.info("推荐数量不足，需要填充{}个热门物品", remainingCount);
            
            List<ItemResponse> hotItems = getHotRecommendations(limit * 2);

            // 过滤掉已推荐的物品和用户的收藏
            Set<Long> existingIds = new HashSet<>(recommendItemIds);
            existingIds.addAll(userItemIds);

            List<Long> additionalItemIds = hotItems.stream()
                    .map(item -> Long.parseLong(item.getId().toString()))
                    .filter(id -> !existingIds.contains(id))
                    .limit(remainingCount)
                    .collect(Collectors.toList());

            recommendItemIds.addAll(additionalItemIds);
        }

        // 获取物品详情
        List<Item> recommendedItems = itemRepository.findAllById(recommendItemIds);
        logger.info("最终获取到的推荐物品数量: {}", recommendedItems.size());

        // 排序以匹配推荐分数顺序
        Map<Long, Item> itemMap = recommendedItems.stream()
                .collect(Collectors.toMap(Item::getId, item -> item, (a, b) -> a));
        List<Item> sortedItems = recommendItemIds.stream()
                .map(itemMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 转换为ItemResponse并返回
        return itemService.convertItemsWithFavoriteStatus(sortedItems);
    }

    @Override
    @Cacheable(value = "contentBasedRecommendations", key = "#itemId + '-' + #limit")
    public List<ItemResponse> getContentBasedRecommendations(Long itemId, int limit) {
        // 对于商品详情页的"猜你喜欢"模块，我们直接从item_similarity表中获取相似商品
        logger.info("获取物品{}的相似推荐，限制数量: {}", itemId, limit);

        // 默认显示4个相似商品
        if (limit <= 0) {
            limit = SIMILAR_ITEMS_PER_ITEM;
        }

        // 获取当前物品
        Item currentItem = itemRepository.findById(itemId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "物品不存在"));

        // 直接从item_similarity表获取相似度最高的物品
        List<ItemSimilarity> similarities = itemSimilarityRepository.findMostSimilarItems(itemId, limit);
        
        // 如果相似度表中没有数据，则退化为基于内容的推荐
        if (similarities.isEmpty()) {
            logger.info("物品{}在相似度表中没有数据，使用基于内容的推荐", itemId);
            return getContentBasedRecommendationsFallback(itemId, limit);
        }
        
        // 获取相似物品的IDs
        List<Long> similarItemIds = similarities.stream()
                .map(ItemSimilarity::getItemId2)
                .collect(Collectors.toList());
        
        // 获取物品详情
        List<Item> similarItems = itemRepository.findAllById(similarItemIds);
        
        // 过滤掉非在售状态的物品
        List<Item> availableSimilarItems = similarItems.stream()
                .filter(item -> item.getStatus() == ItemStatus.FOR_SALE && item.getIsVisible())
                .collect(Collectors.toList());
        
        // 如果有效的推荐不足，使用基于内容的推荐补充
        if (availableSimilarItems.size() < limit) {
            int remainingCount = limit - availableSimilarItems.size();
            logger.info("有效的相似物品不足，补充{}个基于内容的推荐", remainingCount);
            
            // 保存已有的推荐物品ID
            Set<Long> existingIds = availableSimilarItems.stream()
                    .map(Item::getId)
                    .collect(Collectors.toSet());
            existingIds.add(itemId); // 排除当前物品
            
            // 获取额外的基于内容的推荐
            List<ItemResponse> additionalItems = getContentBasedRecommendationsFallback(itemId, limit * 2);
            List<Long> additionalItemIds = additionalItems.stream()
                    .map(item -> Long.parseLong(item.getId().toString()))
                    .filter(id -> !existingIds.contains(id))
                    .limit(remainingCount)
                    .collect(Collectors.toList());
            
            // 获取额外推荐的物品详情
            if (!additionalItemIds.isEmpty()) {
                List<Item> additionalSimilarItems = itemRepository.findAllById(additionalItemIds);
                availableSimilarItems.addAll(additionalSimilarItems);
            }
        }
        
        // 按相似度排序
        Map<Long, Double> scoreMap = similarities.stream()
                .collect(Collectors.toMap(ItemSimilarity::getItemId2, ItemSimilarity::getScore));
        
        availableSimilarItems.sort((a, b) -> 
            Double.compare(scoreMap.getOrDefault(b.getId(), 0.0), scoreMap.getOrDefault(a.getId(), 0.0)));
        
        // 转换为ItemResponse并返回
        return itemService.convertItemsWithFavoriteStatus(availableSimilarItems);
    }
    
    /**
     * 基于内容的推荐（相似物品）备用方法
     * 当相似度表中没有数据时使用
     */
    private List<ItemResponse> getContentBasedRecommendationsFallback(Long itemId, int limit) {
        logger.info("使用基于内容的推荐作为备用方案，物品ID: {}, 限制数量: {}", itemId, limit);

        // 获取当前物品
        Item currentItem = itemRepository.findById(itemId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "物品不存在"));

        // 获取分类和价格信息
        Integer categoryId = currentItem.getCategory().getId();
        BigDecimal currentPrice = currentItem.getPrice();

        // 计算价格范围（±30%）
        BigDecimal minPrice = currentPrice.multiply(new BigDecimal(1 - PRICE_RANGE_FACTOR));
        BigDecimal maxPrice = currentPrice.multiply(new BigDecimal(1 + PRICE_RANGE_FACTOR));

        // 查询同类别、价格范围内的相似物品
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        List<Item> similarItems = itemRepository.findByCategoryIdAndStatusAndIsVisibleAndIdNotAndPriceBetween(
                categoryId,
                ItemStatus.FOR_SALE,
                true,
                itemId,
                minPrice,
                maxPrice,
                pageable);

        // 转换为ItemResponse并返回
        return itemService.convertItemsWithFavoriteStatus(similarItems);
    }

    @Override
    @Scheduled(cron = "0 0 4 * * ?") // 每天凌晨4点执行
    @CacheEvict(value = {"itemCFRecommendations"}, allEntries = true) // 清除缓存
    @Transactional
    public void calculateItemSimilarities() {
        // 这个方法已经被简化，因为我们假设item_similarity表中已经包含了模拟数据
        logger.info("跳过物品相似度计算，使用预先准备的数据");
    }
}
