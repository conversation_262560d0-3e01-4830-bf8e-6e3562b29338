package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "notifications")
public class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id", nullable = false)
    private User recipient; // 通知接收者

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id")
    private User sender; // 通知发送者，系统通知为null

    @NotNull
    @Enumerated(EnumType.STRING)
    private NotificationType type;

    @Column(name = "related_entity_id", nullable = false)
    private Long relatedEntityId;

    @Column(name = "content", columnDefinition = "JSON")
    private String content; // JSON格式的通知内容

    @Column(name = "is_read")
    private Boolean isRead = false;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    // 通知类型枚举
    public enum NotificationType {
        // 闲置物品订单相关通知
        IDLE_NEW_ORDER, // 新订单通知
        IDLE_ORDER_CONFIRMED, // 订单确认通知
        IDLE_ORDER_REJECTED, // 订单拒绝通知
        IDLE_BUYER_ACKNOWLEDGED, // 买家确认通知
        IDLE_ORDER_CANCELLED, // 订单取消通知
        IDLE_ORDER_COMPLETED, // 订单完成通知
        IDLE_CONTACT_CONFIRMED, // 联系确认通知

        // 求好物相关通知
        WANTED_NEW_OFFER, // 新报价通知
        WANTED_OFFER_ACCEPTED, // 报价接受通知
        WANTED_OFFER_REJECTED, // 报价拒绝通知
        WANTED_OFFER_CONFIRMED, // 报价确认通知
        WANTED_OFFERER_CONFIRMED, // 报价者确认通知

        // 通用通知
        TRANSACTION_COMPLETED, // 交易完成通知
        NEW_COMMENT_ON_ITEM, // 新评论通知
        NEW_REPLY_TO_COMMENT, // 评论回复通知
        NEW_RATING_RECEIVED, // 新评价通知

        // 系统通知
        SYSTEM_WELCOME // 欢迎通知
    }

    // Constructors
    public Notification() {
    }

    public Notification(User recipient, User sender, NotificationType type, Long relatedEntityId) {
        this.recipient = recipient;
        this.sender = sender;
        this.type = type;
        this.relatedEntityId = relatedEntityId;
    }

    public Notification(User recipient, User sender, NotificationType type, Long relatedEntityId, String content) {
        this.recipient = recipient;
        this.sender = sender;
        this.type = type;
        this.relatedEntityId = relatedEntityId;
        this.content = content;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getRecipient() {
        return recipient;
    }

    public void setRecipient(User recipient) {
        this.recipient = recipient;
    }

    public User getSender() {
        return sender;
    }

    public void setSender(User sender) {
        this.sender = sender;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public Long getRelatedEntityId() {
        return relatedEntityId;
    }

    public void setRelatedEntityId(Long relatedEntityId) {
        this.relatedEntityId = relatedEntityId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean read) {
        isRead = read;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}