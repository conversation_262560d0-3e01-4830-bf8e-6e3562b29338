package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Rating;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class RatingResponse {

    private Long id;
    private Map<String, Object> rater;
    private Map<String, Object> ratee;
    private Integer score;
    private String transaction_type;
    private Long related_transaction_id;
    private LocalDateTime created_at;
    private Map<String, Object> transaction;

    public RatingResponse() {
    }

    public RatingResponse(Rating rating) {
        this.id = rating.getId();
        this.score = rating.getScore() != null ? rating.getScore().intValue() : null;
        
        // 将枚举类型转换为字符串
        if (rating.getTransactionType() == Rating.TransactionType.IDLE) {
            this.transaction_type = "IDLE";
        } else if (rating.getTransactionType() == Rating.TransactionType.WANTED) {
            this.transaction_type = "WANTED";
        }
        
        this.related_transaction_id = rating.getRelatedTransactionId();
        this.created_at = rating.getCreatedAt();

        // 设置评价人信息
        this.rater = new HashMap<>();
        this.rater.put("id", rating.getRater().getId());
        this.rater.put("username", rating.getRater().getUsername());
        this.rater.put("avatar_url", rating.getRater().getAvatarUrl());

        // 设置被评价人信息
        this.ratee = new HashMap<>();
        this.ratee.put("id", rating.getRatee().getId());
        this.ratee.put("username", rating.getRatee().getUsername());
        this.ratee.put("avatar_url", rating.getRatee().getAvatarUrl());
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Map<String, Object> getRater() {
        return rater;
    }

    public void setRater(Map<String, Object> rater) {
        this.rater = rater;
    }

    public Map<String, Object> getRatee() {
        return ratee;
    }

    public void setRatee(Map<String, Object> ratee) {
        this.ratee = ratee;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Long getRelated_transaction_id() {
        return related_transaction_id;
    }

    public void setRelated_transaction_id(Long related_transaction_id) {
        this.related_transaction_id = related_transaction_id;
    }

    public LocalDateTime getCreated_at() {
        return created_at;
    }

    public void setCreated_at(LocalDateTime created_at) {
        this.created_at = created_at;
    }

    public Map<String, Object> getTransaction() {
        return transaction;
    }

    public void setTransaction(Map<String, Object> transaction) {
        this.transaction = transaction;
    }
}