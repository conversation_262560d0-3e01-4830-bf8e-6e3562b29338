package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.service.FileStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Service
public class FileStorageServiceImpl implements FileStorageService {

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;

    private Path fileStoragePath;

    @Override
    @PostConstruct
    public void init() {
        this.fileStoragePath = Paths.get(uploadDir).toAbsolutePath().normalize();
        try {
            Files.createDirectories(this.fileStoragePath);
        } catch (Exception ex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "无法创建文件上传目录");
        }
    }

    // Method for testing purposes
    public void initForTest(Path path) {
        this.fileStoragePath = path;
        try {
            Files.createDirectories(this.fileStoragePath);
        } catch (Exception ex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "无法创建测试文件上传目录");
        }
    }

    @Override
    public String storeFile(MultipartFile file) {
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        if (originalFilename.contains("..")) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "文件名包含无效路径序列 " + originalFilename);
        }

        String fileExtension = "";
        if (originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        String filename = UUID.randomUUID().toString() + fileExtension;
        Path targetLocation = fileStoragePath.resolve(filename);

        try {
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException ex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "无法存储文件 " + filename);
        }

        return filename;
    }

    @Override
    public Resource loadFileAsResource(String filename) {
        try {
            Path filePath = fileStoragePath.resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists()) {
                return resource;
            } else {
                throw new ApiException(HttpStatus.NOT_FOUND, "文件不存在 " + filename);
            }
        } catch (MalformedURLException ex) {
            throw new ApiException(HttpStatus.NOT_FOUND, "文件不存在 " + filename);
        }
    }

    @Override
    public boolean deleteFile(String filename) {
        try {
            Path filePath = fileStoragePath.resolve(filename).normalize();
            return Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "无法删除文件 " + filename);
        }
    }
} 