package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.service.RatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/ratings")
@Tag(name = "评价 (Ratings)", description = "交易评价相关接口")
public class RatingController {

    private final RatingService ratingService;

    public RatingController(RatingService ratingService) {
        this.ratingService = ratingService;
    }

    @PostMapping
    @Operation(summary = "提交交易评价", description = "在交易完成后，交易双方可调用此接口提交评价。需要指明是为哪种类型的交易（IDLE/WANTED）和具体的交易ID进行评价。")
    @PreAuthorize("isAuthenticated()")
    @SecurityRequirement(name = "JWT")
    public ResponseEntity<ApiResponse<RatingResponse>> createRating(@Valid @RequestBody RatingRequest ratingRequest) {
        RatingResponse ratingResponse = ratingService.createRating(ratingRequest);
        return new ResponseEntity<>(ApiResponse.success("评价创建成功", ratingResponse), HttpStatus.CREATED);
    }
}