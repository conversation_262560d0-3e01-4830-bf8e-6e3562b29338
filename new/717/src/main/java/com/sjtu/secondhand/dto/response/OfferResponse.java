package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Offer;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class OfferResponse {

    private Long id;
    private Long wantedItemId;
    private String wantedItemName;
    private Map<String, Object> offerer;
    private Map<String, Object> requester;
    private String status;
    private Boolean isOffererRated;
    private Boolean isRequesterRated;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public OfferResponse() {
    }

    public OfferResponse(Offer offer) {
        this.id = offer.getId();
        this.wantedItemId = offer.getWantedItem().getId();
        this.wantedItemName = offer.getWantedItem().getName();
        this.status = offer.getStatus().name();
        this.isOffererRated = offer.getIsOffererRated();
        this.isRequesterRated = offer.getIsRequesterRated();
        this.createdAt = offer.getCreatedAt();
        this.updatedAt = offer.getUpdatedAt();

        // 设置响应者信息
        this.offerer = new HashMap<>();
        this.offerer.put("id", offer.getOfferer().getId());
        this.offerer.put("username", offer.getOfferer().getUsername());
        this.offerer.put("avatar", offer.getOfferer().getAvatarUrl());

        // 设置求购者信息
        this.requester = new HashMap<>();
        this.requester.put("id", offer.getRequester().getId());
        this.requester.put("username", offer.getRequester().getUsername());
        this.requester.put("avatar", offer.getRequester().getAvatarUrl());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWantedItemId() {
        return wantedItemId;
    }

    public void setWantedItemId(Long wantedItemId) {
        this.wantedItemId = wantedItemId;
    }

    public String getWantedItemName() {
        return wantedItemName;
    }

    public void setWantedItemName(String wantedItemName) {
        this.wantedItemName = wantedItemName;
    }

    public Map<String, Object> getOfferer() {
        return offerer;
    }

    public void setOfferer(Map<String, Object> offerer) {
        this.offerer = offerer;
    }

    public Map<String, Object> getRequester() {
        return requester;
    }

    public void setRequester(Map<String, Object> requester) {
        this.requester = requester;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsOffererRated() {
        return isOffererRated;
    }

    public void setIsOffererRated(Boolean offererRated) {
        isOffererRated = offererRated;
    }

    public Boolean getIsRequesterRated() {
        return isRequesterRated;
    }

    public void setIsRequesterRated(Boolean requesterRated) {
        isRequesterRated = requesterRated;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}