package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OfferRepository;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.OfferService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class OfferServiceImpl implements OfferService {

    private final OfferRepository offerRepository;
    private final ItemRepository itemRepository;
    private final UserService userService;
    private final NotificationService notificationService;

    @Autowired
    public OfferServiceImpl(OfferRepository offerRepository, ItemRepository itemRepository, 
                           UserService userService, NotificationService notificationService) {
        this.offerRepository = offerRepository;
        this.itemRepository = itemRepository;
        this.userService = userService;
        this.notificationService = notificationService;
    }

    @Override
    @Transactional
    public OfferResponse createOffer(OfferRequest offerRequest) {
        // 这个方法不应该被调用，因为我们现在使用带有wantedItemId参数的方法
        throw new UnsupportedOperationException("请使用带有wantedItemId参数的createOffer方法");
    }

    @Override
    @Transactional
    public OfferResponse createOffer(Long wantedItemId, OfferRequest offerRequest) {
        User currentUser = userService.getCurrentUser();
        
        // 获取求购物品
        Item wantedItem = itemRepository.findById(wantedItemId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "求购物品不存在"));
        
        // 检查物品类型
        if (wantedItem.getItemType() != Item.ItemType.WANTED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只能对求购类型的物品创建Offer");
        }
        
        // 检查物品状态
        if (wantedItem.getStatus() != Item.ItemStatus.FOR_SALE) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "该求购物品已不可响应");
        }
        
        // 检查是否是自己的求购物品
        if (wantedItem.getUser().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "不能响应自己的求购");
        }
        
        // 创建Offer
        Offer offer = new Offer();
        offer.setWantedItem(wantedItem);
        offer.setOfferer(currentUser);
        offer.setRequester(wantedItem.getUser());
        offer.setStatus(Offer.OfferStatus.PENDING_ACCEPTANCE);
        
        Offer savedOffer = offerRepository.save(offer);
        
        // 发送通知
        notificationService.createNewOfferNotification(savedOffer);
        
        return new OfferResponse(savedOffer);
    }

    @Override
    @Transactional(readOnly = true)
    public OfferResponse getOfferById(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = getOfferAndCheckPermission(offerId, currentUser);
        
        return new OfferResponse(offer);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OfferResponse> getMyOffers(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Offer> offers = offerRepository.findByOfferer(currentUser, pageable);
        
        return offers.map(OfferResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OfferResponse> getMyRequests(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Offer> offers = offerRepository.findByRequester(currentUser, pageable);
        
        return offers.map(OfferResponse::new);
    }

    @Override
    @Transactional
    public OfferResponse acceptOffer(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为求购者
        if (!offer.getRequester().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有求购者可以接受Offer");
        }
        
        // 检查Offer状态
        if (offer.getStatus() != Offer.OfferStatus.PENDING_ACCEPTANCE) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有待接受的Offer可以被接受");
        }
        
        // 更新Offer状态
        offer.setStatus(Offer.OfferStatus.ACCEPTED);
        offer.setUpdatedAt(LocalDateTime.now());
        Offer updatedOffer = offerRepository.save(offer);
        
        // 更新物品状态
        Item wantedItem = offer.getWantedItem();
        wantedItem.setStatus(Item.ItemStatus.RESERVED);
        itemRepository.save(wantedItem);
        
        // 发送通知
        notificationService.createOfferAcceptedNotification(updatedOffer);
        
        return new OfferResponse(updatedOffer);
    }

    @Override
    @Transactional
    public OfferResponse rejectOffer(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为求购者
        if (!offer.getRequester().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有求购者可以拒绝Offer");
        }
        
        // 检查Offer状态
        if (offer.getStatus() != Offer.OfferStatus.PENDING_ACCEPTANCE) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有待接受的Offer可以被拒绝");
        }
        
        // 更新Offer状态
        offer.setStatus(Offer.OfferStatus.REJECTED);
        offer.setUpdatedAt(LocalDateTime.now());
        Offer updatedOffer = offerRepository.save(offer);
        
        // 发送通知
        notificationService.createOfferRejectedNotification(updatedOffer);
        
        return new OfferResponse(updatedOffer);
    }

    @Override
    @Transactional
    public OfferResponse confirmOffer(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为响应者
        if (!offer.getOfferer().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有响应者可以确认Offer");
        }
        
        // 检查Offer状态
        if (offer.getStatus() != Offer.OfferStatus.ACCEPTED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有已接受的Offer可以被确认");
        }
        
        // 更新Offer状态
        offer.setStatus(Offer.OfferStatus.CONFIRMED);
        offer.setUpdatedAt(LocalDateTime.now());
        Offer updatedOffer = offerRepository.save(offer);
        
        // 发送通知
        notificationService.createOfferConfirmedNotification(updatedOffer);
        
        return new OfferResponse(updatedOffer);
    }

    @Override
    @Transactional
    public OfferResponse completeOffer(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为交易参与方
        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());
        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());
        
        if (!isOfferer && !isRequester) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有交易参与方可以完成交易");
        }
        
        // 检查Offer状态
        if (offer.getStatus() != Offer.OfferStatus.CONFIRMED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有已确认的Offer可以被标记为完成");
        }
        
        // 更新Offer状态
        offer.setStatus(Offer.OfferStatus.COMPLETED);
        offer.setUpdatedAt(LocalDateTime.now());
        Offer updatedOffer = offerRepository.save(offer);
        
        // 更新物品状态
        Item wantedItem = offer.getWantedItem();
        wantedItem.setStatus(Item.ItemStatus.SOLD);
        itemRepository.save(wantedItem);
        
        // 发送通知 - 使用通用的创建通知方法
        notificationService.createNotification(
            offer.getRequester(), 
            offer.getOfferer(), 
            Notification.NotificationType.TRANSACTION_COMPLETED, 
            offer.getId(), 
            "您的交易已完成"
        );
        
        return new OfferResponse(updatedOffer);
    }

    @Override
    @Transactional
    public OfferResponse cancelOffer(Long offerId) {
        User currentUser = userService.getCurrentUser();
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为交易参与方
        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());
        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());
        
        if (!isOfferer && !isRequester) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有交易参与方可以取消交易");
        }
        
        // 检查Offer状态
        if (offer.getStatus() == Offer.OfferStatus.COMPLETED || offer.getStatus() == Offer.OfferStatus.CANCELLED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "已完成或已取消的Offer不能被取消");
        }
        
        // 更新Offer状态
        offer.setStatus(Offer.OfferStatus.CANCELLED);
        offer.setUpdatedAt(LocalDateTime.now());
        Offer updatedOffer = offerRepository.save(offer);
        
        // 如果Offer已被接受，需要更新物品状态
        if (offer.getStatus() == Offer.OfferStatus.ACCEPTED || offer.getStatus() == Offer.OfferStatus.CONFIRMED) {
            Item wantedItem = offer.getWantedItem();
            wantedItem.setStatus(Item.ItemStatus.FOR_SALE);
            itemRepository.save(wantedItem);
        }
        
        // 发送通知 - 使用通用的创建通知方法
        User recipient = isOfferer ? offer.getRequester() : offer.getOfferer();
        notificationService.createNotification(
            recipient, 
            currentUser, 
            Notification.NotificationType.WANTED_OFFER_REJECTED, // 使用已有的通知类型
            offer.getId(), 
            "交易已被取消"
        );
        
        return new OfferResponse(updatedOffer);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OfferResponse> getMyOffersByStatus(String status, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Offer.OfferStatus offerStatus = parseOfferStatus(status);
        Page<Offer> offers = offerRepository.findByOffererAndStatus(currentUser, offerStatus, pageable);
        
        return offers.map(OfferResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OfferResponse> getMyRequestsByStatus(String status, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Offer.OfferStatus offerStatus = parseOfferStatus(status);
        Page<Offer> offers = offerRepository.findByRequesterAndStatus(currentUser, offerStatus, pageable);
        
        return offers.map(OfferResponse::new);
    }
    
    private Offer getOfferAndCheckPermission(Long offerId, User currentUser) {
        Offer offer = offerRepository.findById(offerId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "Offer不存在"));
        
        // 验证当前用户是否为交易参与方
        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());
        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());
        
        if (!isOfferer && !isRequester) {
            throw new ApiException(HttpStatus.FORBIDDEN, "您无权访问此Offer");
        }
        
        return offer;
    }
    
    private Offer.OfferStatus parseOfferStatus(String status) {
        try {
            return Offer.OfferStatus.valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "无效的Offer状态");
        }
    }
} 