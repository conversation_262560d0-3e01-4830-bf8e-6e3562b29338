package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OfferRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.OrderService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class OrderServiceImpl implements OrderService {

    private final OrderRepository orderRepository;
    private final ItemRepository itemRepository;
    private final OfferRepository offerRepository;
    private final UserService userService;
    private final NotificationService notificationService;
    private final NotificationEventService notificationEventService;

    public OrderServiceImpl(OrderRepository orderRepository,
            ItemRepository itemRepository,
            OfferRepository offerRepository,
            UserService userService,
            NotificationService notificationService,
            NotificationEventService notificationEventService) {
        this.orderRepository = orderRepository;
        this.itemRepository = itemRepository;
        this.offerRepository = offerRepository;
        this.userService = userService;
        this.notificationService = notificationService;
        this.notificationEventService = notificationEventService;
    }

    @Override
    @Transactional
    public OrderResponse createOrder(OrderRequest orderRequest) {
        User buyer = userService.getCurrentUser();
        Item item = itemRepository.findById(orderRequest.getItem_id().longValue())
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "商品不存在"));

        // 检查商品状态
        if (item.getStatus() != Item.ItemStatus.FOR_SALE) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "该商品不可购买");
        }

        // 检查是否已有进行中的订单
        boolean hasActiveOrder = orderRepository.existsActiveOrderForItem(item);
        if (hasActiveOrder) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "该商品已有进行中的订单");
        }

        // 检查是否是自己的商品
        if (item.getUser().getId().equals(buyer.getId())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "不能购买自己的商品");
        }

        // 创建订单
        Order order = new Order();
        order.setItem(item);
        order.setBuyer(buyer);
        order.setSeller(item.getUser());
        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);

        // 注意：根据API文档，OrderRequest中不再包含offer_id字段
        // 如果需要关联报价，应该在其他地方处理

        Order savedOrder = orderRepository.save(order);

        // 更新商品状态
        item.setStatus(Item.ItemStatus.RESERVED);
        itemRepository.save(item);

        // 发送通知
        notificationService.createNewOrderNotification(savedOrder);

        // 发送实时更新
        try {
            notificationEventService.sendOrderUpdateEvent(
                    savedOrder,
                    "created",
                    "新订单已创建，等待卖家确认");
        } catch (Exception e) {
            // Just log the error, don't fail the operation
            System.err.println("Failed to send order update event: " + e.getMessage());
        }

        return convertToOrderResponse(savedOrder);
    }

    @Override
    @Transactional(readOnly = true)
    public OrderResponse getOrderById(Long orderId) {
        User currentUser = userService.getCurrentUser();
        Order order = getOrderAndCheckPermission(orderId, currentUser);

        return new OrderResponse(order);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderResponse> getMyBoughtOrders(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Order> orders = orderRepository.findByBuyer(currentUser, pageable);

        return orders.map(OrderResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderResponse> getMySoldOrders(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Order> orders = orderRepository.findBySeller(currentUser, pageable);

        return orders.map(OrderResponse::new);
    }

    @Override
    @Transactional
    public OrderResponse confirmOrder(Long orderId) {
        User currentUser = userService.getCurrentUser();
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证当前用户是否为卖家
        if (!order.getSeller().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有卖家可以确认订单");
        }

        // 检查订单状态
        if (order.getStatus() != Order.OrderStatus.PENDING_CONFIRMATION) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有待确认的订单可以被确认");
        }

        // 更新订单状态
        order.setStatus(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);
        order.setUpdatedAt(LocalDateTime.now());
        Order updatedOrder = orderRepository.save(order);

        // 发送通知
        notificationService.createOrderConfirmedNotification(updatedOrder);

        // 发送实时更新
        try {
            notificationEventService.sendOrderUpdateEvent(
                    updatedOrder,
                    "confirmed",
                    "订单已确认，等待买家确认联系方式");
        } catch (Exception e) {
            // Just log the error, don't fail the operation
            System.err.println("Failed to send order update event: " + e.getMessage());
        }

        return new OrderResponse(updatedOrder);
    }

    @Override
    @Transactional
    public OrderResponse cancelOrder(Long orderId) {
        User currentUser = userService.getCurrentUser();
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证当前用户是买家或卖家
        boolean isBuyer = order.getBuyer().getId().equals(currentUser.getId());
        boolean isSeller = order.getSeller().getId().equals(currentUser.getId());

        if (!isBuyer && !isSeller) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有买家或卖家可以取消订单");
        }

        // 检查订单状态
        if (order.getStatus() == Order.OrderStatus.COMPLETED || order.getStatus() == Order.OrderStatus.CANCELLED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "已完成或已取消的订单不能被取消");
        }

        // 更新订单状态
        order.setStatus(Order.OrderStatus.CANCELLED);
        order.setUpdatedAt(LocalDateTime.now());
        Order updatedOrder = orderRepository.save(order);

        // 更新商品状态
        Item item = order.getItem();
        item.setStatus(Item.ItemStatus.FOR_SALE);
        itemRepository.save(item);

        // 发送通知
        notificationService.createOrderCancelledNotification(updatedOrder, currentUser);

        // 发送实时更新
        try {
            notificationEventService.sendOrderUpdateEvent(
                    updatedOrder,
                    "cancelled",
                    "订单已取消，物品重新上架");
        } catch (Exception e) {
            // Just log the error, don't fail the operation
            System.err.println("Failed to send order update event: " + e.getMessage());
        }

        return convertToOrderResponse(updatedOrder);
    }

    @Override
    @Transactional
    public OrderResponse confirmContact(Long orderId) {
        User currentUser = userService.getCurrentUser();
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证当前用户是否为买家
        if (!order.getBuyer().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有买家可以确认联系");
        }

        // 检查订单状态
        if (order.getStatus() != Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有待确认联系的订单可以被确认联系");
        }

        // 更新订单状态
        order.setStatus(Order.OrderStatus.CONFIRMED);
        order.setUpdatedAt(LocalDateTime.now());
        Order updatedOrder = orderRepository.save(order);

        // 发送通知
        notificationService.createContactConfirmedNotification(updatedOrder);

        return convertToOrderResponse(updatedOrder);
    }

    @Override
    @Transactional
    public OrderResponse completeOrder(Long orderId) {
        User currentUser = userService.getCurrentUser();
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证当前用户是否为买家
        if (!order.getBuyer().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "只有买家可以完成订单");
        }

        // 检查订单状态
        if (order.getStatus() != Order.OrderStatus.CONFIRMED) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "只有已确认的订单可以被完成");
        }

        // 更新订单状态
        order.setStatus(Order.OrderStatus.COMPLETED);
        order.setUpdatedAt(LocalDateTime.now());
        Order updatedOrder = orderRepository.save(order);

        // 更新商品状态
        Item item = order.getItem();
        item.setStatus(Item.ItemStatus.SOLD);
        itemRepository.save(item);

        // 发送通知
        notificationService.createOrderCompletedNotification(updatedOrder);

        // 发送实时更新
        try {
            notificationEventService.sendOrderUpdateEvent(
                    updatedOrder,
                    "completed",
                    "交易已完成，物品已售出");
        } catch (Exception e) {
            // Just log the error, don't fail the operation
            System.err.println("Failed to send order update event: " + e.getMessage());
        }

        return convertToOrderResponse(updatedOrder);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderResponse> getMyBoughtOrdersByStatus(String status, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Order.OrderStatus orderStatus = parseOrderStatus(status);

        Page<Order> orders = orderRepository.findByBuyerAndStatus(currentUser, orderStatus, pageable);
        return orders.map(OrderResponse::new);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderResponse> getMySoldOrdersByStatus(String status, Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Order.OrderStatus orderStatus = parseOrderStatus(status);

        Page<Order> orders = orderRepository.findBySellerAndStatus(currentUser, orderStatus, pageable);
        return orders.map(OrderResponse::new);
    }

    // 辅助方法：获取订单并检查权限
    private Order getOrderAndCheckPermission(Long orderId, User currentUser) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, "订单不存在"));

        // 验证是否是买家或卖家
        if (!order.getBuyer().getId().equals(currentUser.getId()) &&
                !order.getSeller().getId().equals(currentUser.getId())) {
            throw new ApiException(HttpStatus.FORBIDDEN, "没有权限访问该订单");
        }

        return order;
    }

    // 辅助方法：解析订单状态字符串
    private Order.OrderStatus parseOrderStatus(String status) {
        try {
            return Order.OrderStatus.valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, "无效的订单状态");
        }
    }

    // 辅助方法：将Order对象转换为OrderResponse
    private OrderResponse convertToOrderResponse(Order order) {
        return new OrderResponse(order);
    }
}