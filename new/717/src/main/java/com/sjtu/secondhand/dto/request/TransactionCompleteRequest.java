package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class TransactionCompleteRequest {

    @NotBlank(message = "交易类型不能为空")
    private String transaction_type; // IDLE代表订单，WANTED代表求购响应

    @NotNull(message = "交易ID不能为空")
    private Integer transaction_id; // 对应的 orders.id 或 offers.id

    // Getters and setters
    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Integer getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(Integer transaction_id) {
        this.transaction_id = transaction_id;
    }
}