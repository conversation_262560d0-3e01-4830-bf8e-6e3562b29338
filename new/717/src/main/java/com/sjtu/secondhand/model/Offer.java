package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "offers")
public class Offer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "wanted_item_id", nullable = false)
    private Item wantedItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "offerer_id", nullable = false)
    private User offerer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requester_id", nullable = false)
    private User requester;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OfferStatus status;

    @Column(name = "is_offerer_rated")
    private Boolean isOffererRated = false;

    @Column(name = "is_requester_rated")
    private Boolean isRequesterRated = false;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public enum OfferStatus {
        PENDING_ACCEPTANCE, // 等待求购者接受
        REJECTED, // 求购者已拒绝
        ACCEPTED, // 求购者已接受
        CONFIRMED, // 响应者已确认
        COMPLETED, // 交易完成
        CANCELLED // 交易取消
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            status = OfferStatus.PENDING_ACCEPTANCE;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Constructors
    public Offer() {
    }

    public Offer(Item wantedItem, User offerer, User requester) {
        this.wantedItem = wantedItem;
        this.offerer = offerer;
        this.requester = requester;
        this.status = OfferStatus.PENDING_ACCEPTANCE;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Item getWantedItem() {
        return wantedItem;
    }

    public void setWantedItem(Item wantedItem) {
        this.wantedItem = wantedItem;
    }

    public User getOfferer() {
        return offerer;
    }

    public void setOfferer(User offerer) {
        this.offerer = offerer;
    }

    public User getRequester() {
        return requester;
    }

    public void setRequester(User requester) {
        this.requester = requester;
    }

    public OfferStatus getStatus() {
        return status;
    }

    public void setStatus(OfferStatus status) {
        this.status = status;
    }

    public Boolean getIsOffererRated() {
        return isOffererRated;
    }

    public void setIsOffererRated(Boolean offererRated) {
        isOffererRated = offererRated;
    }

    public Boolean getIsRequesterRated() {
        return isRequesterRated;
    }

    public void setIsRequesterRated(Boolean requesterRated) {
        isRequesterRated = requesterRated;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}