package com.sjtu.secondhand.service;

import com.sjtu.secondhand.dto.response.ItemResponse;

import java.util.List;

/**
 * 推荐系统服务接口
 */
public interface RecommendationService {

    /**
     * 获取全局热门推荐（基于热度分）
     * 
     * @param limit 返回结果数量
     * @return 推荐物品列表
     */
    List<ItemResponse> getHotRecommendations(int limit);

    /**
     * 获取基于物品的协同过滤推荐
     * 
     * @param userId 用户ID
     * @param limit  返回结果数量
     * @return 推荐物品列表
     */
    List<ItemResponse> getItemCFRecommendations(Long userId, int limit);

    /**
     * 获取基于内容的推荐（相似物品）
     * 
     * @param itemId 物品ID
     * @param limit  返回结果数量
     * @return 推荐物品列表
     */
    List<ItemResponse> getContentBasedRecommendations(Long itemId, int limit);
    
    /**
     * 执行离线计算任务，计算物品相似度（Item-CF）
     */
    void calculateItemSimilarities();
}
