package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;

public class CommentCreate {

    @NotBlank(message = "评论内容不能为空")
    private String content;
    
    private Integer parent_id;

    public CommentCreate() {
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }
} 