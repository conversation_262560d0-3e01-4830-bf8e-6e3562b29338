package com.sjtu.secondhand.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

/**
 * Web配置类
 * 用于配置CORS跨域等Web相关配置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;
    
    @Value("${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}")
    private String allowedOrigins;

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        String[] origins = allowedOrigins.split(",");
        logger.info("配置CORS，允许的域名: {}", allowedOrigins);
        
        registry.addMapping("/**")
                .allowedOrigins(origins)
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*")
                .exposedHeaders("Authorization", "Content-Type", "Content-Length", 
                               "Content-Disposition", "ETag", "x-oss-request-id")
                .allowCredentials(true)
                .maxAge(3600);
                
        logger.info("CORS配置完成");
    }

    /**
     * 添加CORS过滤器，确保所有请求都经过CORS处理
     */
    @Bean
    public CorsFilter corsFilter() {
        logger.info("初始化CORS过滤器");
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        String[] origins = allowedOrigins.split(",");
        
        // 允许所有来源
        corsConfiguration.setAllowedOrigins(Arrays.asList(origins));
        // 允许所有头
        corsConfiguration.addAllowedHeader("*");
        // 允许所有方法
        corsConfiguration.addAllowedMethod("*");
        // 允许携带凭证
        corsConfiguration.setAllowCredentials(true);
        // 暴露响应头
        corsConfiguration.addExposedHeader("Authorization");
        corsConfiguration.addExposedHeader("Content-Type");
        corsConfiguration.addExposedHeader("Content-Length");
        corsConfiguration.addExposedHeader("Content-Disposition");
        corsConfiguration.addExposedHeader("ETag");
        corsConfiguration.addExposedHeader("x-oss-request-id");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        
        logger.info("CORS过滤器配置完成");
        return new CorsFilter(source);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件目录的静态资源映射
        Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
        logger.info("配置静态资源映射: /uploads/** -> {}", uploadPath.toString());
        
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + uploadPath.toString() + "/");
    }
}
