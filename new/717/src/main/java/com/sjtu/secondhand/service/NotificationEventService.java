package com.sjtu.secondhand.service;

import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Order;

/**
 * 通知事件服务接口
 * 用于发送实时通知事件，主要用于Server-Sent Events (SSE)实现
 */
public interface NotificationEventService {
    
    /**
     * 发送订单更新事件
     * 
     * @param order 订单对象
     * @param eventType 事件类型（如created, confirmed, cancelled等）
     * @param message 事件消息
     */
    void sendOrderUpdateEvent(Order order, String eventType, String message);
    
    /**
     * 发送通知事件
     * 
     * @param notification 通知对象
     */
    void sendNotificationEvent(Notification notification);
}
