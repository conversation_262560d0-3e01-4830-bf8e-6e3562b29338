package com.sjtu.secondhand.service;

import com.sjtu.secondhand.model.User;

public interface UserService {

    /**
     * 获取当前登录用户
     * 
     * @return 当前登录用户
     */
    User getCurrentUser();

    /**
     * 根据用户ID获取用户
     * 
     * @param userId 用户ID
     * @return 用户对象
     */
    User getUserById(Long userId);

    /**
     * 根据用户名获取用户
     * 
     * @param username 用户名
     * @return 用户对象
     */
    User getUserByUsername(String username);

    /**
     * 更新用户信息
     * 
     * @param userUpdates 需要更新的用户信息
     * @return 更新后的用户对象
     */
    User updateUser(User userUpdates);

    /**
     * 保存用户信息
     * 
     * @param user 用户对象
     * @return 保存后的用户对象
     */
    User saveUser(User user);
}