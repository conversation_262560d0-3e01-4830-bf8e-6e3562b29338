package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.security.CustomUserDetails;
import com.sjtu.secondhand.service.RecommendationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能推荐系统控制器
 */
@RestController
@RequestMapping("/recommendations")
public class RecommendationController {

    private static final Logger logger = LoggerFactory.getLogger(RecommendationController.class);
    private final RecommendationService recommendationService;
    
    // 默认推荐数量参数
    private static final int DEFAULT_HOME_RECOMMENDATIONS = 16;  // 首页推荐默认数量
    private static final int DEFAULT_SIMILAR_ITEMS = 4;        // 商品详情页相似物品默认数量

    @Autowired
    public RecommendationController(RecommendationService recommendationService) {
        this.recommendationService = recommendationService;
    }

    /**
     * 获取全局热门推荐
     * 用于首页热门展示和冷启动场景
     * 
     * @param limit 限制返回结果数量，默认为16
     * @return 热门物品列表
     */
    @GetMapping("/hot")
    public ResponseEntity<ApiResponse<List<ItemResponse>>> getHotRecommendations(
            @RequestParam(value = "limit", defaultValue = "16") int limit) {
        logger.info("请求热门推荐，限制数量: {}", limit);
        List<ItemResponse> recommendations = recommendationService.getHotRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }

    /**
     * 获取首页"为你推荐"个性化推荐列表
     * 基于用户收藏的物品进行协同过滤推荐
     * 如果用户没有收藏（冷启动），则返回热门推荐
     * 
     * @param userDetails 当前登录用户
     * @param limit 限制返回结果数量，默认为16
     * @return 个性化推荐物品列表
     */
    @GetMapping("/for-you")
    public ResponseEntity<ApiResponse<List<ItemResponse>>> getForYouRecommendations(
            @AuthenticationPrincipal CustomUserDetails userDetails,
            @RequestParam(value = "limit", defaultValue = "16") int limit) {
        
        if (userDetails == null) {
            // 未登录用户，返回热门推荐
            logger.info("未登录用户请求首页推荐，返回热门物品");
            List<ItemResponse> recommendations = recommendationService.getHotRecommendations(limit);
            return ResponseEntity.ok(ApiResponse.success(recommendations));
        }
        
        // 已登录用户，返回个性化推荐
        logger.info("用户 {} 请求首页个性化推荐", userDetails.getUsername());
        List<ItemResponse> recommendations = recommendationService.getItemCFRecommendations(
                userDetails.getUserId(), limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    /**
     * 获取商品详情页的"猜你喜欢"相似物品
     * 直接从item_similarity表获取相似度最高的物品
     * 
     * @param itemId 当前浏览的物品ID
     * @param limit 限制返回结果数量，默认为4
     * @return 相似物品列表
     */
    @GetMapping("/similar/{itemId}")
    public ResponseEntity<ApiResponse<List<ItemResponse>>> getSimilarItems(
            @PathVariable Long itemId,
            @RequestParam(value = "limit", defaultValue = "4") int limit) {
        logger.info("请求物品 {} 的相似推荐，限制数量: {}", itemId, limit);
        List<ItemResponse> recommendations = recommendationService.getContentBasedRecommendations(itemId, limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }

    /**
     * 手动触发离线计算物品相似度
     * 仅限管理员使用（此功能在简化方案中已不需要，保留API兼容性）
     */
    @PostMapping("/admin/recalculate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> triggerSimilarityCalculation() {
        logger.info("手动触发物品相似度计算");
        recommendationService.calculateItemSimilarities();
        return ResponseEntity.ok(ApiResponse.success("操作成功，使用预置的相似度数据"));
    }
} 