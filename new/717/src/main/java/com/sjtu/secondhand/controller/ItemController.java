package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.dto.response.CommentResponse;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.repository.CommentRepository;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/items")
@Tag(name = "物品 (Items)", description = "物品相关接口")
public class ItemController {

    private static final Logger logger = LoggerFactory.getLogger(ItemController.class);

    private final ItemService itemService;
    private final UserRepository userRepository;
    private final ItemRepository itemRepository;
    private final CommentRepository commentRepository;
    private final ElasticsearchSyncService elasticsearchSyncService;

    public ItemController(ItemService itemService, UserRepository userRepository, ItemRepository itemRepository,
            CommentRepository commentRepository, ElasticsearchSyncService elasticsearchSyncService) {
        this.itemService = itemService;
        this.userRepository = userRepository;
        this.itemRepository = itemRepository;
        this.commentRepository = commentRepository;
        this.elasticsearchSyncService = elasticsearchSyncService;
    }

    @GetMapping
    @Operation(summary = "获取物品列表（支持筛选、排序和分页）", description = "获取平台上的物品列表，可通过多种参数进行筛选、排序和分页")
    public ResponseEntity<ApiResponse<ItemPageResponse>> getItems(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "sort", defaultValue = "latest") String sort,
            @RequestParam(value = "item_type", required = false) String itemType,
            @RequestParam(value = "category_id", required = false) String categoryIdStr,
            @RequestParam(value = "price_min", required = false) Double priceMin,
            @RequestParam(value = "price_max", required = false) Double priceMax,
            @RequestParam(value = "q", required = false) String keyword,
            @RequestParam(value = "user_id", required = false) Long userId) {
        
        System.out.println("GET /items 接收参数: page=" + page + ", size=" + size + ", sort=" + sort
                + ", item_type=" + itemType + ", category_id=" + categoryIdStr
                + ", price_min=" + priceMin + ", price_max=" + priceMax
                + ", q=" + keyword + ", user_id=" + userId);
        
        // 将页码转换为从0开始，以符合Spring Data的Page接口
        page = page - 1;
        if (page < 0) page = 0;
        
        // 根据sort参数确定排序字段和方向
        String sortField = "createdAt";
        Sort.Direction direction = Sort.Direction.DESC;
        
        switch (sort.toLowerCase()) {
            case "latest":
                sortField = "createdAt";
                direction = Sort.Direction.DESC;
                break;
            case "hot":
                sortField = "viewCount";
                direction = Sort.Direction.DESC;
                break;
            case "price_asc":
                sortField = "price";
                direction = Sort.Direction.ASC;
                break;
            case "price_desc":
                sortField = "price";
                direction = Sort.Direction.DESC;
                break;
        }

        Sort sortObj = Sort.by(direction, sortField);
        Pageable pageable = PageRequest.of(page, size, sortObj);

        try {
            // 调用服务层方法，传递所有筛选条件
            Page<Item> itemPage = itemService.getFilteredItems(
                    itemType, categoryIdStr, priceMin, priceMax, keyword, userId, pageable);

            // 使用新的批量处理方法设置收藏状态
            List<ItemResponse> itemResponses = itemService.convertItemsWithFavoriteStatus(itemPage.getContent());

            ItemPageResponse response = new ItemPageResponse(itemResponses, itemPage);

            // 返回符合测试期望格式的响应
            return ResponseEntity.ok(ApiResponse.success("操作成功", response));
        } catch (Exception e) {
            System.err.println("获取物品列表控制器异常: " + e.getMessage());
            e.printStackTrace(); // 打印完整堆栈信息
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("500 INTERNAL_SERVER_ERROR", "获取物品列表失败: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取物品详情", description = "根据ID获取单个物品的完整详细信息")
    public ResponseEntity<ApiResponse<ItemResponse>> getItemById(@PathVariable Long id) {
        System.out.println("【收藏量调试】控制器接收到获取物品详情请求，物品ID: " + id);
        
        ItemResponse itemResponse = itemService.getItemById(id);
        
        System.out.println("【收藏量调试】控制器获取到物品详情，物品ID: " + id + "，收藏量: " + itemResponse.getFavoriteCount());
        
        return ResponseEntity.ok(ApiResponse.success("操作成功", itemResponse));
    }

    @PostMapping
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "发布商品", description = "发布新商品信息", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<ApiResponse<ItemResponse>> createItem(@Valid @RequestBody ItemRequest itemRequest) {
        try {
            System.out.println("接收到创建商品请求: " + itemRequest.getName() + ", 类型: " + itemRequest.getItem_type());
            System.out.println("商品分类ID: " + itemRequest.getCategory_id() + ", 状态: " + itemRequest.getCondition());
            System.out.println("图片URL: "
                    + (itemRequest.getImage_urls() != null ? String.join(", ", itemRequest.getImage_urls()) : "无"));

            // 验证必要的字段
            if (itemRequest.getName() == null || itemRequest.getName().isEmpty()) {
                System.err.println("商品名称为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("商品名称不能为空"));
            }

            if (itemRequest.getCategory_id() == null) {
                System.err.println("商品分类ID为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("商品分类ID不能为空"));
            }

            if (itemRequest.getCondition() == null || itemRequest.getCondition().isEmpty()) {
                System.err.println("商品状态为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("商品状态不能为空"));
            }

            ItemResponse createdItem = itemService.createItem(itemRequest);
            System.out.println("商品创建成功，ID: " + createdItem.getId());
            return ResponseEntity.ok(ApiResponse.success(createdItem));
        } catch (Exception e) {
            System.err.println("创建商品失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("创建商品失败: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新物品信息", description = "物主可以更新自己发布的物品信息")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<ItemResponse>> updateItem(
            @PathVariable Long id,
            @Valid @RequestBody ItemRequest itemRequest) {
        ItemResponse updatedItem = itemService.updateItem(id, itemRequest);
        return ResponseEntity.ok(ApiResponse.success("物品更新成功", updatedItem));
    }

    @PatchMapping("/{id}/status")
    @Operation(summary = "更新物品状态", description = "更新物品的上架/下架状态")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<ItemResponse>> updateItemStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusData) {
        try {
            String status = statusData.get("status");
            if (status == null || status.isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("状态不能为空"));
            }

            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取物品
            Item item = itemRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("物品不存在"));

            // 验证物品所有者
            if (!item.getUser().getId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("您没有权限更新此物品"));
            }

            // 更新物品状态
            if ("listed".equals(status)) {
                item.setIsVisible(true);
            } else if ("unlisted".equals(status)) {
                item.setIsVisible(false);
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的状态值，必须是 'listed' 或 'unlisted'"));
            }

            // 保存更新
            item = itemRepository.save(item);

            // 返回更新后的物品
            ItemResponse itemResponse = new ItemResponse(item);
            return ResponseEntity.ok(ApiResponse.success("物品状态更新成功", itemResponse));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("更新物品状态失败: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除物品", description = "删除指定ID的物品")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Void>> deleteItem(@PathVariable Long id) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取物品
            Item item = itemRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("物品不存在"));

            // 验证物品所有者
            if (!item.getUser().getId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("您没有权限删除此物品"));
            }

            // 删除物品
            itemRepository.delete(item);

            return ResponseEntity.ok(ApiResponse.success("物品删除成功", null));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("删除物品失败: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/favorite")
    @Operation(summary = "收藏物品", description = "将指定ID的物品添加到当前用户的收藏列表")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Void>> addToFavorites(@PathVariable("id") Long itemId) {
        System.out.println("【收藏量调试】控制器接收到收藏物品请求，物品ID: " + itemId);
        
        Long userId = getCurrentUserId();
        System.out.println("【收藏量调试】当前用户ID: " + userId);
        
        itemService.addToFavorites(itemId, userId);
        
        // 收藏操作后，立即获取物品详情，检查收藏量是否更新
        try {
            ItemResponse updatedItem = itemService.getItemById(itemId);
            System.out.println("【收藏量调试】收藏操作后，物品ID: " + itemId + "，最新收藏量: " + updatedItem.getFavoriteCount());
        } catch (Exception e) {
            System.out.println("【收藏量调试】获取更新后的物品详情失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(ApiResponse.success("收藏成功", null));
    }

    @DeleteMapping("/{id}/favorite")
    @Operation(summary = "取消收藏物品", description = "将指定ID的物品从当前用户的收藏列表中移除")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Void>> removeFromFavorites(@PathVariable("id") Long itemId) {
        System.out.println("【收藏量调试】控制器接收到取消收藏物品请求，物品ID: " + itemId);
        
        Long userId = getCurrentUserId();
        System.out.println("【收藏量调试】当前用户ID: " + userId);
        
        itemService.removeFromFavorites(itemId, userId);
        
        // 取消收藏操作后，立即获取物品详情，检查收藏量是否更新
        try {
            ItemResponse updatedItem = itemService.getItemById(itemId);
            System.out.println("【收藏量调试】取消收藏操作后，物品ID: " + itemId + "，最新收藏量: " + updatedItem.getFavoriteCount());
        } catch (Exception e) {
            System.out.println("【收藏量调试】获取更新后的物品详情失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(ApiResponse.success("取消收藏成功", null));
    }

    @GetMapping("/{id}/comments")
    @Operation(summary = "获取物品的评论列表", description = "获取指定物品下的所有公开评论，支持嵌套（盖楼）")
    public ResponseEntity<ApiResponse<List<CommentResponse>>> getItemComments(@PathVariable Long id) {
        try {
            // 获取物品
            Item item = itemRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("物品不存在"));

            // 获取该物品的所有顶级评论（没有父评论的评论）
            List<Comment> comments = commentRepository.findByItemAndParentIsNullOrderByCreatedAtDesc(item);

            // 将评论转换为响应对象
            List<CommentResponse> commentResponses = comments.stream()
                    .map(comment -> {
                        CommentResponse response = new CommentResponse(comment);

                        // 获取评论的回复
                        List<Comment> replies = commentRepository.findByParentOrderByCreatedAtAsc(comment);
                        if (!replies.isEmpty()) {
                            List<CommentResponse> replyResponses = replies.stream()
                                    .map(CommentResponse::new)
                                    .collect(Collectors.toList());
                            response.setReplies(replyResponses);
                        }

                        return response;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取评论成功", commentResponses));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取评论失败: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/comments")
    @Operation(summary = "添加评论", description = "为指定物品添加评论")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<CommentResponse>> addComment(
            @PathVariable Long id,
            @RequestBody Map<String, Object> commentData) {
        try {
            String content = (String) commentData.get("content");
            if (content == null || content.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error("评论内容不能为空"));
            }

            // 获取父评论ID（如果有）
            Long parentId = null;
            if (commentData.get("parent_id") != null) {
                try {
                    parentId = Long.parseLong(commentData.get("parent_id").toString());
                } catch (NumberFormatException e) {
                    // 忽略无效的父评论ID
                }
            }

            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 获取物品
            Item item = itemRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("物品不存在"));

            // 创建评论对象
            Comment comment = new Comment();
            comment.setItem(item);
            comment.setUser(user);
            comment.setContent(content.trim());

            // 如果有父评论ID，设置父评论
            if (parentId != null) {
                Comment parent = commentRepository.findById(parentId)
                        .orElseThrow(() -> new RuntimeException("父评论不存在"));
                comment.setParent(parent);
            }

            // 保存评论
            comment = commentRepository.save(comment);

            // 创建响应对象
            CommentResponse response = new CommentResponse(comment);

            return ResponseEntity.ok(ApiResponse.success("评论成功", response));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("添加评论失败: " + e.getMessage()));
        }
    }

    @GetMapping("/debug")
    @Operation(summary = "调试端点 - 获取所有商品", description = "用于调试，返回数据库中的所有商品")
    public ResponseEntity<ApiResponse<List<Item>>> getAllItemsForDebug() {
        List<Item> items = itemRepository.findAll();
        return ResponseEntity.ok(ApiResponse.success("获取所有商品成功", items));
    }

    @GetMapping("/my")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "获取当前用户发布的物品", description = "获取当前登录用户发布的所有物品")
    public ResponseEntity<ApiResponse<List<ItemResponse>>> getMyItems(
            @RequestParam(value = "item_type", required = false) String itemType) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取用户发布的物品
            List<Item> items;
            if (itemType != null && !itemType.isEmpty()) {
                // 如果指定了物品类型，则根据类型筛选
                Item.ItemType type = Item.ItemType.valueOf(itemType);
                items = itemRepository.findByUserIdAndItemType(userId, type);
            } else {
                // 否则获取所有物品
                items = itemRepository.findByUserId(userId);
            }

            // 转换为响应对象
            List<ItemResponse> itemResponses = items.stream()
                    .map(item -> new ItemResponse(item))
                    .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取我的物品成功", itemResponses));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取我的物品失败: " + e.getMessage()));
        }
    }

    @GetMapping("/search/es")
    @Operation(summary = "使用Elasticsearch搜索物品", description = "通过Elasticsearch实现的智能搜索功能，支持同义词和相关性排序")
    public ResponseEntity<ApiResponse<Object>> searchWithEs(
            @RequestParam(value = "q", required = false) String keyword,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "category_id", required = false) Long categoryId,
            @RequestParam(value = "price_min", required = false) Double priceMin,
            @RequestParam(value = "price_max", required = false) Double priceMax,
            @RequestParam(value = "item_type", required = false) String itemType,
            @RequestParam(value = "condition", required = false) String condition,
            @RequestParam(value = "sort", defaultValue = "latest") String sort,
            @RequestParam(value = "direct_es", defaultValue = "true") boolean directEs) {
        try {
            // 记录请求参数
            logger.info("接收到ES搜索请求 - 参数: keyword={}, page={}, size={}, directEs={}, sort={}", 
                        keyword, page, size, directEs, sort);
            
            Object result;
            
            // 根据directEs参数决定使用哪种搜索方式
            if (directEs) {
                logger.info("使用直接调用ES API的搜索方式 (direct_es=true)");
                result = elasticsearchSyncService.directAdvancedSearch(
                        keyword, categoryId, priceMin, priceMax, itemType, condition, page, size, sort);
                logger.info("直接ES搜索完成，结果类型: {}", result != null ? result.getClass().getName() : "null");
            } else {
                logger.info("使用Spring Data Elasticsearch搜索方式 (direct_es=false)");
                result = elasticsearchSyncService.advancedSearch(
                        keyword, categoryId, priceMin, priceMax, itemType, condition, page, size, sort);
                logger.info("标准ES搜索完成，结果类型: {}", result != null ? result.getClass().getName() : "null");
            }
            
            // 检查结果是否需要回退到MySQL搜索
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                String source = (String) resultMap.getOrDefault("source", "");
                logger.info("搜索结果source标记: {}", source);
                
                // 如果结果中包含elasticsearch_direct标记，表示直接搜索成功
                if ("elasticsearch_direct".equals(source)) {
                    logger.info("直接ES搜索成功，返回结果");
                    return ResponseEntity.ok(ApiResponse.success("搜索成功", result));
                }
                
                // 如果ES搜索结果标记为需要回退，则执行MySQL搜索
                if ("elasticsearch_fallback".equals(source) || "elasticsearch_error".equals(source)) {
                    logger.info("检测到Elasticsearch回退标记，切换到MySQL搜索");
                    return searchWithMySQL(keyword, page, size, categoryId, priceMin, priceMax, itemType, condition, sort);
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success("搜索成功", result));
        } catch (Exception e) {
            logger.error("ES搜索失败，回退到MySQL搜索: {}", e.getMessage(), e);
            return searchWithMySQL(keyword, page, size, categoryId, priceMin, priceMax, itemType, condition, sort);
        }
    }

    // 提取MySQL搜索方法
    private ResponseEntity<ApiResponse<Object>> searchWithMySQL(
            String keyword, int page, int size, Long categoryId, 
            Double priceMin, Double maxPrice, String itemType, 
            String condition, String sort) {
        
        // 将页码转换为从0开始，以符合Spring Data的Page接口
        int adjustedPage = page;
        if (page > 0) adjustedPage = page - 1;
        
        // 根据sort参数确定排序字段和方向
        String sortField = "createdAt";
        Sort.Direction direction = Sort.Direction.DESC;
        
        switch (sort.toLowerCase()) {
            case "latest":
                sortField = "createdAt";
                direction = Sort.Direction.DESC;
                break;
            case "hot":
                sortField = "viewCount";
                direction = Sort.Direction.DESC;
                break;
            case "price_asc":
                sortField = "price";
                direction = Sort.Direction.ASC;
                break;
            case "price_desc":
                sortField = "price";
                direction = Sort.Direction.DESC;
                break;
        }

        Sort sortObj = Sort.by(direction, sortField);
        Pageable pageable = PageRequest.of(adjustedPage, size, sortObj);

        // 调用服务层方法，传递所有筛选条件
        Page<Item> itemPage = itemService.getFilteredItems(
                itemType, categoryId != null ? categoryId.toString() : null, 
                priceMin, maxPrice, keyword, null, pageable);

        // 使用新的批量处理方法设置收藏状态
        List<ItemResponse> itemResponses = itemService.convertItemsWithFavoriteStatus(itemPage.getContent());

        ItemPageResponse response = new ItemPageResponse(itemResponses, itemPage);
        response.setSource("mysql"); // 标记数据来源

        // 返回符合测试期望格式的响应
        return ResponseEntity.ok(ApiResponse.success("操作成功", response));
    }

    // 辅助方法：获取当前登录用户ID
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            try {
                // 首先尝试从UserDetails中获取ID
                if (authentication.getPrincipal() instanceof UserDetails) {
                    UserDetails userDetails = (UserDetails) authentication.getPrincipal();
                    try {
                        return Long.parseLong(userDetails.getUsername());
                    } catch (NumberFormatException e) {
                        // 如果用户名不是数字，则通过用户名查找用户
                        String username = userDetails.getUsername();
                        return userRepository.findByUsername(username)
                                .orElseThrow(() -> new RuntimeException("用户不存在: " + username))
                                .getId();
                    }
                } else {
                    // 如果Principal不是UserDetails，则尝试通过用户名查找用户
                    String username = authentication.getName();
                    return userRepository.findByUsername(username)
                            .orElseThrow(() -> new RuntimeException("用户不存在: " + username))
                            .getId();
                }
            } catch (Exception e) {
                throw new RuntimeException("获取用户ID失败: " + e.getMessage(), e);
            }
        }
        throw new IllegalStateException("未登录用户");
    }
}