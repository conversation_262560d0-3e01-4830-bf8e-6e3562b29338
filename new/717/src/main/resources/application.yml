server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: secondhand-platform
  datasource:
    # 使用MySQL数据库
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:secondhanddb}?useSSL=false&serverTimezone=Asia/Shanghai&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:102938Zz}
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
    defer-datasource-initialization: false
  # # Flyway数据库迁移配置
  # flyway:
  #   enabled: true
  #   baseline-on-migrate: true
  #   locations: classpath:db/migration
  #   validate-on-migrate: true
  # SQL初始化配置
  sql:
    init:
      mode: never
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 10MB
      max-request-size: 15MB
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 10000
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  # 启用缓存
  cache:
    type: ${CACHE_TYPE:redis}

  # Elasticsearch配置
  elasticsearch:
    uris: http://${ELASTICSEARCH_HOST:localhost}:${ELASTICSEARCH_PORT:9200}

jwt:
  secret: ${JWT_SECRET:SecondHandPlatformSecurityKey2024}
  expiration: 604800 # 24小时

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: ${SWAGGER_ENABLED:false}

logging:
  level:
    root: WARN
    com.sjtu.secondhand: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.hibernate: WARN
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql: WARN
  file:
    name: ./logs/secondhand-platform.log
    max-size: 10MB
    max-history: 10
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30

file:
  upload-dir: ./uploads

# 存储服务配置，值为local或oss
storage:
  type: oss

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
    access-key-id: ${OSS_ACCESS_KEY_ID:LTAI5tLKLwDpsP947BPBLsnX}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:******************************}
    bucket-name: ${OSS_BUCKET_NAME:shareu-youyu}
    bucket-domain: ${OSS_BUCKET_DOMAIN:shareu-youyu.oss-cn-shanghai.aliyuncs.com}
    allowed-origins: ${OSS_ALLOWED_ORIGINS:http://localhost:3000,http://127.0.0.1:3000,https://localhost:3000,https://127.0.0.1:3000}
