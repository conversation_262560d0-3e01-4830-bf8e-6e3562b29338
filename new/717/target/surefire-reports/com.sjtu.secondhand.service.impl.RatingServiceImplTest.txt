-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.RatingServiceImplTest
-------------------------------------------------------------------------------
Tests run: 33, Failures: 1, Errors: 5, Skipped: 0, Time elapsed: 0.104 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.RatingServiceImplTest
getPendingOrderRatings_BothBuyerAndSeller  Time elapsed: 0 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getId()" because the return value of "com.sjtu.secondhand.model.Rating.getRater()" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getPendingOrderRatings_BothBuyerAndSeller(RatingServiceImplTest.java:625)

createRating_UserNotInvolvedInOrder  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <您不能评价此订单> but was: <您不是该订单的买家或卖家>
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.createRating_UserNotInvolvedInOrder(RatingServiceImplTest.java:180)

getRatingById_Success  Time elapsed: 0.002 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getRatingById_Success(RatingServiceImplTest.java:186)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getUserRatings_Success  Time elapsed: 0 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 用户不存在
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getUserRatings_Success(RatingServiceImplTest.java:253)

getPendingOrderRatings_Success  Time elapsed: 0.001 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getId()" because the return value of "com.sjtu.secondhand.model.Rating.getRater()" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getPendingOrderRatings_Success(RatingServiceImplTest.java:307)

getRatingById_NotFound  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getRatingById_NotFound(RatingServiceImplTest.java:200)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

