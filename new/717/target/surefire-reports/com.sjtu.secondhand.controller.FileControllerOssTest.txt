-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.FileControllerOssTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.059 s <<< FAILURE! - in com.sjtu.secondhand.controller.FileControllerOssTest
uploadImage_shouldHandleOssException  Time elapsed: 0.021 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: OSS upload failed
	at com.sjtu.secondhand.controller.FileControllerOssTest.uploadImage_shouldHandleOssException(FileControllerOssTest.java:86)
Caused by: java.lang.RuntimeException: OSS upload failed
	at com.sjtu.secondhand.controller.FileControllerOssTest.uploadImage_shouldHandleOssException(FileControllerOssTest.java:86)

uploadMultipleImages_shouldHandlePartialOssFailure  Time elapsed: 0.013 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: OSS upload failed for second file
	at com.sjtu.secondhand.controller.FileControllerOssTest.uploadMultipleImages_shouldHandlePartialOssFailure(FileControllerOssTest.java:103)
Caused by: java.lang.RuntimeException: OSS upload failed for second file
	at com.sjtu.secondhand.controller.FileControllerOssTest.uploadMultipleImages_shouldHandlePartialOssFailure(FileControllerOssTest.java:103)

