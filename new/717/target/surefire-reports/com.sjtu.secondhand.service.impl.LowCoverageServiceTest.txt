-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.LowCoverageServiceTest
-------------------------------------------------------------------------------
Tests run: 22, Failures: 0, Errors: 3, Skipped: 0, Time elapsed: 0.509 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.LowCoverageServiceTest
testCreateRating_SellerRateBuyer  Time elapsed: 0.004 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 用户不存在
	at com.sjtu.secondhand.service.impl.LowCoverageServiceTest.testCreateRating_SellerRateBuyer(LowCoverageServiceTest.java:366)

testCreateRating_BuyerRateSeller  Time elapsed: 0.008 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 用户不存在
	at com.sjtu.secondhand.service.impl.LowCoverageServiceTest.testCreateRating_BuyerRateSeller(LowCoverageServiceTest.java:338)

testGetItemCFRecommendations_WithFavorites  Time elapsed: 0.012 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'convertItemsWithFavoriteStatus' method:
    itemService.convertItemsWithFavoriteStatus(
    []
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:108)
 - has following stubbing(s) with different arguments:
    1. itemService.convertItemsWithFavoriteStatus(
    [com.sjtu.secondhand.model.Item@5418a10]
);
      -> at com.sjtu.secondhand.service.impl.LowCoverageServiceTest.testGetItemCFRecommendations_WithFavorites(LowCoverageServiceTest.java:527)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.LowCoverageServiceTest.testGetItemCFRecommendations_WithFavorites(LowCoverageServiceTest.java:530)

