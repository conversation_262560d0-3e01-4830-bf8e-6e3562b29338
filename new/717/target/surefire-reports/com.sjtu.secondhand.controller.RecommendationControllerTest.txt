-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.RecommendationControllerTest
-------------------------------------------------------------------------------
Tests run: 26, Failures: 0, Errors: 8, Skipped: 0, Time elapsed: 4.793 s <<< FAILURE! - in com.sjtu.secondhand.controller.RecommendationControllerTest
getForYouRecommendations_WithCustomLimit  Time elapsed: 0.073 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_WithCustomLimit(RecommendationControllerTest.java:309)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_WithCustomLimit(RecommendationControllerTest.java:309)

getForYouRecommendations_WithAuthenticatedUser  Time elapsed: 0.016 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_WithAuthenticatedUser(RecommendationControllerTest.java:291)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_WithAuthenticatedUser(RecommendationControllerTest.java:291)

getForYouRecommendations_EmptyResults  Time elapsed: 0.022 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_EmptyResults(RecommendationControllerTest.java:324)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_EmptyResults(RecommendationControllerTest.java:324)

getForYouRecommendations_ZeroLimit  Time elapsed: 0.021 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_ZeroLimit(RecommendationControllerTest.java:411)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_ZeroLimit(RecommendationControllerTest.java:411)

triggerSimilarityCalculation_ServiceThrowsException  Time elapsed: 0.015 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Calculation error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.triggerSimilarityCalculation_ServiceThrowsException(RecommendationControllerTest.java:362)
Caused by: java.lang.RuntimeException: Calculation error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.triggerSimilarityCalculation_ServiceThrowsException(RecommendationControllerTest.java:362)

getSimilarItems_ServiceThrowsException  Time elapsed: 0.018 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getSimilarItems_ServiceThrowsException(RecommendationControllerTest.java:351)
Caused by: java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getSimilarItems_ServiceThrowsException(RecommendationControllerTest.java:351)

getHotRecommendations_ServiceThrowsException  Time elapsed: 0.014 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getHotRecommendations_ServiceThrowsException(RecommendationControllerTest.java:339)
Caused by: java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getHotRecommendations_ServiceThrowsException(RecommendationControllerTest.java:339)

getForYouRecommendations_NegativeLimit  Time elapsed: 0.018 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_NegativeLimit(RecommendationControllerTest.java:424)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getUsername()" because "this.user" is null
	at com.sjtu.secondhand.controller.RecommendationControllerTest.getForYouRecommendations_NegativeLimit(RecommendationControllerTest.java:424)

