-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.ElasticsearchSyncServiceImplTest
-------------------------------------------------------------------------------
Tests run: 29, Failures: 1, Errors: 1, Skipped: 0, Time elapsed: 0.19 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.ElasticsearchSyncServiceImplTest
testAdvancedSearch_Exception  Time elapsed: 0.004 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <elasticsearch_error> but was: <elasticsearch_fallback>
	at com.sjtu.secondhand.service.impl.ElasticsearchSyncServiceImplTest.testAdvancedSearch_Exception(ElasticsearchSyncServiceImplTest.java:670)

testSyncAllItemsToElasticsearch_ItemConversionException  Time elapsed: 0.009 s  <<< ERROR!
java.lang.RuntimeException: 同步到Elasticsearch失败: 获取ID失败
	at com.sjtu.secondhand.service.impl.ElasticsearchSyncServiceImplTest.testSyncAllItemsToElasticsearch_ItemConversionException(ElasticsearchSyncServiceImplTest.java:553)
Caused by: java.lang.RuntimeException: 获取ID失败
	at com.sjtu.secondhand.service.impl.ElasticsearchSyncServiceImplTest.testSyncAllItemsToElasticsearch_ItemConversionException(ElasticsearchSyncServiceImplTest.java:553)

