-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.UserControllerTest
-------------------------------------------------------------------------------
Tests run: 16, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.581 s <<< FAILURE! - in com.sjtu.secondhand.controller.UserControllerTest
getUserById_UserNotFound  Time elapsed: 0.04 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.UserControllerTest.getUserById_UserNotFound(UserControllerTest.java:106)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.UserControllerTest.getUserById_UserNotFound(UserControllerTest.java:106)

getCurrentUser_ServiceException  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.UserControllerTest.getCurrentUser_ServiceException(UserControllerTest.java:75)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.UserControllerTest.getCurrentUser_ServiceException(UserControllerTest.java:75)

