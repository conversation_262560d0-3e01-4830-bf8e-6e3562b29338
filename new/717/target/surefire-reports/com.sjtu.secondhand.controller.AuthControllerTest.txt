-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.AuthControllerTest
-------------------------------------------------------------------------------
Tests run: 11, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.493 s <<< FAILURE! - in com.sjtu.secondhand.controller.AuthControllerTest
testRegister_ServiceThrowsException  Time elapsed: 0.023 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Username already exists
	at com.sjtu.secondhand.controller.AuthControllerTest.testRegister_ServiceThrowsException(AuthControllerTest.java:156)
Caused by: java.lang.RuntimeException: Username already exists
	at com.sjtu.secondhand.controller.AuthControllerTest.testRegister_ServiceThrowsException(AuthControllerTest.java:156)

testLogin_ServiceThrowsException  Time elapsed: 0.025 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Invalid credentials
	at com.sjtu.secondhand.controller.AuthControllerTest.testLogin_ServiceThrowsException(AuthControllerTest.java:99)
Caused by: java.lang.RuntimeException: Invalid credentials
	at com.sjtu.secondhand.controller.AuthControllerTest.testLogin_ServiceThrowsException(AuthControllerTest.java:99)

