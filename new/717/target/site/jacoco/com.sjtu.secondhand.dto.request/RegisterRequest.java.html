<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RegisterRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">RegisterRequest.java</span></div><h1>RegisterRequest.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

public class RegisterRequest {

    @NotBlank(message = &quot;用户名不能为空&quot;)
    @Size(min = 3, max = 20, message = &quot;用户名长度必须在3-20个字符之间&quot;)
    private String username;

    @NotBlank(message = &quot;密码不能为空&quot;)
    @Size(min = 6, max = 20, message = &quot;密码长度必须在6-20个字符之间&quot;)
    private String password;

    @NotBlank(message = &quot;邮箱不能为空&quot;)
    @Email(message = &quot;邮箱格式不正确&quot;)
    private String email;

<span class="fc" id="L21">    public RegisterRequest() {</span>
<span class="fc" id="L22">    }</span>

<span class="fc" id="L24">    public RegisterRequest(String username, String password, String email) {</span>
<span class="fc" id="L25">        this.username = username;</span>
<span class="fc" id="L26">        this.password = password;</span>
<span class="fc" id="L27">        this.email = email;</span>
<span class="fc" id="L28">    }</span>

    public String getUsername() {
<span class="fc" id="L31">        return username;</span>
    }

    public void setUsername(String username) {
<span class="fc" id="L35">        this.username = username;</span>
<span class="fc" id="L36">    }</span>

    public String getPassword() {
<span class="fc" id="L39">        return password;</span>
    }

    public void setPassword(String password) {
<span class="fc" id="L43">        this.password = password;</span>
<span class="fc" id="L44">    }</span>

    public String getEmail() {
<span class="fc" id="L47">        return email;</span>
    }

    public void setEmail(String email) {
<span class="fc" id="L51">        this.email = email;</span>
<span class="fc" id="L52">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>