<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">OrderRequest.java</span></div><h1>OrderRequest.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotNull;

public class OrderRequest {

    @NotNull(message = &quot;物品ID不能为空&quot;)
    private Integer item_id;

<span class="fc" id="L10">    public OrderRequest() {</span>
<span class="fc" id="L11">    }</span>

    public Integer getItem_id() {
<span class="fc" id="L14">        return item_id;</span>
    }

    public void setItem_id(Integer item_id) {
<span class="fc" id="L18">        this.item_id = item_id;</span>
<span class="fc" id="L19">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>