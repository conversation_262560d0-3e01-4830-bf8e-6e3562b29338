<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">ItemRequest.java</span></div><h1>ItemRequest.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

<span class="fc" id="L10">public class ItemRequest {</span>

    @NotNull(message = &quot;物品类型不能为空&quot;)
    private String item_type; // IDLE-卖闲置, WANTED-求好物

    @NotBlank(message = &quot;名称不能为空&quot;)
    @Size(max = 100, message = &quot;名称长度不能超过100个字符&quot;)
    private String name;

    @NotBlank(message = &quot;描述不能为空&quot;)
    private String description;

    @NotNull(message = &quot;分类ID不能为空&quot;)
    private Integer category_id;

    // 当 item_type 为 'IDLE' 时必填
    private BigDecimal price;

    // 当 item_type 为 'WANTED' 时可选，心理价位下限
    private BigDecimal price_min;

    // 当 item_type 为 'WANTED' 时可选，心理价位上限
    private BigDecimal price_max;

    @NotNull(message = &quot;物品状态不能为空&quot;)
    private String condition; // BRAND_NEW-全新, LIKE_NEW-几乎全新, FINE-轻微使用, CLEARLY_USED-明显使用

    // 图片URL列表，由上传接口返回
<span class="fc" id="L38">    private List&lt;String&gt; image_urls = new ArrayList&lt;&gt;();</span>

    // Getters and Setters
    public String getItem_type() {
<span class="fc" id="L42">        return item_type;</span>
    }

    public void setItem_type(String item_type) {
<span class="fc" id="L46">        this.item_type = item_type;</span>
<span class="fc" id="L47">    }</span>

    public String getName() {
<span class="fc" id="L50">        return name;</span>
    }

    public void setName(String name) {
<span class="fc" id="L54">        this.name = name;</span>
<span class="fc" id="L55">    }</span>

    public String getDescription() {
<span class="fc" id="L58">        return description;</span>
    }

    public void setDescription(String description) {
<span class="fc" id="L62">        this.description = description;</span>
<span class="fc" id="L63">    }</span>

    public Integer getCategory_id() {
<span class="fc" id="L66">        return category_id;</span>
    }

    public void setCategory_id(Integer category_id) {
<span class="fc" id="L70">        this.category_id = category_id;</span>
<span class="fc" id="L71">    }</span>

    public BigDecimal getPrice() {
<span class="fc" id="L74">        return price;</span>
    }

    public void setPrice(BigDecimal price) {
<span class="fc" id="L78">        this.price = price;</span>
<span class="fc" id="L79">    }</span>

    public BigDecimal getPrice_min() {
<span class="fc" id="L82">        return price_min;</span>
    }

    public void setPrice_min(BigDecimal price_min) {
<span class="fc" id="L86">        this.price_min = price_min;</span>
<span class="fc" id="L87">    }</span>

    public BigDecimal getPrice_max() {
<span class="fc" id="L90">        return price_max;</span>
    }

    public void setPrice_max(BigDecimal price_max) {
<span class="fc" id="L94">        this.price_max = price_max;</span>
<span class="fc" id="L95">    }</span>

    public String getCondition() {
<span class="fc" id="L98">        return condition;</span>
    }

    public void setCondition(String condition) {
<span class="fc" id="L102">        this.condition = condition;</span>
<span class="fc" id="L103">    }</span>

    public List&lt;String&gt; getImage_urls() {
<span class="fc" id="L106">        return image_urls;</span>
    }

    public void setImage_urls(List&lt;String&gt; image_urls) {
<span class="fc" id="L110">        this.image_urls = image_urls;</span>
<span class="fc" id="L111">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>