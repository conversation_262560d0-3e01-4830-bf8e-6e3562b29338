<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LoginRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">LoginRequest.java</span></div><h1>LoginRequest.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;

public class LoginRequest {
    @NotBlank(message = &quot;用户名不能为空&quot;)
    private String username;

    @NotBlank(message = &quot;密码不能为空&quot;)
    private String password;

<span class="fc" id="L12">    public LoginRequest() {</span>
<span class="fc" id="L13">    }</span>

<span class="fc" id="L15">    public LoginRequest(String username, String password) {</span>
<span class="fc" id="L16">        this.username = username;</span>
<span class="fc" id="L17">        this.password = password;</span>
<span class="fc" id="L18">    }</span>

    public String getUsername() {
<span class="fc" id="L21">        return username;</span>
    }

    public void setUsername(String username) {
<span class="fc" id="L25">        this.username = username;</span>
<span class="fc" id="L26">    }</span>

    public String getPassword() {
<span class="fc" id="L29">        return password;</span>
    }

    public void setPassword(String password) {
<span class="fc" id="L33">        this.password = password;</span>
<span class="fc" id="L34">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>