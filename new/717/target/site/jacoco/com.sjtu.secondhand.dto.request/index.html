<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.sjtu.secondhand.dto.request</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <span class="el_package">com.sjtu.secondhand.dto.request</span></div><h1>com.sjtu.secondhand.dto.request</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 244</td><td class="ctr2">100%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">0</td><td class="ctr2">64</td><td class="ctr1">0</td><td class="ctr2">108</td><td class="ctr1">0</td><td class="ctr2">64</td><td class="ctr1">0</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a1"><a href="ItemRequest.html" class="el_class">ItemRequest</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="71" alt="71"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">19</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a6"><a href="RegisterRequest.html" class="el_class">RegisterRequest</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="36" alt="36"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="LoginRequest.html" class="el_class">LoginRequest</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="26" alt="26"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k3">6</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a8"><a href="UserUpdateRequest.html" class="el_class">UserUpdateRequest</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k4">6</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="RatingRequest.html" class="el_class">RatingRequest</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="24" alt="24"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k2">7</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a0"><a href="CommentCreate.html" class="el_class">CommentCreate</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="17" alt="17"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">5</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="OfferRequest.html" class="el_class">OfferRequest</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="17" alt="17"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">5</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="TransactionCompleteRequest.html" class="el_class">TransactionCompleteRequest</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="17" alt="17"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">5</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a4"><a href="OrderRequest.html" class="el_class">OrderRequest</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="10" alt="10"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">3</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>