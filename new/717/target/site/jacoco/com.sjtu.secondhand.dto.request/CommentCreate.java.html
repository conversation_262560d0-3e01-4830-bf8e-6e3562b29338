<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CommentCreate.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">CommentCreate.java</span></div><h1>CommentCreate.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.NotBlank;

public class CommentCreate {

    @NotBlank(message = &quot;评论内容不能为空&quot;)
    private String content;
    
    private Integer parent_id;

<span class="fc" id="L12">    public CommentCreate() {</span>
<span class="fc" id="L13">    }</span>

    public String getContent() {
<span class="fc" id="L16">        return content;</span>
    }

    public void setContent(String content) {
<span class="fc" id="L20">        this.content = content;</span>
<span class="fc" id="L21">    }</span>

    public Integer getParent_id() {
<span class="fc" id="L24">        return parent_id;</span>
    }

    public void setParent_id(Integer parent_id) {
<span class="fc" id="L28">        this.parent_id = parent_id;</span>
<span class="fc" id="L29">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>