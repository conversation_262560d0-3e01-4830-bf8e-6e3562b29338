<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RatingRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.request</a> &gt; <span class="el_source">RatingRequest.java</span></div><h1>RatingRequest.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.request;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class RatingRequest {

    @NotNull(message = &quot;交易类型不能为空&quot;)
    private String transaction_type; // IDLE 或 WANTED

    @NotNull(message = &quot;交易ID不能为空&quot;)
    private Integer related_transaction_id;

    @NotNull(message = &quot;评分不能为空&quot;)
    @Min(value = 1, message = &quot;评分最低为1分&quot;)
    @Max(value = 5, message = &quot;评分最高为5分&quot;)
    private Integer score;

<span class="fc" id="L20">    public RatingRequest() {</span>
<span class="fc" id="L21">    }</span>

    public String getTransaction_type() {
<span class="fc" id="L24">        return transaction_type;</span>
    }

    public void setTransaction_type(String transaction_type) {
<span class="fc" id="L28">        this.transaction_type = transaction_type;</span>
<span class="fc" id="L29">    }</span>

    public Integer getRelated_transaction_id() {
<span class="fc" id="L32">        return related_transaction_id;</span>
    }

    public void setRelated_transaction_id(Integer related_transaction_id) {
<span class="fc" id="L36">        this.related_transaction_id = related_transaction_id;</span>
<span class="fc" id="L37">    }</span>

    public Integer getScore() {
<span class="fc" id="L40">        return score;</span>
    }

    public void setScore(Integer score) {
<span class="fc" id="L44">        this.score = score;</span>
<span class="fc" id="L45">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>