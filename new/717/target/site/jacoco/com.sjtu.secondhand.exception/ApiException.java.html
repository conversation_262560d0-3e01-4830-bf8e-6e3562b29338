<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ApiException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.exception</a> &gt; <span class="el_source">ApiException.java</span></div><h1>ApiException.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.exception;

import org.springframework.http.HttpStatus;

public class ApiException extends RuntimeException {
    private final HttpStatus status;
    private final String message;

<span class="fc" id="L9">    public ApiException(HttpStatus status, String message) {</span>
<span class="fc" id="L10">        this.status = status;</span>
<span class="fc" id="L11">        this.message = message;</span>
<span class="fc" id="L12">    }</span>

    public ApiException(String superMessage, HttpStatus status, String message) {
<span class="fc" id="L15">        super(superMessage);</span>
<span class="fc" id="L16">        this.status = status;</span>
<span class="fc" id="L17">        this.message = message;</span>
<span class="fc" id="L18">    }</span>

    public HttpStatus getStatus() {
<span class="fc" id="L21">        return status;</span>
    }

    @Override
    public String getMessage() {
<span class="fc" id="L26">        return message;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>