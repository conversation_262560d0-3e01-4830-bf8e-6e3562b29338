<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GlobalExceptionHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.exception</a> &gt; <span class="el_source">GlobalExceptionHandler.java</span></div><h1>GlobalExceptionHandler.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.exception;

import com.sjtu.secondhand.dto.response.ApiResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
<span class="fc" id="L19">public class GlobalExceptionHandler {</span>

    @ExceptionHandler(ApiException.class)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleApiException(ApiException exception) {
<span class="fc" id="L23">        ApiResponse&lt;Object&gt; response = ApiResponse.error(exception.getStatus().toString(), exception.getMessage());</span>
<span class="fc" id="L24">        return new ResponseEntity&lt;&gt;(response, exception.getStatus());</span>
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleBadCredentialsException() {
<span class="fc" id="L29">        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;AUTH_ERROR&quot;, &quot;用户名或密码错误&quot;);</span>
<span class="fc" id="L30">        return new ResponseEntity&lt;&gt;(response, HttpStatus.UNAUTHORIZED);</span>
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleAccessDeniedException() {
<span class="fc" id="L35">        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;ACCESS_DENIED&quot;, &quot;没有访问权限&quot;);</span>
<span class="fc" id="L36">        return new ResponseEntity&lt;&gt;(response, HttpStatus.FORBIDDEN);</span>
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity&lt;ApiResponse&lt;Map&lt;String, String&gt;&gt;&gt; handleValidationErrors(MethodArgumentNotValidException ex) {
<span class="fc" id="L41">        Map&lt;String, String&gt; errors = new HashMap&lt;&gt;();</span>
<span class="fc" id="L42">        ex.getBindingResult().getAllErrors().forEach(error -&gt; {</span>
<span class="fc" id="L43">            String fieldName = ((FieldError) error).getField();</span>
<span class="fc" id="L44">            String errorMessage = error.getDefaultMessage();</span>
<span class="fc" id="L45">            errors.put(fieldName, errorMessage);</span>
<span class="fc" id="L46">        });</span>
<span class="fc" id="L47">        ApiResponse&lt;Map&lt;String, String&gt;&gt; response = ApiResponse.error(&quot;INVALID_PARAMS&quot;, &quot;参数验证失败&quot;, errors);</span>
<span class="fc" id="L48">        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);</span>
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleConstraintViolation(ConstraintViolationException ex) {
<span class="fc" id="L53">        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;INVALID_PARAMS&quot;, ex.getMessage());</span>
<span class="fc" id="L54">        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);</span>
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleGlobalException(Exception exception, WebRequest request) {
<span class="fc" id="L59">        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;SERVER_ERROR&quot;, &quot;发生了一个未处理的异常: &quot; + exception.getMessage());</span>
<span class="fc" id="L60">        return new ResponseEntity&lt;&gt;(response, HttpStatus.INTERNAL_SERVER_ERROR);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>