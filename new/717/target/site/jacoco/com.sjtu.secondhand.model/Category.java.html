<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Category.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Category.java</span></div><h1>Category.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = &quot;categories&quot;)
public class Category {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotBlank
    @Size(max = 50)
    @Column(unique = true)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;parent_id&quot;)
    private Category parent;

<span class="pc" id="L26">    @OneToMany(mappedBy = &quot;parent&quot;)</span>
    private List&lt;Category&gt; children = new ArrayList&lt;&gt;();

<span class="pc" id="L29">    @OneToMany(mappedBy = &quot;category&quot;)</span>
    private List&lt;Item&gt; items = new ArrayList&lt;&gt;();

    // Constructors
<span class="fc" id="L33">    public Category() {</span>
<span class="fc" id="L34">    }</span>

<span class="fc" id="L36">    public Category(String name) {</span>
<span class="fc" id="L37">        this.name = name;</span>
<span class="fc" id="L38">    }</span>

<span class="nc" id="L40">    public Category(String name, Category parent) {</span>
<span class="nc" id="L41">        this.name = name;</span>
<span class="nc" id="L42">        this.parent = parent;</span>
<span class="nc" id="L43">    }</span>

    // Getters and setters
    public Integer getId() {
<span class="fc" id="L47">        return id;</span>
    }

    public void setId(Integer id) {
<span class="fc" id="L51">        this.id = id;</span>
<span class="fc" id="L52">    }</span>

    public String getName() {
<span class="fc" id="L55">        return name;</span>
    }

    public void setName(String name) {
<span class="fc" id="L59">        this.name = name;</span>
<span class="fc" id="L60">    }</span>

    public Category getParent() {
<span class="fc" id="L63">        return parent;</span>
    }

    public void setParent(Category parent) {
<span class="fc" id="L67">        this.parent = parent;</span>
<span class="fc" id="L68">    }</span>

    public List&lt;Category&gt; getChildren() {
<span class="fc" id="L71">        return children;</span>
    }

    public void setChildren(List&lt;Category&gt; children) {
<span class="nc" id="L75">        this.children = children;</span>
<span class="nc" id="L76">    }</span>

    public void addChild(Category child) {
<span class="fc" id="L79">        this.children.add(child);</span>
<span class="fc" id="L80">        child.setParent(this);</span>
<span class="fc" id="L81">    }</span>

    public List&lt;Item&gt; getItems() {
<span class="fc" id="L84">        return items;</span>
    }

    public void setItems(List&lt;Item&gt; items) {
<span class="nc" id="L88">        this.items = items;</span>
<span class="nc" id="L89">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>