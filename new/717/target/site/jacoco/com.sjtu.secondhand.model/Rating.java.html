<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Rating.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Rating.java</span></div><h1>Rating.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = &quot;ratings&quot;)
public class Rating {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = &quot;related_transaction_id&quot;, nullable = false)
    private Long relatedTransactionId;

    @Column(name = &quot;transaction_type&quot;, nullable = false)
    @Enumerated(EnumType.STRING)
    private TransactionType transactionType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;rater_id&quot;, nullable = false)
    private User rater;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;ratee_id&quot;, nullable = false)
    private User ratee;

    @NotNull
    @Min(1)
    @Max(5)
    private Byte score;

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
<span class="nc" id="L42">        createdAt = LocalDateTime.now();</span>
<span class="nc" id="L43">    }</span>

    // 交易类型枚举
<span class="fc" id="L46">    public enum TransactionType {</span>
<span class="fc" id="L47">        IDLE, WANTED</span>
    }

    // Constructors
<span class="fc" id="L51">    public Rating() {</span>
<span class="fc" id="L52">    }</span>

<span class="nc" id="L54">    public Rating(User rater, User ratee, Byte score, TransactionType transactionType, Long relatedTransactionId) {</span>
<span class="nc" id="L55">        this.rater = rater;</span>
<span class="nc" id="L56">        this.ratee = ratee;</span>
<span class="nc" id="L57">        this.score = score;</span>
<span class="nc" id="L58">        this.transactionType = transactionType;</span>
<span class="nc" id="L59">        this.relatedTransactionId = relatedTransactionId;</span>
<span class="nc" id="L60">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L64">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L68">        this.id = id;</span>
<span class="fc" id="L69">    }</span>

    public Long getRelatedTransactionId() {
<span class="fc" id="L72">        return relatedTransactionId;</span>
    }

    public void setRelatedTransactionId(Long relatedTransactionId) {
<span class="fc" id="L76">        this.relatedTransactionId = relatedTransactionId;</span>
<span class="fc" id="L77">    }</span>

    public TransactionType getTransactionType() {
<span class="fc" id="L80">        return transactionType;</span>
    }

    public void setTransactionType(TransactionType transactionType) {
<span class="fc" id="L84">        this.transactionType = transactionType;</span>
<span class="fc" id="L85">    }</span>

    public User getRater() {
<span class="fc" id="L88">        return rater;</span>
    }

    public void setRater(User rater) {
<span class="fc" id="L92">        this.rater = rater;</span>
<span class="fc" id="L93">    }</span>

    public User getRatee() {
<span class="fc" id="L96">        return ratee;</span>
    }

    public void setRatee(User ratee) {
<span class="fc" id="L100">        this.ratee = ratee;</span>
<span class="fc" id="L101">    }</span>

    public Byte getScore() {
<span class="fc" id="L104">        return score;</span>
    }

    public void setScore(Byte score) {
<span class="fc" id="L108">        this.score = score;</span>
<span class="fc" id="L109">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L112">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L116">        this.createdAt = createdAt;</span>
<span class="fc" id="L117">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>