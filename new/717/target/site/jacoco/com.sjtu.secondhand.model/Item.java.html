<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Item.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Item.java</span></div><h1>Item.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import com.sjtu.secondhand.config.ItemEntityListener;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = &quot;items&quot;)
@EntityListeners(ItemEntityListener.class)
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;user_id&quot;, nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;category_id&quot;, nullable = false)
    private Category category;

<span class="fc" id="L30">    @Column(name = &quot;item_type&quot;)</span>
    @Enumerated(EnumType.STRING)
    private ItemType itemType = ItemType.IDLE;

    @NotBlank
    @Size(max = 255)
    private String name;

    @NotBlank
    @Column(columnDefinition = &quot;TEXT&quot;)
    private String description;

    @Column(name = &quot;price&quot;)
    private BigDecimal price;

    @Column(name = &quot;price_min&quot;)
    private BigDecimal priceMin;

    @Column(name = &quot;price_max&quot;)
    private BigDecimal priceMax;

    @Column(name = &quot;`condition`&quot;)
    @Enumerated(EnumType.STRING)
    private ItemCondition condition;

<span class="fc" id="L55">    @Enumerated(EnumType.STRING)</span>
    @Column(name = &quot;status&quot;)
    private ItemStatus status = ItemStatus.FOR_SALE;

<span class="fc" id="L59">    @Column(name = &quot;is_visible&quot;)</span>
<span class="fc" id="L60">    private Boolean isVisible = true;</span>

<span class="fc" id="L62">    @Column(name = &quot;view_count&quot;)</span>
<span class="fc" id="L63">    private Integer viewCount = 0;</span>

<span class="fc" id="L65">    @Column(name = &quot;favorite_count&quot;)</span>
<span class="fc" id="L66">    private Integer favoriteCount = 0;</span>

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @Column(name = &quot;updated_at&quot;)
    private LocalDateTime updatedAt;

<span class="fc" id="L74">    @OneToMany(mappedBy = &quot;item&quot;, cascade = CascadeType.ALL, orphanRemoval = true)</span>
    private List&lt;ItemImage&gt; images = new ArrayList&lt;&gt;();

    @PrePersist
    protected void onCreate() {
<span class="nc" id="L79">        createdAt = LocalDateTime.now();</span>
<span class="nc" id="L80">        updatedAt = LocalDateTime.now();</span>
<span class="nc" id="L81">    }</span>

    @PreUpdate
    protected void onUpdate() {
<span class="nc" id="L85">        updatedAt = LocalDateTime.now();</span>
<span class="nc" id="L86">    }</span>

    // Enums
<span class="fc" id="L89">    public enum ItemType {</span>
<span class="fc" id="L90">        IDLE, WANTED</span>
    }

<span class="fc" id="L93">    public enum ItemCondition {</span>
<span class="fc" id="L94">        BRAND_NEW, LIKE_NEW, FINE, CLEARLY_USED</span>
    }

<span class="fc" id="L97">    public enum ItemStatus {</span>
<span class="fc" id="L98">        FOR_SALE, RESERVED, SOLD</span>
    }

    // Constructors
<span class="fc" id="L102">    public Item() {</span>
<span class="fc" id="L103">    }</span>

    public Item(User user, Category category, String name, String description, BigDecimal price,
<span class="fc" id="L106">            ItemCondition condition) {</span>
<span class="fc" id="L107">        this.user = user;</span>
<span class="fc" id="L108">        this.category = category;</span>
<span class="fc" id="L109">        this.name = name;</span>
<span class="fc" id="L110">        this.description = description;</span>
<span class="fc" id="L111">        this.price = price;</span>
<span class="fc" id="L112">        this.condition = condition;</span>
<span class="fc" id="L113">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L117">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L121">        this.id = id;</span>
<span class="fc" id="L122">    }</span>

    public User getUser() {
<span class="fc" id="L125">        return user;</span>
    }

    public void setUser(User user) {
<span class="fc" id="L129">        this.user = user;</span>
<span class="fc" id="L130">    }</span>

    public Category getCategory() {
<span class="fc" id="L133">        return category;</span>
    }

    public void setCategory(Category category) {
<span class="fc" id="L137">        this.category = category;</span>
<span class="fc" id="L138">    }</span>

    public String getName() {
<span class="fc" id="L141">        return name;</span>
    }

    public void setName(String name) {
<span class="fc" id="L145">        this.name = name;</span>
<span class="fc" id="L146">    }</span>

    public String getDescription() {
<span class="fc" id="L149">        return description;</span>
    }

    public void setDescription(String description) {
<span class="fc" id="L153">        this.description = description;</span>
<span class="fc" id="L154">    }</span>

    public BigDecimal getPrice() {
<span class="fc" id="L157">        return price;</span>
    }

    public void setPrice(BigDecimal price) {
<span class="fc" id="L161">        this.price = price;</span>
<span class="fc" id="L162">    }</span>

    public BigDecimal getPriceMin() {
<span class="fc" id="L165">        return priceMin;</span>
    }

    public void setPriceMin(BigDecimal priceMin) {
<span class="fc" id="L169">        this.priceMin = priceMin;</span>
<span class="fc" id="L170">    }</span>

    public BigDecimal getPriceMax() {
<span class="fc" id="L173">        return priceMax;</span>
    }

    public void setPriceMax(BigDecimal priceMax) {
<span class="fc" id="L177">        this.priceMax = priceMax;</span>
<span class="fc" id="L178">    }</span>

    public ItemCondition getCondition() {
<span class="fc" id="L181">        return condition;</span>
    }

    public void setCondition(ItemCondition condition) {
<span class="fc" id="L185">        this.condition = condition;</span>
<span class="fc" id="L186">    }</span>

    public ItemStatus getStatus() {
<span class="fc" id="L189">        return status;</span>
    }

    public void setStatus(ItemStatus status) {
<span class="fc" id="L193">        this.status = status;</span>
<span class="fc" id="L194">    }</span>

    public Boolean getIsVisible() {
<span class="fc" id="L197">        return isVisible;</span>
    }

    public void setIsVisible(Boolean visible) {
<span class="fc" id="L201">        isVisible = visible;</span>
<span class="fc" id="L202">    }</span>

    public Integer getViewCount() {
<span class="fc" id="L205">        return viewCount;</span>
    }

    public void setViewCount(Integer viewCount) {
<span class="fc" id="L209">        this.viewCount = viewCount;</span>
<span class="fc" id="L210">    }</span>

    public Integer getFavoriteCount() {
<span class="fc" id="L213">        return favoriteCount;</span>
    }

    public void setFavoriteCount(Integer favoriteCount) {
<span class="fc" id="L217">        this.favoriteCount = favoriteCount;</span>
<span class="fc" id="L218">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L221">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L225">        this.createdAt = createdAt;</span>
<span class="fc" id="L226">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L229">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L233">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L234">    }</span>

    public List&lt;ItemImage&gt; getImages() {
<span class="fc" id="L237">        return images;</span>
    }

    public void setImages(List&lt;ItemImage&gt; images) {
<span class="fc" id="L241">        this.images = images;</span>
<span class="fc" id="L242">    }</span>

    public ItemType getItemType() {
<span class="fc" id="L245">        return itemType;</span>
    }

    public void setItemType(ItemType itemType) {
<span class="fc" id="L249">        this.itemType = itemType;</span>
<span class="fc" id="L250">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>