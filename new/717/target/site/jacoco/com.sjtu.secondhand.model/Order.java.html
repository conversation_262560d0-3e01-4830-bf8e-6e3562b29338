<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Order.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Order.java</span></div><h1>Order.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = &quot;orders&quot;)
public class Order {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;item_id&quot;, nullable = false, unique = true)
    private Item item;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;buyer_id&quot;, nullable = false)
    private User buyer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;seller_id&quot;, nullable = false)
    private User seller;

    @Enumerated(EnumType.STRING)
    @Column(name = &quot;status&quot;)
    private OrderStatus status;

<span class="fc" id="L30">    @Column(name = &quot;is_buyer_rated&quot;)</span>
<span class="fc" id="L31">    private Boolean isBuyerRated = false;</span>

<span class="fc" id="L33">    @Column(name = &quot;is_seller_rated&quot;)</span>
<span class="fc" id="L34">    private Boolean isSellerRated = false;</span>

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @Column(name = &quot;updated_at&quot;)
    private LocalDateTime updatedAt;

<span class="fc" id="L42">    public enum OrderStatus {</span>
<span class="fc" id="L43">        PENDING_CONFIRMATION, // 待卖家确认</span>
<span class="fc" id="L44">        REJECTED, // 卖家已拒绝</span>
<span class="fc" id="L45">        AWAITING_ACKNOWLEDGEMENT, // 等待买家确认</span>
<span class="fc" id="L46">        CONFIRMED, // 卖家已确认</span>
<span class="fc" id="L47">        COMPLETED, // 交易完成</span>
<span class="fc" id="L48">        CANCELLED // 交易取消</span>
    }

    @PrePersist
    protected void onCreate() {
<span class="fc" id="L53">        createdAt = LocalDateTime.now();</span>
<span class="fc" id="L54">        updatedAt = LocalDateTime.now();</span>
<span class="fc bfc" id="L55" title="All 2 branches covered.">        if (status == null) {</span>
<span class="fc" id="L56">            status = OrderStatus.PENDING_CONFIRMATION;</span>
        }
<span class="fc" id="L58">    }</span>

    @PreUpdate
    protected void onUpdate() {
<span class="fc" id="L62">        updatedAt = LocalDateTime.now();</span>
<span class="fc" id="L63">    }</span>

    // Constructors
<span class="fc" id="L66">    public Order() {</span>
<span class="fc" id="L67">    }</span>

<span class="fc" id="L69">    public Order(Item item, User buyer, User seller) {</span>
<span class="fc" id="L70">        this.item = item;</span>
<span class="fc" id="L71">        this.buyer = buyer;</span>
<span class="fc" id="L72">        this.seller = seller;</span>
<span class="fc" id="L73">        this.status = OrderStatus.PENDING_CONFIRMATION;</span>
<span class="fc" id="L74">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L78">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L82">        this.id = id;</span>
<span class="fc" id="L83">    }</span>

    public Item getItem() {
<span class="fc" id="L86">        return item;</span>
    }

    public void setItem(Item item) {
<span class="fc" id="L90">        this.item = item;</span>
<span class="fc" id="L91">    }</span>

    public User getBuyer() {
<span class="fc" id="L94">        return buyer;</span>
    }

    public void setBuyer(User buyer) {
<span class="fc" id="L98">        this.buyer = buyer;</span>
<span class="fc" id="L99">    }</span>

    public User getSeller() {
<span class="fc" id="L102">        return seller;</span>
    }

    public void setSeller(User seller) {
<span class="fc" id="L106">        this.seller = seller;</span>
<span class="fc" id="L107">    }</span>

    public OrderStatus getStatus() {
<span class="fc" id="L110">        return status;</span>
    }

    public void setStatus(OrderStatus status) {
<span class="fc" id="L114">        this.status = status;</span>
<span class="fc" id="L115">    }</span>

    public Boolean getIsBuyerRated() {
<span class="fc" id="L118">        return isBuyerRated;</span>
    }

    public void setIsBuyerRated(Boolean buyerRated) {
<span class="fc" id="L122">        isBuyerRated = buyerRated;</span>
<span class="fc" id="L123">    }</span>

    public Boolean getIsSellerRated() {
<span class="fc" id="L126">        return isSellerRated;</span>
    }

    public void setIsSellerRated(Boolean sellerRated) {
<span class="fc" id="L130">        isSellerRated = sellerRated;</span>
<span class="fc" id="L131">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L134">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L138">        this.createdAt = createdAt;</span>
<span class="fc" id="L139">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L142">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L146">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L147">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>