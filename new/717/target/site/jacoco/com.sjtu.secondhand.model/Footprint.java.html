<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Footprint.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Footprint.java</span></div><h1>Footprint.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户浏览足迹实体类
 */
@Entity
@Table(name = &quot;footprints&quot;, uniqueConstraints = {
    @UniqueConstraint(name = &quot;uk_user_item&quot;, columnNames = {&quot;user_id&quot;, &quot;item_id&quot;})
})
public class Footprint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;user_id&quot;, nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;item_id&quot;, nullable = false)
    private Item item;

    @Column(name = &quot;view_time&quot;, nullable = false)
    private LocalDateTime viewTime;

    @PrePersist
    protected void onCreate() {
<span class="nc" id="L32">        viewTime = LocalDateTime.now();</span>
<span class="nc" id="L33">    }</span>
    
    // 默认构造函数
<span class="fc" id="L36">    public Footprint() {</span>
<span class="fc" id="L37">    }</span>
    
    // 全参数构造函数
<span class="fc" id="L40">    public Footprint(Long id, User user, Item item, LocalDateTime viewTime) {</span>
<span class="fc" id="L41">        this.id = id;</span>
<span class="fc" id="L42">        this.user = user;</span>
<span class="fc" id="L43">        this.item = item;</span>
<span class="fc" id="L44">        this.viewTime = viewTime;</span>
<span class="fc" id="L45">    }</span>
    
    // 静态 builder 方法
    public static FootprintBuilder builder() {
<span class="fc" id="L49">        return new FootprintBuilder();</span>
    }
    
    // Builder 类
<span class="fc" id="L53">    public static class FootprintBuilder {</span>
        private Long id;
        private User user;
        private Item item;
        private LocalDateTime viewTime;
        
        public FootprintBuilder id(Long id) {
<span class="nc" id="L60">            this.id = id;</span>
<span class="nc" id="L61">            return this;</span>
        }
        
        public FootprintBuilder user(User user) {
<span class="fc" id="L65">            this.user = user;</span>
<span class="fc" id="L66">            return this;</span>
        }
        
        public FootprintBuilder item(Item item) {
<span class="fc" id="L70">            this.item = item;</span>
<span class="fc" id="L71">            return this;</span>
        }
        
        public FootprintBuilder viewTime(LocalDateTime viewTime) {
<span class="fc" id="L75">            this.viewTime = viewTime;</span>
<span class="fc" id="L76">            return this;</span>
        }
        
        public Footprint build() {
<span class="fc" id="L80">            return new Footprint(id, user, item, viewTime);</span>
        }
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
<span class="nc" id="L86">        return id;</span>
    }
    
    public void setId(Long id) {
<span class="fc" id="L90">        this.id = id;</span>
<span class="fc" id="L91">    }</span>
    
    public User getUser() {
<span class="nc" id="L94">        return user;</span>
    }
    
    public void setUser(User user) {
<span class="fc" id="L98">        this.user = user;</span>
<span class="fc" id="L99">    }</span>
    
    public Item getItem() {
<span class="fc" id="L102">        return item;</span>
    }
    
    public void setItem(Item item) {
<span class="fc" id="L106">        this.item = item;</span>
<span class="fc" id="L107">    }</span>
    
    public LocalDateTime getViewTime() {
<span class="fc" id="L110">        return viewTime;</span>
    }
    
    public void setViewTime(LocalDateTime viewTime) {
<span class="fc" id="L114">        this.viewTime = viewTime;</span>
<span class="fc" id="L115">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>