<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Comment.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Comment.java</span></div><h1>Comment.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = &quot;comments&quot;)
public class Comment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;item_id&quot;, nullable = false)
    private Item item;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;user_id&quot;, nullable = false)
    private User user;

    @NotBlank
    @Column(columnDefinition = &quot;TEXT&quot;)
    private String content;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;parent_id&quot;)
    private Comment parent;

<span class="fc" id="L33">    @OneToMany(mappedBy = &quot;parent&quot;, cascade = CascadeType.ALL)</span>
    private List&lt;Comment&gt; replies = new ArrayList&lt;&gt;();

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
<span class="fc" id="L41">        createdAt = LocalDateTime.now();</span>
<span class="fc" id="L42">    }</span>

    // Constructors
<span class="fc" id="L45">    public Comment() {</span>
<span class="fc" id="L46">    }</span>

<span class="fc" id="L48">    public Comment(Item item, User user, String content) {</span>
<span class="fc" id="L49">        this.item = item;</span>
<span class="fc" id="L50">        this.user = user;</span>
<span class="fc" id="L51">        this.content = content;</span>
<span class="fc" id="L52">    }</span>

<span class="fc" id="L54">    public Comment(Item item, User user, String content, Comment parent) {</span>
<span class="fc" id="L55">        this.item = item;</span>
<span class="fc" id="L56">        this.user = user;</span>
<span class="fc" id="L57">        this.content = content;</span>
<span class="fc" id="L58">        this.parent = parent;</span>
<span class="fc" id="L59">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L63">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L67">        this.id = id;</span>
<span class="fc" id="L68">    }</span>

    public Item getItem() {
<span class="fc" id="L71">        return item;</span>
    }

    public void setItem(Item item) {
<span class="fc" id="L75">        this.item = item;</span>
<span class="fc" id="L76">    }</span>

    public User getUser() {
<span class="fc" id="L79">        return user;</span>
    }

    public void setUser(User user) {
<span class="fc" id="L83">        this.user = user;</span>
<span class="fc" id="L84">    }</span>

    public String getContent() {
<span class="fc" id="L87">        return content;</span>
    }

    public void setContent(String content) {
<span class="fc" id="L91">        this.content = content;</span>
<span class="fc" id="L92">    }</span>

    public Comment getParent() {
<span class="fc" id="L95">        return parent;</span>
    }

    public void setParent(Comment parent) {
<span class="fc" id="L99">        this.parent = parent;</span>
<span class="fc" id="L100">    }</span>

    public List&lt;Comment&gt; getReplies() {
<span class="fc" id="L103">        return replies;</span>
    }

    public void setReplies(List&lt;Comment&gt; replies) {
<span class="fc" id="L107">        this.replies = replies;</span>
<span class="fc" id="L108">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L111">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L115">        this.createdAt = createdAt;</span>
<span class="fc" id="L116">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>