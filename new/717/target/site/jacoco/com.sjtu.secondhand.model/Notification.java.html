<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Notification.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Notification.java</span></div><h1>Notification.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = &quot;notifications&quot;)
public class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;recipient_id&quot;, nullable = false)
    private User recipient; // 通知接收者

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;sender_id&quot;)
    private User sender; // 通知发送者，系统通知为null

    @NotNull
    @Enumerated(EnumType.STRING)
    private NotificationType type;

    @Column(name = &quot;related_entity_id&quot;, nullable = false)
    private Long relatedEntityId;

    @Column(name = &quot;content&quot;, columnDefinition = &quot;JSON&quot;)
    private String content; // JSON格式的通知内容

<span class="pc" id="L33">    @Column(name = &quot;is_read&quot;)</span>
<span class="pc" id="L34">    private Boolean isRead = false;</span>

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
<span class="nc" id="L41">        createdAt = LocalDateTime.now();</span>
<span class="nc" id="L42">    }</span>

    // 通知类型枚举
<span class="fc" id="L45">    public enum NotificationType {</span>
        // 闲置物品订单相关通知
<span class="fc" id="L47">        IDLE_NEW_ORDER, // 新订单通知</span>
<span class="fc" id="L48">        IDLE_ORDER_CONFIRMED, // 订单确认通知</span>
<span class="fc" id="L49">        IDLE_ORDER_REJECTED, // 订单拒绝通知</span>
<span class="fc" id="L50">        IDLE_BUYER_ACKNOWLEDGED, // 买家确认通知</span>
<span class="fc" id="L51">        IDLE_ORDER_CANCELLED, // 订单取消通知</span>
<span class="fc" id="L52">        IDLE_ORDER_COMPLETED, // 订单完成通知</span>
<span class="fc" id="L53">        IDLE_CONTACT_CONFIRMED, // 联系确认通知</span>

        // 求好物相关通知
<span class="fc" id="L56">        WANTED_NEW_OFFER, // 新报价通知</span>
<span class="fc" id="L57">        WANTED_OFFER_ACCEPTED, // 报价接受通知</span>
<span class="fc" id="L58">        WANTED_OFFER_REJECTED, // 报价拒绝通知</span>
<span class="fc" id="L59">        WANTED_OFFER_CONFIRMED, // 报价确认通知</span>
<span class="fc" id="L60">        WANTED_OFFERER_CONFIRMED, // 报价者确认通知</span>

        // 通用通知
<span class="fc" id="L63">        TRANSACTION_COMPLETED, // 交易完成通知</span>
<span class="fc" id="L64">        NEW_COMMENT_ON_ITEM, // 新评论通知</span>
<span class="fc" id="L65">        NEW_REPLY_TO_COMMENT, // 评论回复通知</span>
<span class="fc" id="L66">        NEW_RATING_RECEIVED, // 新评价通知</span>

        // 系统通知
<span class="fc" id="L69">        SYSTEM_WELCOME // 欢迎通知</span>
    }

    // Constructors
<span class="fc" id="L73">    public Notification() {</span>
<span class="fc" id="L74">    }</span>

<span class="nc" id="L76">    public Notification(User recipient, User sender, NotificationType type, Long relatedEntityId) {</span>
<span class="nc" id="L77">        this.recipient = recipient;</span>
<span class="nc" id="L78">        this.sender = sender;</span>
<span class="nc" id="L79">        this.type = type;</span>
<span class="nc" id="L80">        this.relatedEntityId = relatedEntityId;</span>
<span class="nc" id="L81">    }</span>

<span class="fc" id="L83">    public Notification(User recipient, User sender, NotificationType type, Long relatedEntityId, String content) {</span>
<span class="fc" id="L84">        this.recipient = recipient;</span>
<span class="fc" id="L85">        this.sender = sender;</span>
<span class="fc" id="L86">        this.type = type;</span>
<span class="fc" id="L87">        this.relatedEntityId = relatedEntityId;</span>
<span class="fc" id="L88">        this.content = content;</span>
<span class="fc" id="L89">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L93">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L97">        this.id = id;</span>
<span class="fc" id="L98">    }</span>

    public User getRecipient() {
<span class="fc" id="L101">        return recipient;</span>
    }

    public void setRecipient(User recipient) {
<span class="fc" id="L105">        this.recipient = recipient;</span>
<span class="fc" id="L106">    }</span>

    public User getSender() {
<span class="fc" id="L109">        return sender;</span>
    }

    public void setSender(User sender) {
<span class="nc" id="L113">        this.sender = sender;</span>
<span class="nc" id="L114">    }</span>

    public NotificationType getType() {
<span class="fc" id="L117">        return type;</span>
    }

    public void setType(NotificationType type) {
<span class="fc" id="L121">        this.type = type;</span>
<span class="fc" id="L122">    }</span>

    public Long getRelatedEntityId() {
<span class="fc" id="L125">        return relatedEntityId;</span>
    }

    public void setRelatedEntityId(Long relatedEntityId) {
<span class="nc" id="L129">        this.relatedEntityId = relatedEntityId;</span>
<span class="nc" id="L130">    }</span>

    public String getContent() {
<span class="fc" id="L133">        return content;</span>
    }

    public void setContent(String content) {
<span class="fc" id="L137">        this.content = content;</span>
<span class="fc" id="L138">    }</span>

    public Boolean getIsRead() {
<span class="fc" id="L141">        return isRead;</span>
    }

    public void setIsRead(Boolean read) {
<span class="fc" id="L145">        isRead = read;</span>
<span class="fc" id="L146">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L149">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L153">        this.createdAt = createdAt;</span>
<span class="fc" id="L154">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>