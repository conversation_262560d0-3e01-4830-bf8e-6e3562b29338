<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Offer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">Offer.java</span></div><h1>Offer.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = &quot;offers&quot;)
public class Offer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;wanted_item_id&quot;, nullable = false)
    private Item wantedItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;offerer_id&quot;, nullable = false)
    private User offerer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;requester_id&quot;, nullable = false)
    private User requester;

    @Enumerated(EnumType.STRING)
    @Column(name = &quot;status&quot;)
    private OfferStatus status;

<span class="fc" id="L30">    @Column(name = &quot;is_offerer_rated&quot;)</span>
<span class="fc" id="L31">    private Boolean isOffererRated = false;</span>

<span class="fc" id="L33">    @Column(name = &quot;is_requester_rated&quot;)</span>
<span class="fc" id="L34">    private Boolean isRequesterRated = false;</span>

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @Column(name = &quot;updated_at&quot;)
    private LocalDateTime updatedAt;

<span class="fc" id="L42">    public enum OfferStatus {</span>
<span class="fc" id="L43">        PENDING_ACCEPTANCE, // 等待求购者接受</span>
<span class="fc" id="L44">        REJECTED, // 求购者已拒绝</span>
<span class="fc" id="L45">        ACCEPTED, // 求购者已接受</span>
<span class="fc" id="L46">        CONFIRMED, // 响应者已确认</span>
<span class="fc" id="L47">        COMPLETED, // 交易完成</span>
<span class="fc" id="L48">        CANCELLED // 交易取消</span>
    }

    @PrePersist
    protected void onCreate() {
<span class="fc" id="L53">        createdAt = LocalDateTime.now();</span>
<span class="fc" id="L54">        updatedAt = LocalDateTime.now();</span>
<span class="fc bfc" id="L55" title="All 2 branches covered.">        if (status == null) {</span>
<span class="fc" id="L56">            status = OfferStatus.PENDING_ACCEPTANCE;</span>
        }
<span class="fc" id="L58">    }</span>

    @PreUpdate
    protected void onUpdate() {
<span class="fc" id="L62">        updatedAt = LocalDateTime.now();</span>
<span class="fc" id="L63">    }</span>

    // Constructors
<span class="fc" id="L66">    public Offer() {</span>
<span class="fc" id="L67">    }</span>

<span class="fc" id="L69">    public Offer(Item wantedItem, User offerer, User requester) {</span>
<span class="fc" id="L70">        this.wantedItem = wantedItem;</span>
<span class="fc" id="L71">        this.offerer = offerer;</span>
<span class="fc" id="L72">        this.requester = requester;</span>
<span class="fc" id="L73">        this.status = OfferStatus.PENDING_ACCEPTANCE;</span>
<span class="fc" id="L74">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L78">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L82">        this.id = id;</span>
<span class="fc" id="L83">    }</span>

    public Item getWantedItem() {
<span class="fc" id="L86">        return wantedItem;</span>
    }

    public void setWantedItem(Item wantedItem) {
<span class="fc" id="L90">        this.wantedItem = wantedItem;</span>
<span class="fc" id="L91">    }</span>

    public User getOfferer() {
<span class="fc" id="L94">        return offerer;</span>
    }

    public void setOfferer(User offerer) {
<span class="fc" id="L98">        this.offerer = offerer;</span>
<span class="fc" id="L99">    }</span>

    public User getRequester() {
<span class="fc" id="L102">        return requester;</span>
    }

    public void setRequester(User requester) {
<span class="fc" id="L106">        this.requester = requester;</span>
<span class="fc" id="L107">    }</span>

    public OfferStatus getStatus() {
<span class="fc" id="L110">        return status;</span>
    }

    public void setStatus(OfferStatus status) {
<span class="fc" id="L114">        this.status = status;</span>
<span class="fc" id="L115">    }</span>

    public Boolean getIsOffererRated() {
<span class="fc" id="L118">        return isOffererRated;</span>
    }

    public void setIsOffererRated(Boolean offererRated) {
<span class="fc" id="L122">        isOffererRated = offererRated;</span>
<span class="fc" id="L123">    }</span>

    public Boolean getIsRequesterRated() {
<span class="fc" id="L126">        return isRequesterRated;</span>
    }

    public void setIsRequesterRated(Boolean requesterRated) {
<span class="fc" id="L130">        isRequesterRated = requesterRated;</span>
<span class="fc" id="L131">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L134">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L138">        this.createdAt = createdAt;</span>
<span class="fc" id="L139">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L142">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L146">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L147">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>