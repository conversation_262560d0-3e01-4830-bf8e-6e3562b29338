<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemImage.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">ItemImage.java</span></div><h1>ItemImage.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Entity
@Table(name = &quot;item_images&quot;)
public class ItemImage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = &quot;item_id&quot;, nullable = false)
    private Item item;

    @NotBlank
    @Size(max = 512)
    private String url;

<span class="fc" id="L23">    @Column(name = &quot;is_primary&quot;)</span>
<span class="fc" id="L24">    private Boolean isPrimary = false;</span>

    // Constructors
<span class="fc" id="L27">    public ItemImage() {</span>
<span class="fc" id="L28">    }</span>

<span class="fc" id="L30">    public ItemImage(Item item, String url, Boolean isPrimary) {</span>
<span class="fc" id="L31">        this.item = item;</span>
<span class="fc" id="L32">        this.url = url;</span>
<span class="fc" id="L33">        this.isPrimary = isPrimary;</span>
<span class="fc" id="L34">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L38">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L42">        this.id = id;</span>
<span class="fc" id="L43">    }</span>

    public Item getItem() {
<span class="fc" id="L46">        return item;</span>
    }

    public void setItem(Item item) {
<span class="fc" id="L50">        this.item = item;</span>
<span class="fc" id="L51">    }</span>

    public String getUrl() {
<span class="fc" id="L54">        return url;</span>
    }

    public void setUrl(String url) {
<span class="fc" id="L58">        this.url = url;</span>
<span class="fc" id="L59">    }</span>

    public Boolean getIsPrimary() {
<span class="fc" id="L62">        return isPrimary;</span>
    }

    public void setIsPrimary(Boolean primary) {
<span class="fc" id="L66">        isPrimary = primary;</span>
<span class="fc" id="L67">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>