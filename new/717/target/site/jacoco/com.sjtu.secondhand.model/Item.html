<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Item</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_class">Item</span></div><h1>Item</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">11 of 200</td><td class="ctr2">94%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">2</td><td class="ctr2">38</td><td class="ctr1">5</td><td class="ctr2">75</td><td class="ctr1">2</td><td class="ctr2">38</td></tr></tfoot><tbody><tr><td id="a19"><a href="Item.java.html#L79" class="el_method">onCreate()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="7" alt="7"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">1</td><td class="ctr1" id="h0">3</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a20"><a href="Item.java.html#L85" class="el_method">onUpdate()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a18"><a href="Item.java.html#L30" class="el_method">Item(User, Category, String, String, BigDecimal, Item.ItemCondition)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="44" alt="44"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a17"><a href="Item.java.html#L30" class="el_method">Item()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="26" alt="26"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a26"><a href="Item.java.html#L121" class="el_method">setId(Long)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a36"><a href="Item.java.html#L129" class="el_method">setUser(User)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a21"><a href="Item.java.html#L137" class="el_method">setCategory(Category)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a30"><a href="Item.java.html#L145" class="el_method">setName(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a24"><a href="Item.java.html#L153" class="el_method">setDescription(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a31"><a href="Item.java.html#L161" class="el_method">setPrice(BigDecimal)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a33"><a href="Item.java.html#L169" class="el_method">setPriceMin(BigDecimal)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a32"><a href="Item.java.html#L177" class="el_method">setPriceMax(BigDecimal)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a22"><a href="Item.java.html#L185" class="el_method">setCondition(Item.ItemCondition)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a34"><a href="Item.java.html#L193" class="el_method">setStatus(Item.ItemStatus)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a28"><a href="Item.java.html#L201" class="el_method">setIsVisible(Boolean)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a37"><a href="Item.java.html#L209" class="el_method">setViewCount(Integer)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a25"><a href="Item.java.html#L217" class="el_method">setFavoriteCount(Integer)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a23"><a href="Item.java.html#L225" class="el_method">setCreatedAt(LocalDateTime)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a35"><a href="Item.java.html#L233" class="el_method">setUpdatedAt(LocalDateTime)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a27"><a href="Item.java.html#L241" class="el_method">setImages(List)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a29"><a href="Item.java.html#L249" class="el_method">setItemType(Item.ItemType)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a5"><a href="Item.java.html#L117" class="el_method">getId()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a15"><a href="Item.java.html#L125" class="el_method">getUser()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a0"><a href="Item.java.html#L133" class="el_method">getCategory()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a9"><a href="Item.java.html#L141" class="el_method">getName()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a3"><a href="Item.java.html#L149" class="el_method">getDescription()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a10"><a href="Item.java.html#L157" class="el_method">getPrice()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c24">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a12"><a href="Item.java.html#L165" class="el_method">getPriceMin()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c25">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a11"><a href="Item.java.html#L173" class="el_method">getPriceMax()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c26">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a1"><a href="Item.java.html#L181" class="el_method">getCondition()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c27">100%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">0</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a13"><a href="Item.java.html#L189" class="el_method">getStatus()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c28">100%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">0</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a7"><a href="Item.java.html#L197" class="el_method">getIsVisible()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c29">100%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">0</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a16"><a href="Item.java.html#L205" class="el_method">getViewCount()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c30">100%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">0</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a4"><a href="Item.java.html#L213" class="el_method">getFavoriteCount()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c31">100%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">0</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a2"><a href="Item.java.html#L221" class="el_method">getCreatedAt()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c32">100%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">0</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">0</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a14"><a href="Item.java.html#L229" class="el_method">getUpdatedAt()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c33">100%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">0</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">0</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">0</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a6"><a href="Item.java.html#L237" class="el_method">getImages()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c34">100%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">0</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">0</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a8"><a href="Item.java.html#L245" class="el_method">getItemType()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c35">100%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">0</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">0</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">0</td><td class="ctr2" id="k37">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>