<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>User.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">User.java</span></div><h1>User.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = &quot;users&quot;, uniqueConstraints = {
        @UniqueConstraint(columnNames = &quot;username&quot;),
        @UniqueConstraint(columnNames = &quot;email&quot;)
})
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 50)
    private String username;

    @NotBlank
    @Size(max = 255)
    private String password;

    @NotBlank
    @Size(max = 255)
    @Email
    private String email;

    @Column(name = &quot;avatar_url&quot;)
    @Size(max = 512)
    private String avatarUrl;

<span class="fc" id="L40">    @Column(name = &quot;credit_score&quot;)</span>
<span class="fc" id="L41">    private Integer creditScore = 50;</span>

<span class="fc" id="L43">    @Column(name = &quot;points&quot;)</span>
<span class="fc" id="L44">    private Integer points = 0;</span>

    @Column(name = &quot;last_check_in_date&quot;)
    private LocalDate lastCheckInDate;

    @Column(name = &quot;created_at&quot;)
    private LocalDateTime createdAt;

    @Column(name = &quot;updated_at&quot;)
    private LocalDateTime updatedAt;

<span class="fc" id="L55">    @ManyToMany(fetch = FetchType.LAZY)</span>
    @JoinTable(name = &quot;favorites&quot;, joinColumns = @JoinColumn(name = &quot;user_id&quot;), inverseJoinColumns = @JoinColumn(name = &quot;item_id&quot;))
    private Set&lt;Item&gt; favoriteItems = new HashSet&lt;&gt;();

    @PrePersist
    protected void onCreate() {
<span class="fc" id="L61">        createdAt = LocalDateTime.now();</span>
<span class="fc" id="L62">        updatedAt = LocalDateTime.now();</span>
<span class="fc" id="L63">    }</span>

    @PreUpdate
    protected void onUpdate() {
<span class="fc" id="L67">        updatedAt = LocalDateTime.now();</span>
<span class="fc" id="L68">    }</span>

    // Constructors
<span class="fc" id="L71">    public User() {</span>
<span class="fc" id="L72">    }</span>

<span class="fc" id="L74">    public User(String username, String password, String email) {</span>
<span class="fc" id="L75">        this.username = username;</span>
<span class="fc" id="L76">        this.password = password;</span>
<span class="fc" id="L77">        this.email = email;</span>
<span class="fc" id="L78">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L82">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L86">        this.id = id;</span>
<span class="fc" id="L87">    }</span>

    public String getUsername() {
<span class="fc" id="L90">        return username;</span>
    }

    public void setUsername(String username) {
<span class="fc" id="L94">        this.username = username;</span>
<span class="fc" id="L95">    }</span>

    public String getPassword() {
<span class="fc" id="L98">        return password;</span>
    }

    public void setPassword(String password) {
<span class="fc" id="L102">        this.password = password;</span>
<span class="fc" id="L103">    }</span>

    public String getEmail() {
<span class="fc" id="L106">        return email;</span>
    }

    public void setEmail(String email) {
<span class="fc" id="L110">        this.email = email;</span>
<span class="fc" id="L111">    }</span>

    public String getAvatarUrl() {
<span class="fc" id="L114">        return avatarUrl;</span>
    }

    public void setAvatarUrl(String avatarUrl) {
<span class="fc" id="L118">        this.avatarUrl = avatarUrl;</span>
<span class="fc" id="L119">    }</span>

    // 兼容性方法，保持向后兼容
    public String getAvatar() {
<span class="fc" id="L123">        return avatarUrl;</span>
    }

    // 兼容性方法，保持向后兼容
    public void setAvatar(String avatar) {
<span class="fc" id="L128">        this.avatarUrl = avatar;</span>
<span class="fc" id="L129">    }</span>
    
    // 兼容性方法，返回null表示无手机号
    public String getPhone() {
<span class="fc" id="L133">        return null;</span>
    }
    
    // 兼容性方法，将createdAt格式化为前端期望的joinedAt日期字符串
    public String getJoinedAt() {
<span class="fc bfc" id="L138" title="All 2 branches covered.">        if (createdAt == null) {</span>
<span class="fc" id="L139">            return null;</span>
        }
<span class="fc" id="L141">        return createdAt.toString();</span>
    }

    public Integer getCreditScore() {
<span class="fc" id="L145">        return creditScore;</span>
    }

    public void setCreditScore(Integer creditScore) {
<span class="fc" id="L149">        this.creditScore = creditScore;</span>
<span class="fc" id="L150">    }</span>

    /**
     * 获取用户评分（与信用分相同）
     * 
     * @return 用户评分
     */
    public Integer getRating() {
<span class="fc" id="L158">        return creditScore;</span>
    }

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L162">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L166">        this.createdAt = createdAt;</span>
<span class="fc" id="L167">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L170">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L174">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L175">    }</span>

    public Set&lt;Item&gt; getFavoriteItems() {
<span class="fc" id="L178">        return favoriteItems;</span>
    }

    public void setFavoriteItems(Set&lt;Item&gt; favoriteItems) {
<span class="fc" id="L182">        this.favoriteItems = favoriteItems;</span>
<span class="fc" id="L183">    }</span>

    public LocalDate getLastCheckInDate() {
<span class="fc" id="L186">        return lastCheckInDate;</span>
    }

    public void setLastCheckInDate(LocalDate lastCheckInDate) {
<span class="fc" id="L190">        this.lastCheckInDate = lastCheckInDate;</span>
<span class="fc" id="L191">    }</span>

    public Integer getPoints() {
<span class="fc" id="L194">        return points;</span>
    }

    public void setPoints(Integer points) {
<span class="fc" id="L198">        this.points = points;</span>
<span class="fc" id="L199">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>