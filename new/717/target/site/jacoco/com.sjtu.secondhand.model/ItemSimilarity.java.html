<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemSimilarity.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model</a> &gt; <span class="el_source">ItemSimilarity.java</span></div><h1>ItemSimilarity.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 物品相似度实体，用于存储协同过滤相似度数据
 * 对应 item_similarity 表
 */
@Entity
@Table(name = &quot;item_similarity&quot;, 
       uniqueConstraints = @UniqueConstraint(name = &quot;uk_item_pair&quot;, columnNames = {&quot;item_id_1&quot;, &quot;item_id_2&quot;}),
       indexes = @Index(name = &quot;idx_item1_score&quot;, columnList = &quot;item_id_1, score DESC&quot;))
public class ItemSimilarity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = &quot;item_id_1&quot;, nullable = false)
    private Long itemId1;

    @Column(name = &quot;item_id_2&quot;, nullable = false)
    private Long itemId2;

    @Column(name = &quot;score&quot;, nullable = false)
    private Double score;

    @Column(name = &quot;created_at&quot;, nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
<span class="fc" id="L34">        createdAt = LocalDateTime.now();</span>
<span class="fc" id="L35">    }</span>

    // 构造函数
<span class="fc" id="L38">    public ItemSimilarity() {</span>
<span class="fc" id="L39">    }</span>

<span class="fc" id="L41">    public ItemSimilarity(Long itemId1, Long itemId2, Double score) {</span>
<span class="fc" id="L42">        this.itemId1 = itemId1;</span>
<span class="fc" id="L43">        this.itemId2 = itemId2;</span>
<span class="fc" id="L44">        this.score = score;</span>
<span class="fc" id="L45">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L49">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L53">        this.id = id;</span>
<span class="fc" id="L54">    }</span>

    public Long getItemId1() {
<span class="fc" id="L57">        return itemId1;</span>
    }

    public void setItemId1(Long itemId1) {
<span class="fc" id="L61">        this.itemId1 = itemId1;</span>
<span class="fc" id="L62">    }</span>

    public Long getItemId2() {
<span class="fc" id="L65">        return itemId2;</span>
    }

    public void setItemId2(Long itemId2) {
<span class="fc" id="L69">        this.itemId2 = itemId2;</span>
<span class="fc" id="L70">    }</span>

    public Double getScore() {
<span class="fc" id="L73">        return score;</span>
    }

    public void setScore(Double score) {
<span class="fc" id="L77">        this.score = score;</span>
<span class="fc" id="L78">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L81">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L85">        this.createdAt = createdAt;</span>
<span class="fc" id="L86">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>