<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RatingResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">RatingResponse.java</span></div><h1>RatingResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Rating;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class RatingResponse {

    private Long id;
    private Map&lt;String, Object&gt; rater;
    private Map&lt;String, Object&gt; ratee;
    private Integer score;
    private String transaction_type;
    private Long related_transaction_id;
    private LocalDateTime created_at;
    private Map&lt;String, Object&gt; transaction;

<span class="fc" id="L20">    public RatingResponse() {</span>
<span class="fc" id="L21">    }</span>

<span class="fc" id="L23">    public RatingResponse(Rating rating) {</span>
<span class="fc" id="L24">        this.id = rating.getId();</span>
<span class="fc bfc" id="L25" title="All 2 branches covered.">        this.score = rating.getScore() != null ? rating.getScore().intValue() : null;</span>
        
        // 将枚举类型转换为字符串
<span class="fc bfc" id="L28" title="All 2 branches covered.">        if (rating.getTransactionType() == Rating.TransactionType.IDLE) {</span>
<span class="fc" id="L29">            this.transaction_type = &quot;IDLE&quot;;</span>
<span class="fc bfc" id="L30" title="All 2 branches covered.">        } else if (rating.getTransactionType() == Rating.TransactionType.WANTED) {</span>
<span class="fc" id="L31">            this.transaction_type = &quot;WANTED&quot;;</span>
        }
        
<span class="fc" id="L34">        this.related_transaction_id = rating.getRelatedTransactionId();</span>
<span class="fc" id="L35">        this.created_at = rating.getCreatedAt();</span>

        // 设置评价人信息
<span class="fc" id="L38">        this.rater = new HashMap&lt;&gt;();</span>
<span class="fc" id="L39">        this.rater.put(&quot;id&quot;, rating.getRater().getId());</span>
<span class="fc" id="L40">        this.rater.put(&quot;username&quot;, rating.getRater().getUsername());</span>
<span class="fc" id="L41">        this.rater.put(&quot;avatar_url&quot;, rating.getRater().getAvatarUrl());</span>

        // 设置被评价人信息
<span class="fc" id="L44">        this.ratee = new HashMap&lt;&gt;();</span>
<span class="fc" id="L45">        this.ratee.put(&quot;id&quot;, rating.getRatee().getId());</span>
<span class="fc" id="L46">        this.ratee.put(&quot;username&quot;, rating.getRatee().getUsername());</span>
<span class="fc" id="L47">        this.ratee.put(&quot;avatar_url&quot;, rating.getRatee().getAvatarUrl());</span>
<span class="fc" id="L48">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L52">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L56">        this.id = id;</span>
<span class="fc" id="L57">    }</span>

    public Map&lt;String, Object&gt; getRater() {
<span class="fc" id="L60">        return rater;</span>
    }

    public void setRater(Map&lt;String, Object&gt; rater) {
<span class="fc" id="L64">        this.rater = rater;</span>
<span class="fc" id="L65">    }</span>

    public Map&lt;String, Object&gt; getRatee() {
<span class="fc" id="L68">        return ratee;</span>
    }

    public void setRatee(Map&lt;String, Object&gt; ratee) {
<span class="fc" id="L72">        this.ratee = ratee;</span>
<span class="fc" id="L73">    }</span>

    public Integer getScore() {
<span class="fc" id="L76">        return score;</span>
    }

    public void setScore(Integer score) {
<span class="fc" id="L80">        this.score = score;</span>
<span class="fc" id="L81">    }</span>

    public String getTransaction_type() {
<span class="fc" id="L84">        return transaction_type;</span>
    }

    public void setTransaction_type(String transaction_type) {
<span class="fc" id="L88">        this.transaction_type = transaction_type;</span>
<span class="fc" id="L89">    }</span>

    public Long getRelated_transaction_id() {
<span class="fc" id="L92">        return related_transaction_id;</span>
    }

    public void setRelated_transaction_id(Long related_transaction_id) {
<span class="fc" id="L96">        this.related_transaction_id = related_transaction_id;</span>
<span class="fc" id="L97">    }</span>

    public LocalDateTime getCreated_at() {
<span class="fc" id="L100">        return created_at;</span>
    }

    public void setCreated_at(LocalDateTime created_at) {
<span class="fc" id="L104">        this.created_at = created_at;</span>
<span class="fc" id="L105">    }</span>

    public Map&lt;String, Object&gt; getTransaction() {
<span class="fc" id="L108">        return transaction;</span>
    }

    public void setTransaction(Map&lt;String, Object&gt; transaction) {
<span class="fc" id="L112">        this.transaction = transaction;</span>
<span class="fc" id="L113">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>