<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NotificationDto.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">NotificationDto.java</span></div><h1>NotificationDto.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Notification;

import java.time.LocalDateTime;

public class NotificationDto {

    private Long id;
    private Notification.NotificationType type;
    private String content;
    private Boolean isRead;
    private LocalDateTime createdAt;
    private Long relatedEntityId;
    private Long senderId;
    private String senderUsername;

    // 添加前端期望的字段
    private String title; // 通知标题
    private boolean is_read; // 与前端字段名保持一致

    // 添加订单确认通知所需的字段
    private String sellerEmail; // 卖家邮箱
    private String sellerUsername; // 卖家用户名
    private String itemName; // 商品名称

    // Constructors
<span class="fc" id="L28">    public NotificationDto() {</span>
<span class="fc" id="L29">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L33">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L37">        this.id = id;</span>
<span class="fc" id="L38">    }</span>

    public Notification.NotificationType getType() {
<span class="fc" id="L41">        return type;</span>
    }

    public void setType(Notification.NotificationType type) {
<span class="fc" id="L45">        this.type = type;</span>
<span class="fc" id="L46">    }</span>

    public String getContent() {
<span class="fc" id="L49">        return content;</span>
    }

    public void setContent(String content) {
<span class="fc" id="L53">        this.content = content;</span>
<span class="fc" id="L54">    }</span>

    public Boolean getIsRead() {
<span class="fc" id="L57">        return isRead;</span>
    }

    public void setIsRead(Boolean isRead) {
<span class="fc" id="L61">        this.isRead = isRead;</span>
<span class="fc" id="L62">        this.is_read = isRead; // 同时更新前端期望的字段</span>
<span class="fc" id="L63">    }</span>

    // 前端期望的is_read getter和setter
    public boolean getIs_read() {
<span class="fc" id="L67">        return is_read;</span>
    }

    public void setIs_read(boolean is_read) {
<span class="fc" id="L71">        this.is_read = is_read;</span>
<span class="fc" id="L72">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L75">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L79">        this.createdAt = createdAt;</span>
<span class="fc" id="L80">    }</span>

    public Long getRelatedEntityId() {
<span class="fc" id="L83">        return relatedEntityId;</span>
    }

    public void setRelatedEntityId(Long relatedEntityId) {
<span class="fc" id="L87">        this.relatedEntityId = relatedEntityId;</span>
<span class="fc" id="L88">    }</span>

    public Long getSenderId() {
<span class="fc" id="L91">        return senderId;</span>
    }

    public void setSenderId(Long senderId) {
<span class="fc" id="L95">        this.senderId = senderId;</span>
<span class="fc" id="L96">    }</span>

    public String getSenderUsername() {
<span class="fc" id="L99">        return senderUsername;</span>
    }

    public void setSenderUsername(String senderUsername) {
<span class="fc" id="L103">        this.senderUsername = senderUsername;</span>
<span class="fc" id="L104">    }</span>

    // 标题的getter和setter
    public String getTitle() {
<span class="fc" id="L108">        return title;</span>
    }

    public void setTitle(String title) {
<span class="fc" id="L112">        this.title = title;</span>
<span class="fc" id="L113">    }</span>

    // 卖家邮箱的getter和setter
    public String getSellerEmail() {
<span class="fc" id="L117">        return sellerEmail;</span>
    }

    public void setSellerEmail(String sellerEmail) {
<span class="fc" id="L121">        this.sellerEmail = sellerEmail;</span>
<span class="fc" id="L122">    }</span>

    // 卖家用户名的getter和setter
    public String getSellerUsername() {
<span class="fc" id="L126">        return sellerUsername;</span>
    }

    public void setSellerUsername(String sellerUsername) {
<span class="fc" id="L130">        this.sellerUsername = sellerUsername;</span>
<span class="fc" id="L131">    }</span>

    // 商品名称的getter和setter
    public String getItemName() {
<span class="fc" id="L135">        return itemName;</span>
    }

    public void setItemName(String itemName) {
<span class="fc" id="L139">        this.itemName = itemName;</span>
<span class="fc" id="L140">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>