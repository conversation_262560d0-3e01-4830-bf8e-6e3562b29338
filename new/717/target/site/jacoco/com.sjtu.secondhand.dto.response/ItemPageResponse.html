<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemPageResponse</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_class">ItemPageResponse</span></div><h1>ItemPageResponse</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 142</td><td class="ctr2">100%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">0</td><td class="ctr2">23</td><td class="ctr1">0</td><td class="ctr2">52</td><td class="ctr1">0</td><td class="ctr2">23</td></tr></tfoot><tbody><tr><td id="a11"><a href="ItemPageResponse.java.html#L37" class="el_method">ItemPageResponse(ItemPageResponse.Builder)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="27" alt="27"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">1</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a13"><a href="ItemPageResponse.java.html#L28" class="el_method">ItemPageResponse(Page)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="102" height="10" title="23" alt="23"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="ItemPageResponse.java.html#L19" class="el_method">ItemPageResponse(List, Page)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="97" height="10" title="22" alt="22"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="ItemPageResponse.java.html#L48" class="el_method">builder()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a16"><a href="ItemPageResponse.java.html#L100" class="el_method">setItems(List)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a15"><a href="ItemPageResponse.java.html#L108" class="el_method">setCurrentPage(int)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a18"><a href="ItemPageResponse.java.html#L116" class="el_method">setSize(int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a21"><a href="ItemPageResponse.java.html#L124" class="el_method">setTotalItems(long)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a22"><a href="ItemPageResponse.java.html#L132" class="el_method">setTotalPages(int)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a19"><a href="ItemPageResponse.java.html#L141" class="el_method">setSource(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="ItemPageResponse.java.html#L150" class="el_method">setContent(List)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a17"><a href="ItemPageResponse.java.html#L158" class="el_method">setPage(int)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a20"><a href="ItemPageResponse.java.html#L166" class="el_method">setTotalElements(long)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a10"><a href="ItemPageResponse.java.html#L16" class="el_method">ItemPageResponse()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a3"><a href="ItemPageResponse.java.html#L96" class="el_method">getItems()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a2"><a href="ItemPageResponse.java.html#L104" class="el_method">getCurrentPage()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a5"><a href="ItemPageResponse.java.html#L112" class="el_method">getSize()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a8"><a href="ItemPageResponse.java.html#L120" class="el_method">getTotalItems()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a9"><a href="ItemPageResponse.java.html#L128" class="el_method">getTotalPages()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a6"><a href="ItemPageResponse.java.html#L137" class="el_method">getSource()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a1"><a href="ItemPageResponse.java.html#L146" class="el_method">getContent()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a4"><a href="ItemPageResponse.java.html#L154" class="el_method">getPage()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a7"><a href="ItemPageResponse.java.html#L162" class="el_method">getTotalElements()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>