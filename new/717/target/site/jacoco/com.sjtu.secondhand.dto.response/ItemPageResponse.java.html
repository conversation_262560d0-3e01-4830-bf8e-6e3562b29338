<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemPageResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">ItemPageResponse.java</span></div><h1>ItemPageResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import org.springframework.data.domain.Page;

import java.util.List;

public class ItemPageResponse {

    private List&lt;ItemResponse&gt; items;
    private int currentPage;
    private int size;
    private long totalItems;
    private int totalPages;
    private String source; // 添加数据来源标记

<span class="fc" id="L16">    public ItemPageResponse() {</span>
<span class="fc" id="L17">    }</span>

<span class="fc" id="L19">    public ItemPageResponse(List&lt;ItemResponse&gt; items, Page&lt;?&gt; page) {</span>
<span class="fc" id="L20">        this.items = items;</span>
<span class="fc" id="L21">        this.currentPage = page.getNumber();</span>
<span class="fc" id="L22">        this.size = page.getSize();</span>
<span class="fc" id="L23">        this.totalItems = page.getTotalElements();</span>
<span class="fc" id="L24">        this.totalPages = page.getTotalPages();</span>
<span class="fc" id="L25">    }</span>

    // 添加一个额外的构造函数，用于直接从 Page&lt;ItemResponse&gt; 创建
<span class="fc" id="L28">    public ItemPageResponse(Page&lt;ItemResponse&gt; page) {</span>
<span class="fc" id="L29">        this.items = page.getContent();</span>
<span class="fc" id="L30">        this.currentPage = page.getNumber();</span>
<span class="fc" id="L31">        this.size = page.getSize();</span>
<span class="fc" id="L32">        this.totalItems = page.getTotalElements();</span>
<span class="fc" id="L33">        this.totalPages = page.getTotalPages();</span>
<span class="fc" id="L34">    }</span>
    
    // 添加一个私有构造函数，用于Builder模式
<span class="fc" id="L37">    private ItemPageResponse(Builder builder) {</span>
<span class="fc" id="L38">        this.items = builder.items;</span>
<span class="fc" id="L39">        this.currentPage = builder.currentPage;</span>
<span class="fc" id="L40">        this.size = builder.size;</span>
<span class="fc" id="L41">        this.totalItems = builder.totalItems;</span>
<span class="fc" id="L42">        this.totalPages = builder.totalPages;</span>
<span class="fc" id="L43">        this.source = builder.source;</span>
<span class="fc" id="L44">    }</span>
    
    // 静态Builder方法
    public static Builder builder() {
<span class="fc" id="L48">        return new Builder();</span>
    }
    
    // Builder类
<span class="fc" id="L52">    public static class Builder {</span>
        private List&lt;ItemResponse&gt; items;
        private int currentPage;
        private int size;
        private long totalItems;
        private int totalPages;
        private String source;
        
        public Builder items(List&lt;ItemResponse&gt; items) {
<span class="fc" id="L61">            this.items = items;</span>
<span class="fc" id="L62">            return this;</span>
        }
        
        public Builder currentPage(int currentPage) {
<span class="fc" id="L66">            this.currentPage = currentPage;</span>
<span class="fc" id="L67">            return this;</span>
        }
        
        public Builder size(int size) {
<span class="fc" id="L71">            this.size = size;</span>
<span class="fc" id="L72">            return this;</span>
        }
        
        public Builder totalItems(long totalItems) {
<span class="fc" id="L76">            this.totalItems = totalItems;</span>
<span class="fc" id="L77">            return this;</span>
        }
        
        public Builder totalPages(int totalPages) {
<span class="fc" id="L81">            this.totalPages = totalPages;</span>
<span class="fc" id="L82">            return this;</span>
        }
        
        public Builder source(String source) {
<span class="fc" id="L86">            this.source = source;</span>
<span class="fc" id="L87">            return this;</span>
        }
        
        public ItemPageResponse build() {
<span class="fc" id="L91">            return new ItemPageResponse(this);</span>
        }
    }

    public List&lt;ItemResponse&gt; getItems() {
<span class="fc" id="L96">        return items;</span>
    }

    public void setItems(List&lt;ItemResponse&gt; items) {
<span class="fc" id="L100">        this.items = items;</span>
<span class="fc" id="L101">    }</span>

    public int getCurrentPage() {
<span class="fc" id="L104">        return currentPage;</span>
    }

    public void setCurrentPage(int currentPage) {
<span class="fc" id="L108">        this.currentPage = currentPage;</span>
<span class="fc" id="L109">    }</span>

    public int getSize() {
<span class="fc" id="L112">        return size;</span>
    }

    public void setSize(int size) {
<span class="fc" id="L116">        this.size = size;</span>
<span class="fc" id="L117">    }</span>

    public long getTotalItems() {
<span class="fc" id="L120">        return totalItems;</span>
    }

    public void setTotalItems(long totalItems) {
<span class="fc" id="L124">        this.totalItems = totalItems;</span>
<span class="fc" id="L125">    }</span>

    public int getTotalPages() {
<span class="fc" id="L128">        return totalPages;</span>
    }

    public void setTotalPages(int totalPages) {
<span class="fc" id="L132">        this.totalPages = totalPages;</span>
<span class="fc" id="L133">    }</span>
    
    // 添加数据来源getter和setter
    public String getSource() {
<span class="fc" id="L137">        return source;</span>
    }
    
    public void setSource(String source) {
<span class="fc" id="L141">        this.source = source;</span>
<span class="fc" id="L142">    }</span>
    
    // 兼容旧版API
    public List&lt;ItemResponse&gt; getContent() {
<span class="fc" id="L146">        return items;</span>
    }
    
    public void setContent(List&lt;ItemResponse&gt; content) {
<span class="fc" id="L150">        this.items = content;</span>
<span class="fc" id="L151">    }</span>
    
    public int getPage() {
<span class="fc" id="L154">        return currentPage;</span>
    }
    
    public void setPage(int page) {
<span class="fc" id="L158">        this.currentPage = page;</span>
<span class="fc" id="L159">    }</span>
    
    public long getTotalElements() {
<span class="fc" id="L162">        return totalItems;</span>
    }
    
    public void setTotalElements(long totalElements) {
<span class="fc" id="L166">        this.totalItems = totalElements;</span>
<span class="fc" id="L167">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>