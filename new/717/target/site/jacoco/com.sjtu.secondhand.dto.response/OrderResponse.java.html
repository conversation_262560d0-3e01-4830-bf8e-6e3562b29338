<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">OrderResponse.java</span></div><h1>OrderResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Order;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class OrderResponse {

    private Long id;
    private Map&lt;String, Object&gt; item;
    private Map&lt;String, Object&gt; buyer;
    private Map&lt;String, Object&gt; seller;
    private String status;
    private Boolean isBuyerRated;
    private Boolean isSellerRated;
    private String createdAt;
    private String updatedAt;

<span class="fc" id="L23">    public OrderResponse() {</span>
<span class="fc" id="L24">    }</span>

<span class="fc" id="L26">    public OrderResponse(Order order) {</span>
<span class="fc" id="L27">        this.id = order.getId();</span>
<span class="fc" id="L28">        this.status = order.getStatus().name();</span>
<span class="fc" id="L29">        this.isBuyerRated = order.getIsBuyerRated();</span>
<span class="fc" id="L30">        this.isSellerRated = order.getIsSellerRated();</span>

<span class="fc" id="L32">        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="fc bfc" id="L33" title="All 2 branches covered.">        this.createdAt = order.getCreatedAt() != null ? order.getCreatedAt().format(formatter) : null;</span>
<span class="fc bfc" id="L34" title="All 2 branches covered.">        this.updatedAt = order.getUpdatedAt() != null ? order.getUpdatedAt().format(formatter) : null;</span>

        // 设置商品信息
<span class="fc" id="L37">        this.item = new HashMap&lt;&gt;();</span>
<span class="fc" id="L38">        this.item.put(&quot;id&quot;, order.getItem().getId());</span>
<span class="fc" id="L39">        this.item.put(&quot;name&quot;, order.getItem().getName());</span>
<span class="fc" id="L40">        this.item.put(&quot;description&quot;, order.getItem().getDescription());</span>
<span class="fc" id="L41">        this.item.put(&quot;price&quot;, order.getItem().getPrice());</span>
<span class="fc bfc" id="L42" title="All 2 branches covered.">        if (order.getItem().getCategory() != null) {</span>
<span class="fc" id="L43">            this.item.put(&quot;categoryId&quot;, order.getItem().getCategory().getId());</span>
<span class="fc" id="L44">            this.item.put(&quot;categoryName&quot;, order.getItem().getCategory().getName());</span>
        }
<span class="fc" id="L46">        this.item.put(&quot;condition&quot;, order.getItem().getCondition().name());</span>
<span class="pc bpc" id="L47" title="1 of 2 branches missed.">        if (order.getItem().getImages() != null) {</span>
<span class="fc" id="L48">            this.item.put(&quot;images&quot;, order.getItem().getImages().stream()</span>
<span class="fc" id="L49">                    .map(image -&gt; image.getUrl())</span>
<span class="fc" id="L50">                    .collect(Collectors.toList()));</span>
        }

        // 设置买家信息
<span class="fc" id="L54">        this.buyer = new HashMap&lt;&gt;();</span>
<span class="fc" id="L55">        this.buyer.put(&quot;id&quot;, order.getBuyer().getId());</span>
<span class="fc" id="L56">        this.buyer.put(&quot;username&quot;, order.getBuyer().getUsername());</span>
<span class="fc" id="L57">        this.buyer.put(&quot;avatar&quot;, order.getBuyer().getAvatarUrl());</span>

        // 设置卖家信息
<span class="fc" id="L60">        this.seller = new HashMap&lt;&gt;();</span>
<span class="fc" id="L61">        this.seller.put(&quot;id&quot;, order.getSeller().getId());</span>
<span class="fc" id="L62">        this.seller.put(&quot;username&quot;, order.getSeller().getUsername());</span>
<span class="fc" id="L63">        this.seller.put(&quot;avatar&quot;, order.getSeller().getAvatarUrl());</span>
<span class="fc" id="L64">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L68">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L72">        this.id = id;</span>
<span class="fc" id="L73">    }</span>

    public Map&lt;String, Object&gt; getItem() {
<span class="fc" id="L76">        return item;</span>
    }

    public void setItem(Map&lt;String, Object&gt; item) {
<span class="fc" id="L80">        this.item = item;</span>
<span class="fc" id="L81">    }</span>

    public Map&lt;String, Object&gt; getBuyer() {
<span class="fc" id="L84">        return buyer;</span>
    }

    public void setBuyer(Map&lt;String, Object&gt; buyer) {
<span class="fc" id="L88">        this.buyer = buyer;</span>
<span class="fc" id="L89">    }</span>

    public Map&lt;String, Object&gt; getSeller() {
<span class="fc" id="L92">        return seller;</span>
    }

    public void setSeller(Map&lt;String, Object&gt; seller) {
<span class="fc" id="L96">        this.seller = seller;</span>
<span class="fc" id="L97">    }</span>

    public String getStatus() {
<span class="fc" id="L100">        return status;</span>
    }

    public void setStatus(String status) {
<span class="fc" id="L104">        this.status = status;</span>
<span class="fc" id="L105">    }</span>

    public Boolean getIsBuyerRated() {
<span class="fc" id="L108">        return isBuyerRated;</span>
    }

    public void setIsBuyerRated(Boolean isBuyerRated) {
<span class="fc" id="L112">        this.isBuyerRated = isBuyerRated;</span>
<span class="fc" id="L113">    }</span>

    public Boolean getIsSellerRated() {
<span class="fc" id="L116">        return isSellerRated;</span>
    }

    public void setIsSellerRated(Boolean isSellerRated) {
<span class="fc" id="L120">        this.isSellerRated = isSellerRated;</span>
<span class="fc" id="L121">    }</span>

    public String getCreatedAt() {
<span class="fc" id="L124">        return createdAt;</span>
    }

    public void setCreatedAt(String createdAt) {
<span class="fc" id="L128">        this.createdAt = createdAt;</span>
<span class="fc" id="L129">    }</span>

    public String getUpdatedAt() {
<span class="fc" id="L132">        return updatedAt;</span>
    }

    public void setUpdatedAt(String updatedAt) {
<span class="fc" id="L136">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L137">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>