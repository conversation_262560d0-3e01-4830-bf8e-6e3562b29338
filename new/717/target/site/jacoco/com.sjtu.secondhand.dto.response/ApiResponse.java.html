<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ApiResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">ApiResponse.java</span></div><h1>ApiResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse&lt;T&gt; {

    private boolean success;
    private String code;
    private String message;
    private T data;

    @JsonFormat(pattern = &quot;yyyy-MM-dd'T'HH:mm:ss.SSSSSSS&quot;)
    private LocalDateTime timestamp;

<span class="fc" id="L19">    public ApiResponse() {</span>
<span class="fc" id="L20">        this.timestamp = LocalDateTime.now();</span>
<span class="fc" id="L21">    }</span>

<span class="fc" id="L23">    public ApiResponse(boolean success, String code, String message, T data) {</span>
<span class="fc" id="L24">        this.success = success;</span>
<span class="fc" id="L25">        this.code = code;</span>
<span class="fc" id="L26">        this.message = message;</span>
<span class="fc" id="L27">        this.data = data;</span>
<span class="fc" id="L28">        this.timestamp = LocalDateTime.now();</span>
<span class="fc" id="L29">    }</span>

    public static &lt;T&gt; ApiResponse&lt;T&gt; success(T data) {
<span class="fc" id="L32">        return new ApiResponse&lt;&gt;(true, &quot;SUCCESS&quot;, &quot;操作成功&quot;, data);</span>
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; success(String message, T data) {
<span class="fc" id="L36">        return new ApiResponse&lt;&gt;(true, &quot;SUCCESS&quot;, message, data);</span>
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; error(String message) {
<span class="fc" id="L40">        return new ApiResponse&lt;&gt;(false, &quot;ERROR&quot;, message, null);</span>
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; error(String code, String message) {
<span class="fc" id="L44">        return new ApiResponse&lt;&gt;(false, code, message, null);</span>
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; error(String code, String message, T data) {
<span class="fc" id="L48">        return new ApiResponse&lt;&gt;(false, code, message, data);</span>
    }
    
    public static &lt;T&gt; ApiResponse&lt;T&gt; failure(String message) {
<span class="fc" id="L52">        return new ApiResponse&lt;&gt;(false, &quot;FAILURE&quot;, message, null);</span>
    }

    // 添加一个方法，用于创建符合测试期望格式的响应
    public static ApiResponse&lt;Object&gt; forTest(Object data) {
<span class="fc" id="L57">        return new ApiResponse&lt;&gt;(true, &quot;SUCCESS&quot;, &quot;操作成功&quot;, data);</span>
    }

    public boolean isSuccess() {
<span class="fc" id="L61">        return success;</span>
    }

    public void setSuccess(boolean success) {
<span class="fc" id="L65">        this.success = success;</span>
<span class="fc" id="L66">    }</span>

    public String getCode() {
<span class="fc" id="L69">        return code;</span>
    }

    public void setCode(String code) {
<span class="fc" id="L73">        this.code = code;</span>
<span class="fc" id="L74">    }</span>

    public String getMessage() {
<span class="fc" id="L77">        return message;</span>
    }

    public void setMessage(String message) {
<span class="fc" id="L81">        this.message = message;</span>
<span class="fc" id="L82">    }</span>

    public T getData() {
<span class="fc" id="L85">        return data;</span>
    }

    public void setData(T data) {
<span class="fc" id="L89">        this.data = data;</span>
<span class="fc" id="L90">    }</span>

    public LocalDateTime getTimestamp() {
<span class="fc" id="L93">        return timestamp;</span>
    }

    public void setTimestamp(LocalDateTime timestamp) {
<span class="fc" id="L97">        this.timestamp = timestamp;</span>
<span class="fc" id="L98">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>