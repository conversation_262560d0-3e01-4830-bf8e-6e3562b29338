<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OfferResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">OfferResponse.java</span></div><h1>OfferResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Offer;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class OfferResponse {

    private Long id;
    private Long wantedItemId;
    private String wantedItemName;
    private Map&lt;String, Object&gt; offerer;
    private Map&lt;String, Object&gt; requester;
    private String status;
    private Boolean isOffererRated;
    private Boolean isRequesterRated;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

<span class="fc" id="L22">    public OfferResponse() {</span>
<span class="fc" id="L23">    }</span>

<span class="fc" id="L25">    public OfferResponse(Offer offer) {</span>
<span class="fc" id="L26">        this.id = offer.getId();</span>
<span class="fc" id="L27">        this.wantedItemId = offer.getWantedItem().getId();</span>
<span class="fc" id="L28">        this.wantedItemName = offer.getWantedItem().getName();</span>
<span class="fc" id="L29">        this.status = offer.getStatus().name();</span>
<span class="fc" id="L30">        this.isOffererRated = offer.getIsOffererRated();</span>
<span class="fc" id="L31">        this.isRequesterRated = offer.getIsRequesterRated();</span>
<span class="fc" id="L32">        this.createdAt = offer.getCreatedAt();</span>
<span class="fc" id="L33">        this.updatedAt = offer.getUpdatedAt();</span>

        // 设置响应者信息
<span class="fc" id="L36">        this.offerer = new HashMap&lt;&gt;();</span>
<span class="fc" id="L37">        this.offerer.put(&quot;id&quot;, offer.getOfferer().getId());</span>
<span class="fc" id="L38">        this.offerer.put(&quot;username&quot;, offer.getOfferer().getUsername());</span>
<span class="fc" id="L39">        this.offerer.put(&quot;avatar&quot;, offer.getOfferer().getAvatarUrl());</span>

        // 设置求购者信息
<span class="fc" id="L42">        this.requester = new HashMap&lt;&gt;();</span>
<span class="fc" id="L43">        this.requester.put(&quot;id&quot;, offer.getRequester().getId());</span>
<span class="fc" id="L44">        this.requester.put(&quot;username&quot;, offer.getRequester().getUsername());</span>
<span class="fc" id="L45">        this.requester.put(&quot;avatar&quot;, offer.getRequester().getAvatarUrl());</span>
<span class="fc" id="L46">    }</span>

    public Long getId() {
<span class="fc" id="L49">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L53">        this.id = id;</span>
<span class="fc" id="L54">    }</span>

    public Long getWantedItemId() {
<span class="fc" id="L57">        return wantedItemId;</span>
    }

    public void setWantedItemId(Long wantedItemId) {
<span class="fc" id="L61">        this.wantedItemId = wantedItemId;</span>
<span class="fc" id="L62">    }</span>

    public String getWantedItemName() {
<span class="fc" id="L65">        return wantedItemName;</span>
    }

    public void setWantedItemName(String wantedItemName) {
<span class="fc" id="L69">        this.wantedItemName = wantedItemName;</span>
<span class="fc" id="L70">    }</span>

    public Map&lt;String, Object&gt; getOfferer() {
<span class="fc" id="L73">        return offerer;</span>
    }

    public void setOfferer(Map&lt;String, Object&gt; offerer) {
<span class="fc" id="L77">        this.offerer = offerer;</span>
<span class="fc" id="L78">    }</span>

    public Map&lt;String, Object&gt; getRequester() {
<span class="fc" id="L81">        return requester;</span>
    }

    public void setRequester(Map&lt;String, Object&gt; requester) {
<span class="fc" id="L85">        this.requester = requester;</span>
<span class="fc" id="L86">    }</span>

    public String getStatus() {
<span class="fc" id="L89">        return status;</span>
    }

    public void setStatus(String status) {
<span class="fc" id="L93">        this.status = status;</span>
<span class="fc" id="L94">    }</span>

    public Boolean getIsOffererRated() {
<span class="fc" id="L97">        return isOffererRated;</span>
    }

    public void setIsOffererRated(Boolean offererRated) {
<span class="fc" id="L101">        isOffererRated = offererRated;</span>
<span class="fc" id="L102">    }</span>

    public Boolean getIsRequesterRated() {
<span class="fc" id="L105">        return isRequesterRated;</span>
    }

    public void setIsRequesterRated(Boolean requesterRated) {
<span class="fc" id="L109">        isRequesterRated = requesterRated;</span>
<span class="fc" id="L110">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L113">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L117">        this.createdAt = createdAt;</span>
<span class="fc" id="L118">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L121">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L125">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L126">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>