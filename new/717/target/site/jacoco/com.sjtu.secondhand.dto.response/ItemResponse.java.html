<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">ItemResponse.java</span></div><h1>ItemResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemCondition;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.Item.ItemType;
import com.sjtu.secondhand.model.ItemImage;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ItemResponse {

    private Long id;
    private String name;
    private String title; // 添加title字段，与name保持一致
    private String description;
    private BigDecimal price;
    private BigDecimal priceMin;
    private BigDecimal priceMax;
    private Long categoryId;
    private String categoryName;
    private Map&lt;String, Object&gt; category;
    private ItemCondition condition;
    private ItemType itemType;
    private ItemStatus status;
    private List&lt;String&gt; images;
    private Map&lt;String, Object&gt; seller;
    private Map&lt;String, Object&gt; user;
    private String createdAt;
    private String updatedAt;
    private Integer viewCount;
    private Integer favoriteCount;
    private Boolean isFavorited; // 添加isFavorited字段
    private String viewTime; // 添加浏览时间字段
    private LocalDateTime viewTimeRaw; // 添加原始浏览时间字段，用于排序

    // Constructors
<span class="fc" id="L45">    public ItemResponse() {</span>
<span class="fc" id="L46">        this.isFavorited = false; // 默认设置为false</span>
<span class="fc" id="L47">    }</span>

<span class="fc" id="L49">    public ItemResponse(Item item) {</span>
<span class="pc bpc" id="L50" title="1 of 2 branches missed.">        if (item != null) {</span>
<span class="fc" id="L51">            System.out.println(</span>
<span class="fc" id="L52">                    &quot;【收藏量调试】ItemResponse构造函数 - 物品ID: &quot; + item.getId() + &quot;, 数据库中的收藏量: &quot; + item.getFavoriteCount());</span>

<span class="fc" id="L54">            this.id = item.getId();</span>
<span class="fc" id="L55">            this.name = item.getName();</span>
<span class="fc" id="L56">            this.title = item.getName(); // 设置title与name一致</span>
<span class="fc" id="L57">            this.description = item.getDescription();</span>
<span class="fc" id="L58">            this.price = item.getPrice();</span>
<span class="fc" id="L59">            this.priceMin = item.getPriceMin();</span>
<span class="fc" id="L60">            this.priceMax = item.getPriceMax();</span>
<span class="fc bfc" id="L61" title="All 2 branches covered.">            if (item.getCategory() != null) {</span>
<span class="fc" id="L62">                this.categoryId = item.getCategory().getId().longValue();</span>
<span class="fc" id="L63">                this.categoryName = item.getCategory().getName();</span>

<span class="fc" id="L65">                Map&lt;String, Object&gt; categoryMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L66">                categoryMap.put(&quot;id&quot;, item.getCategory().getId());</span>
<span class="fc" id="L67">                categoryMap.put(&quot;name&quot;, item.getCategory().getName());</span>
<span class="fc" id="L68">                this.category = categoryMap;</span>
            }
<span class="fc" id="L70">            this.condition = item.getCondition();</span>
<span class="fc" id="L71">            this.itemType = item.getItemType();</span>
<span class="fc" id="L72">            this.status = item.getStatus();</span>

            // 转换图片列表
<span class="pc bpc" id="L75" title="1 of 2 branches missed.">            if (item.getImages() != null) {</span>
<span class="fc" id="L76">                this.images = item.getImages().stream()</span>
<span class="fc" id="L77">                        .map(ItemImage::getUrl)</span>
<span class="fc" id="L78">                        .collect(Collectors.toList());</span>
            } else {
<span class="nc" id="L80">                this.images = new ArrayList&lt;&gt;();</span>
            }

            // 设置用户信息
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">            if (item.getUser() != null) {</span>
<span class="fc" id="L85">                Map&lt;String, Object&gt; userMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L86">                userMap.put(&quot;id&quot;, item.getUser().getId());</span>
<span class="fc" id="L87">                userMap.put(&quot;username&quot;, item.getUser().getUsername());</span>
<span class="fc" id="L88">                userMap.put(&quot;avatar&quot;, item.getUser().getAvatarUrl());</span>
<span class="fc" id="L89">                userMap.put(&quot;rating&quot;, item.getUser().getRating());</span>
<span class="fc" id="L90">                this.user = userMap;</span>

                // 为了兼容性，同时设置seller字段，确保包含avatar属性
<span class="fc" id="L93">                Map&lt;String, Object&gt; sellerMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L94">                sellerMap.put(&quot;id&quot;, item.getUser().getId());</span>
<span class="fc" id="L95">                sellerMap.put(&quot;username&quot;, item.getUser().getUsername());</span>
<span class="fc" id="L96">                sellerMap.put(&quot;avatar&quot;, item.getUser().getAvatarUrl()); // 确保包含avatar属性</span>
<span class="fc" id="L97">                sellerMap.put(&quot;rating&quot;, item.getUser().getRating());</span>
<span class="fc" id="L98">                this.seller = sellerMap;</span>
            }

<span class="fc" id="L101">            this.viewCount = item.getViewCount();</span>
<span class="fc" id="L102">            this.favoriteCount = item.getFavoriteCount();</span>

            // 初始化收藏状态为false，稍后会由专门的方法设置正确的值
<span class="fc" id="L105">            this.isFavorited = false;</span>

<span class="fc" id="L107">            System.out.println(&quot;【收藏量调试】ItemResponse构造函数 - 设置收藏量为: &quot; + this.favoriteCount);</span>

<span class="fc" id="L109">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="fc bfc" id="L110" title="All 2 branches covered.">            this.createdAt = item.getCreatedAt() != null ? item.getCreatedAt().format(formatter) : null;</span>
<span class="fc bfc" id="L111" title="All 2 branches covered.">            this.updatedAt = item.getUpdatedAt() != null ? item.getUpdatedAt().format(formatter) : null;</span>
        }
<span class="fc" id="L113">    }</span>

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L117">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L121">        this.id = id;</span>
<span class="fc" id="L122">    }</span>

    public String getName() {
<span class="fc" id="L125">        return name;</span>
    }

    public void setName(String name) {
<span class="fc" id="L129">        this.name = name;</span>
<span class="fc" id="L130">    }</span>

    public String getDescription() {
<span class="fc" id="L133">        return description;</span>
    }

    public void setDescription(String description) {
<span class="fc" id="L137">        this.description = description;</span>
<span class="fc" id="L138">    }</span>

    public BigDecimal getPrice() {
<span class="fc" id="L141">        return price;</span>
    }

    public void setPrice(BigDecimal price) {
<span class="fc" id="L145">        this.price = price;</span>
<span class="fc" id="L146">    }</span>

    public BigDecimal getPriceMin() {
<span class="fc" id="L149">        return priceMin;</span>
    }

    public void setPriceMin(BigDecimal priceMin) {
<span class="fc" id="L153">        this.priceMin = priceMin;</span>
<span class="fc" id="L154">    }</span>

    public BigDecimal getPriceMax() {
<span class="fc" id="L157">        return priceMax;</span>
    }

    public void setPriceMax(BigDecimal priceMax) {
<span class="fc" id="L161">        this.priceMax = priceMax;</span>
<span class="fc" id="L162">    }</span>

    public Long getCategoryId() {
<span class="fc" id="L165">        return categoryId;</span>
    }

    public void setCategoryId(Long categoryId) {
<span class="nc" id="L169">        this.categoryId = categoryId;</span>
<span class="nc" id="L170">    }</span>

    public String getCategoryName() {
<span class="fc" id="L173">        return categoryName;</span>
    }

    public void setCategoryName(String categoryName) {
<span class="nc" id="L177">        this.categoryName = categoryName;</span>
<span class="nc" id="L178">    }</span>

    public Map&lt;String, Object&gt; getCategory() {
<span class="fc" id="L181">        return category;</span>
    }

    public void setCategory(Map&lt;String, Object&gt; category) {
<span class="fc" id="L185">        this.category = category;</span>
<span class="fc" id="L186">    }</span>

    public ItemCondition getCondition() {
<span class="fc" id="L189">        return condition;</span>
    }

    public void setCondition(ItemCondition condition) {
<span class="fc" id="L193">        this.condition = condition;</span>
<span class="fc" id="L194">    }</span>

    public ItemType getItemType() {
<span class="fc" id="L197">        return itemType;</span>
    }

    public void setItemType(ItemType itemType) {
<span class="fc" id="L201">        this.itemType = itemType;</span>
<span class="fc" id="L202">    }</span>

    public ItemStatus getStatus() {
<span class="fc" id="L205">        return status;</span>
    }

    public void setStatus(ItemStatus status) {
<span class="fc" id="L209">        this.status = status;</span>
<span class="fc" id="L210">    }</span>

    public List&lt;String&gt; getImages() {
<span class="fc" id="L213">        return images;</span>
    }

    public void setImages(List&lt;String&gt; images) {
<span class="fc" id="L217">        this.images = images;</span>
<span class="fc" id="L218">    }</span>

    public Map&lt;String, Object&gt; getSeller() {
<span class="fc" id="L221">        return seller;</span>
    }

    public void setSeller(Map&lt;String, Object&gt; seller) {
<span class="fc" id="L225">        this.seller = seller;</span>
<span class="fc" id="L226">    }</span>

    public Map&lt;String, Object&gt; getUser() {
<span class="fc" id="L229">        return user;</span>
    }

    public void setUser(Map&lt;String, Object&gt; user) {
<span class="fc" id="L233">        this.user = user;</span>
<span class="fc" id="L234">    }</span>

    public String getCreatedAt() {
<span class="fc" id="L237">        return createdAt;</span>
    }

    public void setCreatedAt(String createdAt) {
<span class="nc" id="L241">        this.createdAt = createdAt;</span>
<span class="nc" id="L242">    }</span>

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc bfc" id="L245" title="All 2 branches covered.">        if (createdAt != null) {</span>
<span class="fc" id="L246">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="fc" id="L247">            this.createdAt = createdAt.format(formatter);</span>
<span class="fc" id="L248">        } else {</span>
<span class="fc" id="L249">            this.createdAt = null;</span>
        }
<span class="fc" id="L251">    }</span>

    public String getUpdatedAt() {
<span class="fc" id="L254">        return updatedAt;</span>
    }

    public void setUpdatedAt(String updatedAt) {
<span class="nc" id="L258">        this.updatedAt = updatedAt;</span>
<span class="nc" id="L259">    }</span>

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc bfc" id="L262" title="All 2 branches covered.">        if (updatedAt != null) {</span>
<span class="fc" id="L263">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="fc" id="L264">            this.updatedAt = updatedAt.format(formatter);</span>
<span class="fc" id="L265">        } else {</span>
<span class="fc" id="L266">            this.updatedAt = null;</span>
        }
<span class="fc" id="L268">    }</span>

    public Integer getViewCount() {
<span class="fc" id="L271">        return viewCount;</span>
    }

    public void setViewCount(Integer viewCount) {
<span class="fc" id="L275">        this.viewCount = viewCount;</span>
<span class="fc" id="L276">    }</span>

    public Integer getFavoriteCount() {
<span class="fc" id="L279">        return favoriteCount;</span>
    }

    public void setFavoriteCount(Integer favoriteCount) {
<span class="fc" id="L283">        this.favoriteCount = favoriteCount;</span>
<span class="fc" id="L284">    }</span>

    // 添加title的getter和setter
    public String getTitle() {
<span class="fc" id="L288">        return title;</span>
    }

    public void setTitle(String title) {
<span class="fc" id="L292">        this.title = title;</span>
<span class="fc" id="L293">    }</span>

    // 添加isFavorited的getter和setter
    public Boolean getIsFavorited() {
<span class="fc" id="L297">        return isFavorited;</span>
    }

    public void setIsFavorited(Boolean isFavorited) {
<span class="fc" id="L301">        this.isFavorited = isFavorited;</span>
<span class="fc" id="L302">    }</span>

    // 新增 viewTime 相关的 getter 和 setter
    public String getViewTime() {
<span class="fc" id="L306">        return viewTime;</span>
    }

    public void setViewTime(String viewTime) {
<span class="fc" id="L310">        this.viewTime = viewTime;</span>
<span class="fc" id="L311">    }</span>

    public void setViewTime(LocalDateTime viewTime) {
<span class="fc bfc" id="L314" title="All 2 branches covered.">        if (viewTime != null) {</span>
<span class="fc" id="L315">            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;);</span>
<span class="fc" id="L316">            this.viewTime = viewTime.format(formatter);</span>
<span class="fc" id="L317">            this.viewTimeRaw = viewTime;</span>
<span class="fc" id="L318">        } else {</span>
<span class="fc" id="L319">            this.viewTime = null;</span>
<span class="fc" id="L320">            this.viewTimeRaw = null;</span>
        }
<span class="fc" id="L322">    }</span>

    public LocalDateTime getViewTimeRaw() {
<span class="fc" id="L325">        return viewTimeRaw;</span>
    }

    public void setViewTimeRaw(LocalDateTime viewTimeRaw) {
<span class="nc" id="L329">        this.viewTimeRaw = viewTimeRaw;</span>
<span class="nc" id="L330">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>