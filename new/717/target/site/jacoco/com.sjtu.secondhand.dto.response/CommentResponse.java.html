<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CommentResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">CommentResponse.java</span></div><h1>CommentResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.model.User;

import java.time.LocalDateTime;
import java.util.List;

public class CommentResponse {
    private Long id;
    
    @JsonProperty(&quot;user&quot;)
    private UserResponse userResponse;
    
    private String content;
    
    @JsonProperty(&quot;parent_id&quot;)
    private Long parentId;
    
    @JsonProperty(&quot;created_at&quot;)
    private LocalDateTime createdAt;
    
    private List&lt;CommentResponse&gt; replies;

<span class="fc" id="L26">    public CommentResponse() {</span>
<span class="fc" id="L27">    }</span>

<span class="fc" id="L29">    public CommentResponse(Comment comment) {</span>
<span class="fc" id="L30">        this.id = comment.getId();</span>
<span class="fc" id="L31">        this.content = comment.getContent();</span>
<span class="fc bfc" id="L32" title="All 2 branches covered.">        this.parentId = comment.getParent() != null ? comment.getParent().getId() : null;</span>
<span class="fc" id="L33">        this.createdAt = comment.getCreatedAt();</span>
        
<span class="pc bpc" id="L35" title="1 of 2 branches missed.">        if (comment.getUser() != null) {</span>
<span class="fc" id="L36">            User user = comment.getUser();</span>
<span class="fc" id="L37">            this.userResponse = new UserResponse(user);</span>
        }
<span class="fc" id="L39">    }</span>

    // Getters and setters
    public Long getId() {
<span class="fc" id="L43">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L47">        this.id = id;</span>
<span class="fc" id="L48">    }</span>

    public UserResponse getUserResponse() {
<span class="fc" id="L51">        return userResponse;</span>
    }

    public void setUserResponse(UserResponse userResponse) {
<span class="fc" id="L55">        this.userResponse = userResponse;</span>
<span class="fc" id="L56">    }</span>

    public String getContent() {
<span class="fc" id="L59">        return content;</span>
    }

    public void setContent(String content) {
<span class="fc" id="L63">        this.content = content;</span>
<span class="fc" id="L64">    }</span>

    public Long getParentId() {
<span class="fc" id="L67">        return parentId;</span>
    }

    public void setParentId(Long parentId) {
<span class="fc" id="L71">        this.parentId = parentId;</span>
<span class="fc" id="L72">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L75">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L79">        this.createdAt = createdAt;</span>
<span class="fc" id="L80">    }</span>

    public List&lt;CommentResponse&gt; getReplies() {
<span class="fc" id="L83">        return replies;</span>
    }

    public void setReplies(List&lt;CommentResponse&gt; replies) {
<span class="fc" id="L87">        this.replies = replies;</span>
<span class="fc" id="L88">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>