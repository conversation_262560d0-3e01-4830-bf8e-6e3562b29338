<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">UserResponse.java</span></div><h1>UserResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sjtu.secondhand.model.User;

/**
 * 用户公开信息响应类
 */
public class UserResponse {
    private Long id;
    
    private String username;
    
    @JsonProperty(&quot;avatar_url&quot;)
    private String avatarUrl;
    
    @JsonProperty(&quot;credit_score&quot;)
    private Integer creditScore;

<span class="fc" id="L20">    public UserResponse() {</span>
<span class="fc" id="L21">    }</span>

<span class="fc" id="L23">    public UserResponse(User user) {</span>
<span class="fc" id="L24">        this.id = user.getId();</span>
<span class="fc" id="L25">        this.username = user.getUsername();</span>
<span class="fc" id="L26">        this.avatarUrl = user.getAvatarUrl();</span>
<span class="fc" id="L27">        this.creditScore = user.getCreditScore();</span>
<span class="fc" id="L28">    }</span>

    // Getters and setters
    public Long getId() {
<span class="fc" id="L32">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L36">        this.id = id;</span>
<span class="fc" id="L37">    }</span>

    public String getUsername() {
<span class="fc" id="L40">        return username;</span>
    }

    public void setUsername(String username) {
<span class="fc" id="L44">        this.username = username;</span>
<span class="fc" id="L45">    }</span>

    public String getAvatarUrl() {
<span class="fc" id="L48">        return avatarUrl;</span>
    }

    public void setAvatarUrl(String avatarUrl) {
<span class="fc" id="L52">        this.avatarUrl = avatarUrl;</span>
<span class="fc" id="L53">    }</span>

    public Integer getCreditScore() {
<span class="fc" id="L56">        return creditScore;</span>
    }

    public void setCreditScore(Integer creditScore) {
<span class="fc" id="L60">        this.creditScore = creditScore;</span>
<span class="fc" id="L61">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>