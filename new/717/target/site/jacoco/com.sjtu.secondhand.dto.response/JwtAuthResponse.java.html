<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtAuthResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.dto.response</a> &gt; <span class="el_source">JwtAuthResponse.java</span></div><h1>JwtAuthResponse.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class JwtAuthResponse {
    @JsonProperty(&quot;token&quot;)
    private String token;
    
<span class="fc" id="L9">    @JsonProperty(&quot;token_type&quot;)</span>
    private String tokenType = &quot;Bearer&quot;;
    
    @JsonProperty(&quot;id&quot;)
    private Long id;
    
    @JsonProperty(&quot;username&quot;)
    private String username;
    
    @JsonProperty(&quot;email&quot;)
    private String email;
    
    @JsonProperty(&quot;avatar_url&quot;)
    private String avatar;

<span class="fc" id="L24">    public JwtAuthResponse() {</span>
<span class="fc" id="L25">    }</span>

<span class="fc" id="L27">    public JwtAuthResponse(String token, Long id, String username) {</span>
<span class="fc" id="L28">        this.token = token;</span>
<span class="fc" id="L29">        this.id = id;</span>
<span class="fc" id="L30">        this.username = username;</span>
<span class="fc" id="L31">    }</span>

<span class="fc" id="L33">    public JwtAuthResponse(String token, String tokenType, Long id, String username) {</span>
<span class="fc" id="L34">        this.token = token;</span>
<span class="fc" id="L35">        this.tokenType = tokenType;</span>
<span class="fc" id="L36">        this.id = id;</span>
<span class="fc" id="L37">        this.username = username;</span>
<span class="fc" id="L38">    }</span>

    public String getToken() {
<span class="fc" id="L41">        return token;</span>
    }

    public void setToken(String token) {
<span class="fc" id="L45">        this.token = token;</span>
<span class="fc" id="L46">    }</span>

    public String getTokenType() {
<span class="fc" id="L49">        return tokenType;</span>
    }

    public void setTokenType(String tokenType) {
<span class="fc" id="L53">        this.tokenType = tokenType;</span>
<span class="fc" id="L54">    }</span>

    public Long getId() {
<span class="fc" id="L57">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L61">        this.id = id;</span>
<span class="fc" id="L62">    }</span>

    public String getUsername() {
<span class="fc" id="L65">        return username;</span>
    }

    public void setUsername(String username) {
<span class="fc" id="L69">        this.username = username;</span>
<span class="fc" id="L70">    }</span>

    public String getEmail() {
<span class="fc" id="L73">        return email;</span>
    }

    public void setEmail(String email) {
<span class="fc" id="L77">        this.email = email;</span>
<span class="fc" id="L78">    }</span>

    public String getAvatar() {
<span class="fc" id="L81">        return avatar;</span>
    }

    public void setAvatar(String avatar) {
<span class="fc" id="L85">        this.avatar = avatar;</span>
<span class="fc" id="L86">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>