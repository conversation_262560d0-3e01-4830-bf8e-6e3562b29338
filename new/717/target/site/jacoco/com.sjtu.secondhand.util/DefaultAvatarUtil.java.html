<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultAvatarUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.util</a> &gt; <span class="el_source">DefaultAvatarUtil.java</span></div><h1>DefaultAvatarUtil.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.util;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 默认头像工具类
 * 用于在用户注册时随机分配默认头像
 */
<span class="fc" id="L11">public class DefaultAvatarUtil {</span>

    // 默认头像URL列表
<span class="fc" id="L14">    private static final List&lt;String&gt; DEFAULT_AVATAR_URLS = Arrays.asList(</span>
            &quot;https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/alice.png&quot;,
            &quot;https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/lily.png&quot;,
            &quot;https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/bob.png&quot;,
            &quot;https://shareu-youyu.oss-cn-shanghai.aliyuncs.com/general/kevin.png&quot;
    );

<span class="fc" id="L21">    private static final Random random = new Random();</span>

    /**
     * 获取随机默认头像URL
     * 
     * @return 随机选择的默认头像URL
     */
    public static String getRandomDefaultAvatarUrl() {
<span class="fc" id="L29">        int index = random.nextInt(DEFAULT_AVATAR_URLS.size());</span>
<span class="fc" id="L30">        return DEFAULT_AVATAR_URLS.get(index);</span>
    }
    
    /**
     * 根据用户名获取默认头像URL
     * 使用用户名的哈希值来确定头像，确保同一用户名总是获得同一头像
     * 
     * @param username 用户名
     * @return 基于用户名确定的默认头像URL
     */
    public static String getDefaultAvatarUrlByUsername(String username) {
<span class="fc" id="L41">        int index = Math.abs(username.hashCode() % DEFAULT_AVATAR_URLS.size());</span>
<span class="fc" id="L42">        return DEFAULT_AVATAR_URLS.get(index);</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>