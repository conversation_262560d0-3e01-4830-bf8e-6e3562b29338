<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemDocument.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.model.es</a> &gt; <span class="el_source">ItemDocument.java</span></div><h1>ItemDocument.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.model.es;

import com.sjtu.secondhand.model.Item;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Document(indexName = &quot;items&quot;)
@Setting(
        settingPath = &quot;es-settings.json&quot;
)
<span class="fc" id="L18">public class ItemDocument {</span>

    @Id
    private Long id;

    @Field(type = FieldType.Text, analyzer = &quot;ik_max_word_synonym&quot;, searchAnalyzer = &quot;ik_smart_synonym&quot;)
    private String name;

    @Field(type = FieldType.Text, analyzer = &quot;ik_max_word_synonym&quot;, searchAnalyzer = &quot;ik_smart_synonym&quot;)
    private String description;

    @Field(type = FieldType.Double)
    private BigDecimal price;

    @Field(type = FieldType.Double)
    private BigDecimal priceMin;

    @Field(type = FieldType.Double)
    private BigDecimal priceMax;

    @Field(type = FieldType.Keyword)
    private String itemType;

    @Field(type = FieldType.Keyword)
    private String condition;

    @Field(type = FieldType.Keyword)
    private String status;

    @Field(type = FieldType.Boolean)
    private Boolean isVisible;

    @Field(type = FieldType.Integer)
    private Integer viewCount;

    @Field(type = FieldType.Integer)
    private Integer favoriteCount;

    @Field(type = FieldType.Object)
    private CategoryInfo category;

    @Field(type = FieldType.Object)
    private UserInfo user;

    @Field(type = FieldType.Date)
    private LocalDateTime createdAt;

    @Field(type = FieldType.Date)
    private LocalDateTime updatedAt;

    @Field(type = FieldType.Keyword)
    private List&lt;String&gt; imageUrls;

    // 内部静态类，用于存储分类信息
<span class="fc" id="L72">    public static class CategoryInfo {</span>
        private Long id;
        private String name;

        public Long getId() {
<span class="fc" id="L77">            return id;</span>
        }

        public void setId(Long id) {
<span class="fc" id="L81">            this.id = id;</span>
<span class="fc" id="L82">        }</span>

        public String getName() {
<span class="fc" id="L85">            return name;</span>
        }

        public void setName(String name) {
<span class="fc" id="L89">            this.name = name;</span>
<span class="fc" id="L90">        }</span>
    }

    // 内部静态类，用于存储用户信息
<span class="fc" id="L94">    public static class UserInfo {</span>
        private Long id;
        private String username;
        private String avatarUrl;
        private Double rating;

        public Long getId() {
<span class="fc" id="L101">            return id;</span>
        }

        public void setId(Long id) {
<span class="fc" id="L105">            this.id = id;</span>
<span class="fc" id="L106">        }</span>

        public String getUsername() {
<span class="fc" id="L109">            return username;</span>
        }

        public void setUsername(String username) {
<span class="fc" id="L113">            this.username = username;</span>
<span class="fc" id="L114">        }</span>

        public String getAvatarUrl() {
<span class="fc" id="L117">            return avatarUrl;</span>
        }

        public void setAvatarUrl(String avatarUrl) {
<span class="fc" id="L121">            this.avatarUrl = avatarUrl;</span>
<span class="fc" id="L122">        }</span>

        public Double getRating() {
<span class="fc" id="L125">            return rating;</span>
        }

        public void setRating(Double rating) {
<span class="fc" id="L129">            this.rating = rating;</span>
<span class="fc" id="L130">        }</span>
    }

    // 从Item实体转换为ES文档
    public static ItemDocument fromItem(Item item) {
<span class="fc" id="L135">        ItemDocument document = new ItemDocument();</span>
<span class="fc" id="L136">        document.setId(item.getId());</span>
<span class="fc" id="L137">        document.setName(item.getName());</span>
<span class="fc" id="L138">        document.setDescription(item.getDescription());</span>
<span class="fc" id="L139">        document.setPrice(item.getPrice());</span>
<span class="fc" id="L140">        document.setPriceMin(item.getPriceMin());</span>
<span class="fc" id="L141">        document.setPriceMax(item.getPriceMax());</span>
<span class="fc bfc" id="L142" title="All 2 branches covered.">        document.setItemType(item.getItemType() != null ? item.getItemType().name() : null);</span>
<span class="fc bfc" id="L143" title="All 2 branches covered.">        document.setCondition(item.getCondition() != null ? item.getCondition().name() : null);</span>
<span class="fc bfc" id="L144" title="All 2 branches covered.">        document.setStatus(item.getStatus() != null ? item.getStatus().name() : null);</span>
<span class="fc" id="L145">        document.setIsVisible(item.getIsVisible());</span>
<span class="fc" id="L146">        document.setViewCount(item.getViewCount());</span>
<span class="fc" id="L147">        document.setFavoriteCount(item.getFavoriteCount());</span>
<span class="fc" id="L148">        document.setCreatedAt(item.getCreatedAt());</span>
<span class="fc" id="L149">        document.setUpdatedAt(item.getUpdatedAt());</span>

        // 设置分类信息
<span class="fc bfc" id="L152" title="All 2 branches covered.">        if (item.getCategory() != null) {</span>
<span class="fc" id="L153">            CategoryInfo categoryInfo = new CategoryInfo();</span>
<span class="fc" id="L154">            categoryInfo.setId(Long.valueOf(item.getCategory().getId()));</span>
<span class="fc" id="L155">            categoryInfo.setName(item.getCategory().getName());</span>
<span class="fc" id="L156">            document.setCategory(categoryInfo);</span>
        }

        // 设置用户信息
<span class="fc bfc" id="L160" title="All 2 branches covered.">        if (item.getUser() != null) {</span>
<span class="fc" id="L161">            UserInfo userInfo = new UserInfo();</span>
<span class="fc" id="L162">            userInfo.setId(item.getUser().getId());</span>
<span class="fc" id="L163">            userInfo.setUsername(item.getUser().getUsername());</span>
<span class="fc" id="L164">            userInfo.setAvatarUrl(item.getUser().getAvatarUrl());</span>
<span class="fc" id="L165">            userInfo.setRating(Double.valueOf(item.getUser().getRating()));</span>
<span class="fc" id="L166">            document.setUser(userInfo);</span>
        }

        // 设置图片URL
<span class="fc bfc" id="L170" title="All 4 branches covered.">        if (item.getImages() != null &amp;&amp; !item.getImages().isEmpty()) {</span>
<span class="fc" id="L171">            document.setImageUrls(item.getImages().stream()</span>
<span class="fc" id="L172">                    .map(img -&gt; img.getUrl())</span>
<span class="fc" id="L173">                    .toList());</span>
        }

<span class="fc" id="L176">        return document;</span>
    }

    // Getters and Setters
    public Long getId() {
<span class="fc" id="L181">        return id;</span>
    }

    public void setId(Long id) {
<span class="fc" id="L185">        this.id = id;</span>
<span class="fc" id="L186">    }</span>

    public String getName() {
<span class="fc" id="L189">        return name;</span>
    }

    public void setName(String name) {
<span class="fc" id="L193">        this.name = name;</span>
<span class="fc" id="L194">    }</span>

    public String getDescription() {
<span class="fc" id="L197">        return description;</span>
    }

    public void setDescription(String description) {
<span class="fc" id="L201">        this.description = description;</span>
<span class="fc" id="L202">    }</span>

    public BigDecimal getPrice() {
<span class="fc" id="L205">        return price;</span>
    }

    public void setPrice(BigDecimal price) {
<span class="fc" id="L209">        this.price = price;</span>
<span class="fc" id="L210">    }</span>

    public BigDecimal getPriceMin() {
<span class="fc" id="L213">        return priceMin;</span>
    }

    public void setPriceMin(BigDecimal priceMin) {
<span class="fc" id="L217">        this.priceMin = priceMin;</span>
<span class="fc" id="L218">    }</span>

    public BigDecimal getPriceMax() {
<span class="fc" id="L221">        return priceMax;</span>
    }

    public void setPriceMax(BigDecimal priceMax) {
<span class="fc" id="L225">        this.priceMax = priceMax;</span>
<span class="fc" id="L226">    }</span>

    public String getItemType() {
<span class="fc" id="L229">        return itemType;</span>
    }

    public void setItemType(String itemType) {
<span class="fc" id="L233">        this.itemType = itemType;</span>
<span class="fc" id="L234">    }</span>

    public String getCondition() {
<span class="fc" id="L237">        return condition;</span>
    }

    public void setCondition(String condition) {
<span class="fc" id="L241">        this.condition = condition;</span>
<span class="fc" id="L242">    }</span>

    public String getStatus() {
<span class="fc" id="L245">        return status;</span>
    }

    public void setStatus(String status) {
<span class="fc" id="L249">        this.status = status;</span>
<span class="fc" id="L250">    }</span>

    public Boolean getIsVisible() {
<span class="fc" id="L253">        return isVisible;</span>
    }

    public void setIsVisible(Boolean isVisible) {
<span class="fc" id="L257">        this.isVisible = isVisible;</span>
<span class="fc" id="L258">    }</span>

    public Integer getViewCount() {
<span class="fc" id="L261">        return viewCount;</span>
    }

    public void setViewCount(Integer viewCount) {
<span class="fc" id="L265">        this.viewCount = viewCount;</span>
<span class="fc" id="L266">    }</span>

    public Integer getFavoriteCount() {
<span class="fc" id="L269">        return favoriteCount;</span>
    }

    public void setFavoriteCount(Integer favoriteCount) {
<span class="fc" id="L273">        this.favoriteCount = favoriteCount;</span>
<span class="fc" id="L274">    }</span>

    public CategoryInfo getCategory() {
<span class="fc" id="L277">        return category;</span>
    }

    public void setCategory(CategoryInfo category) {
<span class="fc" id="L281">        this.category = category;</span>
<span class="fc" id="L282">    }</span>

    public UserInfo getUser() {
<span class="fc" id="L285">        return user;</span>
    }

    public void setUser(UserInfo user) {
<span class="fc" id="L289">        this.user = user;</span>
<span class="fc" id="L290">    }</span>

    public LocalDateTime getCreatedAt() {
<span class="fc" id="L293">        return createdAt;</span>
    }

    public void setCreatedAt(LocalDateTime createdAt) {
<span class="fc" id="L297">        this.createdAt = createdAt;</span>
<span class="fc" id="L298">    }</span>

    public LocalDateTime getUpdatedAt() {
<span class="fc" id="L301">        return updatedAt;</span>
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
<span class="fc" id="L305">        this.updatedAt = updatedAt;</span>
<span class="fc" id="L306">    }</span>

    public List&lt;String&gt; getImageUrls() {
<span class="fc" id="L309">        return imageUrls;</span>
    }

    public void setImageUrls(List&lt;String&gt; imageUrls) {
<span class="fc" id="L313">        this.imageUrls = imageUrls;</span>
<span class="fc" id="L314">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>