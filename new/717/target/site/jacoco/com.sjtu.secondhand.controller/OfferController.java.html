<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OfferController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">OfferController.java</span></div><h1>OfferController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.service.OfferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Tag(name = &quot;求购响应 (Offers)&quot;, description = &quot;求购响应相关接口&quot;)
public class OfferController {

    private final OfferService offerService;

<span class="fc" id="L23">    public OfferController(OfferService offerService) {</span>
<span class="fc" id="L24">        this.offerService = offerService;</span>
<span class="fc" id="L25">    }</span>

    @PostMapping(&quot;/items/{wanted_item_id}/offers&quot;)
    @Operation(summary = &quot;响应求购贴 (提交Offer)&quot;, description = &quot;有物者对一个 'WANTED' 类型的物品（求购贴）进行响应，创建一个Offer。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OfferResponse&gt;&gt; createOffer(
            @PathVariable(&quot;wanted_item_id&quot;) Long wantedItemId,
            @Valid @RequestBody OfferRequest offerRequest) {
        // 直接将求购物品ID传递给服务层
<span class="fc" id="L35">        OfferResponse offerResponse = offerService.createOffer(wantedItemId, offerRequest);</span>
<span class="fc" id="L36">        return new ResponseEntity&lt;&gt;(ApiResponse.success(&quot;Offer提交成功&quot;, offerResponse), HttpStatus.CREATED);</span>
    }

    @PostMapping(&quot;/offers/{id}/accept&quot;)
    @Operation(summary = &quot;[求购者] 接受Offer&quot;, description = &quot;求购者操作。接受某个Offer，将 'PENDING_ACCEPTANCE' 状态的Offer流转至 'ACCEPTED'，同时物品状态变为 'RESERVED'。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OfferResponse&gt;&gt; acceptOffer(@PathVariable(&quot;id&quot;) Long offerId) {
<span class="fc" id="L44">        OfferResponse offerResponse = offerService.acceptOffer(offerId);</span>
<span class="fc" id="L45">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, offerResponse));</span>
    }

    @PostMapping(&quot;/offers/{id}/reject&quot;)
    @Operation(summary = &quot;[求购者] 拒绝Offer&quot;, description = &quot;求购者操作。拒绝此Offer，状态流转至 'REJECTED'。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OfferResponse&gt;&gt; rejectOffer(@PathVariable(&quot;id&quot;) Long offerId) {
<span class="fc" id="L53">        OfferResponse offerResponse = offerService.rejectOffer(offerId);</span>
<span class="fc" id="L54">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, offerResponse));</span>
    }

    @PostMapping(&quot;/offers/{id}/confirm&quot;)
    @Operation(summary = &quot;[响应者] 确认交易&quot;, description = &quot;响应者（有物者）操作。在求购者接受后进行最终确认，状态流转至 'CONFIRMED'。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OfferResponse&gt;&gt; confirmOffer(@PathVariable(&quot;id&quot;) Long offerId) {
<span class="fc" id="L62">        OfferResponse offerResponse = offerService.confirmOffer(offerId);</span>
<span class="fc" id="L63">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, offerResponse));</span>
    }

    @PostMapping(&quot;/offers/{id}/cancel&quot;)
    @Operation(summary = &quot;[求购/响应者] 取消Offer&quot;, description = &quot;在交易完成前取消Offer，状态流转至 'CANCELLED'。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;OfferResponse&gt;&gt; cancelOffer(@PathVariable(&quot;id&quot;) Long offerId) {
<span class="fc" id="L71">        OfferResponse offerResponse = offerService.cancelOffer(offerId);</span>
<span class="fc" id="L72">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功&quot;, offerResponse));</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>