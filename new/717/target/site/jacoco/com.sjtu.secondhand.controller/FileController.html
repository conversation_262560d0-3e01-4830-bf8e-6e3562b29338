<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_class">FileController</span></div><h1>FileController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">8 of 128</td><td class="ctr2">93%</td><td class="bar">1 of 6</td><td class="ctr2">83%</td><td class="ctr1">1</td><td class="ctr2">8</td><td class="ctr1">2</td><td class="ctr2">38</td><td class="ctr1">0</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a0"><a href="FileController.java.html#L92" class="el_method">downloadFile(String, HttpServletRequest)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="94" height="10" title="30" alt="30"/></td><td class="ctr2" id="c4">78%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">11</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="FileController.java.html#L66" class="el_method">uploadMultipleImages(MultipartFile[])</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="116" height="10" title="37" alt="37"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="FileController.java.html#L45" class="el_method">uploadImage(MultipartFile)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="101" height="10" title="32" alt="32"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="FileController.java.html#L76" class="el_method">lambda$uploadMultipleImages$0(MultipartFile)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="12" alt="12"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">5</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="FileController.java.html#L36" class="el_method">FileController(FileStorageService, OssStorageService)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="9" alt="9"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>