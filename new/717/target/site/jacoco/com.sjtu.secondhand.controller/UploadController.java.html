<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UploadController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">UploadController.java</span></div><h1>UploadController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.CORSConfiguration;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.OssStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping(&quot;/upload&quot;)
@Tag(name = &quot;文件上传 (Upload)&quot;, description = &quot;文件上传相关接口&quot;)
public class UploadController {

<span class="fc" id="L30">    private static final Logger logger = LoggerFactory.getLogger(UploadController.class);</span>

    private final FileStorageService fileStorageService;
    private final OssStorageService ossStorageService;
    
    @Autowired
    private OSS ossClient;

    @Value(&quot;${server.servlet.context-path}&quot;)
    private String contextPath;

    @Value(&quot;${file.upload-dir:uploads}&quot;)
    private String uploadDir;

    @Value(&quot;${storage.type:local}&quot;)
    private String storageType;
    
    @Value(&quot;${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}&quot;)
    private String allowedOrigins;
    
    @Value(&quot;${aliyun.oss.bucket-name:shareu-youyu}&quot;)
    private String bucketName;
    
    @Value(&quot;${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}&quot;)
    private String endpoint;

<span class="fc" id="L56">    public UploadController(FileStorageService fileStorageService, OssStorageService ossStorageService) {</span>
<span class="fc" id="L57">        this.fileStorageService = fileStorageService;</span>
<span class="fc" id="L58">        this.ossStorageService = ossStorageService;</span>
<span class="fc" id="L59">    }</span>

    @GetMapping(&quot;/signature&quot;)
    @Operation(summary = &quot;获取上传签名&quot;, description = &quot;获取用于直传的签名URL&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Map&lt;String, String&gt;&gt;&gt; getUploadSignature(
            @RequestParam(&quot;filename&quot;) String filename,
            @RequestParam(&quot;content_type&quot;) String contentType) {

<span class="fc" id="L68">        logger.info(&quot;获取上传签名请求，文件名: {}, 内容类型: {}, 存储类型: {}&quot;, filename, contentType, storageType);</span>

<span class="fc bfc" id="L70" title="All 2 branches covered.">        if (&quot;oss&quot;.equals(storageType)) {</span>
            try {
                // 使用OSS生成预签名URL
<span class="fc" id="L73">                Map&lt;String, String&gt; signatureData = ossStorageService.generatePresignedUrl(filename, contentType);</span>
                
                // 检查上传URL是否存在
<span class="fc bfc" id="L76" title="All 2 branches covered.">                if (signatureData.get(&quot;upload_url&quot;) == null) {</span>
<span class="fc" id="L77">                    logger.error(&quot;生成的上传URL为空&quot;);</span>
<span class="fc" id="L78">                    return ResponseEntity.ok(ApiResponse.error(&quot;SIGNATURE_ERROR&quot;, &quot;生成的上传URL为空&quot;, null));</span>
                }
                
<span class="fc" id="L81">                logger.info(&quot;OSS签名生成成功，上传URL: {}&quot;, signatureData.get(&quot;upload_url&quot;));</span>
                
                // 为前端提供额外信息，帮助调试
<span class="fc" id="L84">                signatureData.put(&quot;current_origin&quot;, allowedOrigins);</span>
                
<span class="fc" id="L86">                return ResponseEntity.ok(ApiResponse.success(&quot;签名生成成功&quot;, signatureData));</span>
<span class="fc" id="L87">            } catch (Exception e) {</span>
<span class="fc" id="L88">                logger.error(&quot;生成OSS签名失败&quot;, e);</span>
<span class="fc" id="L89">                return ResponseEntity.ok(ApiResponse.error(&quot;SIGNATURE_ERROR&quot;, &quot;生成OSS签名失败: &quot; + e.getMessage(), null));</span>
            }
        } else {
            // 本地存储模式，返回上传端点
<span class="fc" id="L93">            Map&lt;String, String&gt; result = new HashMap&lt;&gt;();</span>
<span class="fc" id="L94">            result.put(&quot;upload_url&quot;, contextPath + &quot;/upload/file&quot;);</span>
<span class="fc" id="L95">            result.put(&quot;method&quot;, &quot;POST&quot;);</span>
<span class="fc" id="L96">            return ResponseEntity.ok(ApiResponse.success(&quot;签名生成成功&quot;, result));</span>
        }
    }

    @PostMapping(&quot;/file&quot;)
    @Operation(summary = &quot;上传文件&quot;, description = &quot;上传文件到服务器&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; uploadFile(@RequestParam(&quot;file&quot;) MultipartFile file) {
<span class="fc bfc" id="L104" title="All 2 branches covered.">        if (&quot;oss&quot;.equals(storageType)) {</span>
            // 使用OSS存储
<span class="fc" id="L106">            String fileUrl = ossStorageService.uploadFile(file);</span>
<span class="fc" id="L107">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileUrl));</span>
        } else {
            // 使用本地存储
<span class="fc" id="L110">            String fileName = fileStorageService.storeFile(file);</span>
<span class="fc" id="L111">            String fileDownloadUri = contextPath + &quot;/upload/files/&quot; + fileName;</span>
<span class="fc" id="L112">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileDownloadUri));</span>
        }
    }

    @DeleteMapping(&quot;/file&quot;)
    @Operation(summary = &quot;删除文件&quot;, description = &quot;删除已上传的文件&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Boolean&gt;&gt; deleteFile(@RequestParam(&quot;objectName&quot;) String objectName) {
<span class="fc bfc" id="L120" title="All 2 branches covered.">        if (&quot;oss&quot;.equals(storageType)) {</span>
            // 使用OSS存储
<span class="fc" id="L122">            boolean deleted = ossStorageService.deleteFile(objectName);</span>
<span class="pc bpc" id="L123" title="1 of 2 branches missed.">            return ResponseEntity.ok(ApiResponse.success(&quot;文件删除&quot; + (deleted ? &quot;成功&quot; : &quot;失败，文件不存在&quot;), deleted));</span>
        } else {
            // 使用本地存储
<span class="fc" id="L126">            boolean deleted = fileStorageService.deleteFile(objectName);</span>
<span class="fc bfc" id="L127" title="All 2 branches covered.">            return ResponseEntity.ok(ApiResponse.success(&quot;文件删除&quot; + (deleted ? &quot;成功&quot; : &quot;失败，文件不存在&quot;), deleted));</span>
        }
    }
    
    /**
     * 获取OSS配置信息，用于诊断
     */
    @GetMapping(&quot;/oss-info&quot;)
    @Operation(summary = &quot;获取OSS配置信息&quot;, description = &quot;获取OSS配置信息，包括CORS设置&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Map&lt;String, Object&gt;&gt;&gt; getOssInfo() {
<span class="fc bfc" id="L138" title="All 2 branches covered.">        if (!&quot;oss&quot;.equals(storageType)) {</span>
<span class="fc" id="L139">            return ResponseEntity.ok(ApiResponse.error(&quot;NOT_OSS&quot;, &quot;当前存储类型不是OSS&quot;, null));</span>
        }
        
        try {
<span class="fc" id="L143">            Map&lt;String, Object&gt; info = new HashMap&lt;&gt;();</span>
<span class="fc" id="L144">            info.put(&quot;bucketName&quot;, bucketName);</span>
<span class="fc" id="L145">            info.put(&quot;endpoint&quot;, endpoint);</span>
<span class="fc" id="L146">            info.put(&quot;allowedOrigins&quot;, allowedOrigins);</span>
            
            // 获取CORS配置
<span class="nc" id="L149">            List&lt;SetBucketCORSRequest.CORSRule&gt; corsRules = ossClient.getBucketCORSRules(bucketName);</span>
<span class="nc" id="L150">            List&lt;Map&lt;String, Object&gt;&gt; corsRulesList = new ArrayList&lt;&gt;();</span>
            
<span class="nc bnc" id="L152" title="All 2 branches missed.">            if (corsRules != null) {</span>
<span class="nc bnc" id="L153" title="All 2 branches missed.">                for (SetBucketCORSRequest.CORSRule rule : corsRules) {</span>
<span class="nc" id="L154">                    Map&lt;String, Object&gt; ruleMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L155">                    ruleMap.put(&quot;allowedOrigins&quot;, rule.getAllowedOrigins());</span>
<span class="nc" id="L156">                    ruleMap.put(&quot;allowedMethods&quot;, rule.getAllowedMethods());</span>
<span class="nc" id="L157">                    ruleMap.put(&quot;allowedHeaders&quot;, rule.getAllowedHeaders());</span>
<span class="nc" id="L158">                    ruleMap.put(&quot;exposeHeaders&quot;, rule.getExposeHeaders());</span>
<span class="nc" id="L159">                    ruleMap.put(&quot;maxAgeSeconds&quot;, rule.getMaxAgeSeconds());</span>
<span class="nc" id="L160">                    corsRulesList.add(ruleMap);</span>
<span class="nc" id="L161">                }</span>
            }
            
<span class="nc" id="L164">            info.put(&quot;corsRules&quot;, corsRulesList);</span>
            
<span class="nc" id="L166">            return ResponseEntity.ok(ApiResponse.success(&quot;获取OSS配置信息成功&quot;, info));</span>
<span class="fc" id="L167">        } catch (Exception e) {</span>
<span class="fc" id="L168">            logger.error(&quot;获取OSS配置信息失败&quot;, e);</span>
<span class="fc" id="L169">            return ResponseEntity.ok(ApiResponse.error(&quot;OSS_INFO_ERROR&quot;, &quot;获取OSS配置信息失败: &quot; + e.getMessage(), null));</span>
        }
    }
    
    /**
     * 更新OSS CORS配置
     */
    @PostMapping(&quot;/update-cors&quot;)
    @Operation(summary = &quot;更新OSS CORS配置&quot;, description = &quot;更新OSS CORS配置&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Boolean&gt;&gt; updateOssCors() {
<span class="fc bfc" id="L180" title="All 2 branches covered.">        if (!&quot;oss&quot;.equals(storageType)) {</span>
<span class="fc" id="L181">            return ResponseEntity.ok(ApiResponse.error(&quot;NOT_OSS&quot;, &quot;当前存储类型不是OSS&quot;, null));</span>
        }
        
        try {
            // 创建CORS规则
<span class="fc" id="L186">            SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);</span>
            
            // 创建一个CORS规则
<span class="fc" id="L189">            SetBucketCORSRequest.CORSRule rule = new SetBucketCORSRequest.CORSRule();</span>
            
            // 设置允许的来源
<span class="fc" id="L192">            List&lt;String&gt; allowedOriginsList = Arrays.asList(allowedOrigins.split(&quot;,&quot;));</span>
<span class="fc" id="L193">            rule.setAllowedOrigins(allowedOriginsList);</span>
            
            // 设置允许的方法
<span class="fc" id="L196">            List&lt;String&gt; allowedMethods = Arrays.asList(&quot;GET&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;POST&quot;, &quot;HEAD&quot;);</span>
<span class="fc" id="L197">            rule.setAllowedMethods(allowedMethods);</span>
            
            // 设置允许的头
<span class="fc" id="L200">            List&lt;String&gt; allowedHeaders = Arrays.asList(&quot;*&quot;);</span>
<span class="fc" id="L201">            rule.setAllowedHeaders(allowedHeaders);</span>
            
            // 设置暴露的头
<span class="fc" id="L204">            List&lt;String&gt; exposeHeaders = Arrays.asList(&quot;ETag&quot;, &quot;x-oss-request-id&quot;);</span>
<span class="fc" id="L205">            rule.setExposeHeaders(exposeHeaders);</span>
            
            // 设置最大缓存时间
<span class="fc" id="L208">            rule.setMaxAgeSeconds(3600);</span>
            
            // 添加规则到请求
<span class="fc" id="L211">            request.setCorsRules(new ArrayList&lt;&gt;(Collections.singletonList(rule)));</span>
            
            // 设置CORS规则
<span class="nc" id="L214">            ossClient.setBucketCORS(request);</span>
            
<span class="nc" id="L216">            logger.info(&quot;OSS CORS规则更新成功&quot;);</span>
<span class="nc" id="L217">            return ResponseEntity.ok(ApiResponse.success(&quot;OSS CORS规则更新成功&quot;, true));</span>
<span class="fc" id="L218">        } catch (Exception e) {</span>
<span class="fc" id="L219">            logger.error(&quot;更新OSS CORS规则失败&quot;, e);</span>
<span class="fc" id="L220">            return ResponseEntity.ok(ApiResponse.error(&quot;UPDATE_CORS_ERROR&quot;, &quot;更新OSS CORS规则失败: &quot; + e.getMessage(), null));</span>
        }
    }
    
    /**
     * 请求头诊断端点
     */
    @GetMapping(&quot;/request-info&quot;)
    @Operation(summary = &quot;获取请求头信息&quot;, description = &quot;获取当前请求的头信息，用于诊断&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Map&lt;String, Object&gt;&gt;&gt; getRequestInfo(HttpServletRequest request) {
<span class="fc" id="L231">        Map&lt;String, Object&gt; info = new HashMap&lt;&gt;();</span>
        
        // 收集请求头信息
<span class="fc" id="L234">        Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();</span>
<span class="fc" id="L235">        Enumeration&lt;String&gt; headerNames = request.getHeaderNames();</span>
<span class="fc bfc" id="L236" title="All 2 branches covered.">        while (headerNames.hasMoreElements()) {</span>
<span class="fc" id="L237">            String headerName = headerNames.nextElement();</span>
<span class="fc" id="L238">            headers.put(headerName, request.getHeader(headerName));</span>
<span class="fc" id="L239">        }</span>
<span class="fc" id="L240">        info.put(&quot;headers&quot;, headers);</span>
        
        // 收集请求信息
<span class="fc" id="L243">        info.put(&quot;method&quot;, request.getMethod());</span>
<span class="fc" id="L244">        info.put(&quot;requestURI&quot;, request.getRequestURI());</span>
<span class="fc" id="L245">        info.put(&quot;queryString&quot;, request.getQueryString());</span>
<span class="fc" id="L246">        info.put(&quot;remoteAddr&quot;, request.getRemoteAddr());</span>
<span class="fc" id="L247">        info.put(&quot;remoteHost&quot;, request.getRemoteHost());</span>
<span class="fc" id="L248">        info.put(&quot;remotePort&quot;, request.getRemotePort());</span>
<span class="fc" id="L249">        info.put(&quot;localAddr&quot;, request.getLocalAddr());</span>
<span class="fc" id="L250">        info.put(&quot;localPort&quot;, request.getLocalPort());</span>
<span class="fc" id="L251">        info.put(&quot;serverName&quot;, request.getServerName());</span>
<span class="fc" id="L252">        info.put(&quot;serverPort&quot;, request.getServerPort());</span>
<span class="fc" id="L253">        info.put(&quot;scheme&quot;, request.getScheme());</span>
        
<span class="fc" id="L255">        return ResponseEntity.ok(ApiResponse.success(&quot;获取请求信息成功&quot;, info));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>