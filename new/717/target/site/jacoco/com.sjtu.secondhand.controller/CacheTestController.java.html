<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheTestController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">CacheTestController.java</span></div><h1>CacheTestController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存测试控制器
 * 仅用于测试环境，提供Redis缓存状态查询接口
 */
@RestController
@RequestMapping(&quot;/cache-test&quot;)
public class CacheTestController {

    private final CacheManager cacheManager;
    private final RedisTemplate&lt;String, Object&gt; redisTemplate;

    @Autowired
<span class="fc" id="L27">    public CacheTestController(CacheManager cacheManager, RedisTemplate&lt;String, Object&gt; redisTemplate) {</span>
<span class="fc" id="L28">        this.cacheManager = cacheManager;</span>
<span class="fc" id="L29">        this.redisTemplate = redisTemplate;</span>
<span class="fc" id="L30">    }</span>

    /**
     * 获取缓存状态
     * 
     * @return 缓存状态信息
     */
    @GetMapping(&quot;/status&quot;)
    public ApiResponse&lt;Map&lt;String, Object&gt;&gt; getCacheStatus() {
<span class="fc" id="L39">        Map&lt;String, Object&gt; status = new HashMap&lt;&gt;();</span>

        // 检查Redis连接
<span class="fc" id="L42">        boolean redisConnected = false;</span>
        try {
<span class="fc bfc" id="L44" title="All 2 branches covered.">            redisConnected = redisTemplate.getConnectionFactory().getConnection().ping() != null;</span>
<span class="fc" id="L45">            status.put(&quot;redisConnected&quot;, redisConnected);</span>
<span class="fc" id="L46">        } catch (Exception e) {</span>
<span class="fc" id="L47">            status.put(&quot;redisConnected&quot;, false);</span>
<span class="fc" id="L48">            status.put(&quot;redisError&quot;, e.getMessage());</span>
<span class="fc" id="L49">        }</span>

        // 获取缓存名称列表
<span class="fc bfc" id="L52" title="All 2 branches covered.">        if (redisConnected) {</span>
            try {
<span class="fc" id="L54">                Set&lt;String&gt; cacheNames = redisTemplate.keys(&quot;*&quot;);</span>
<span class="fc" id="L55">                status.put(&quot;cacheNames&quot;, cacheNames);</span>

                // 获取物品缓存的键
<span class="fc" id="L58">                Set&lt;String&gt; itemCacheKeys = redisTemplate.keys(&quot;items::*&quot;);</span>
<span class="fc" id="L59">                status.put(&quot;itemCacheKeys&quot;, itemCacheKeys);</span>

                // 获取物品缓存数量
<span class="fc bfc" id="L62" title="All 2 branches covered.">                status.put(&quot;itemCacheCount&quot;, itemCacheKeys != null ? itemCacheKeys.size() : 0);</span>
<span class="fc" id="L63">            } catch (Exception e) {</span>
<span class="fc" id="L64">                status.put(&quot;keysError&quot;, e.getMessage());</span>
<span class="fc" id="L65">            }</span>
        }

<span class="fc" id="L68">        return ApiResponse.success(&quot;缓存状态信息&quot;, status);</span>
    }

    /**
     * 清除所有缓存
     * 
     * @return 操作结果
     */
    @GetMapping(&quot;/clear&quot;)
    public ApiResponse&lt;Void&gt; clearAllCaches() {
        try {
            // 清除所有缓存
<span class="fc" id="L80">            Set&lt;String&gt; cacheNames = redisTemplate.keys(&quot;*&quot;);</span>
<span class="fc bfc" id="L81" title="All 2 branches covered.">            if (cacheNames != null) {</span>
<span class="fc bfc" id="L82" title="All 2 branches covered.">                for (String name : cacheNames) {</span>
<span class="fc" id="L83">                    redisTemplate.delete(name);</span>
<span class="fc" id="L84">                }</span>
            }
<span class="fc" id="L86">            return ApiResponse.success(&quot;所有缓存已清除&quot;, null);</span>
<span class="fc" id="L87">        } catch (Exception e) {</span>
<span class="fc" id="L88">            return ApiResponse.error(&quot;清除缓存失败: &quot; + e.getMessage());</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>