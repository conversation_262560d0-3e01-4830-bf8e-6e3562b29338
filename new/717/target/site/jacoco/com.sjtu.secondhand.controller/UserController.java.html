<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">UserController.java</span></div><h1>UserController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.request.UserUpdateRequest;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(&quot;/users&quot;)
@Tag(name = &quot;用户&quot;, description = &quot;用户相关接口&quot;)
@SecurityRequirement(name = &quot;JWT&quot;)
public class UserController {

    private final UserService userService;
    private final ItemService itemService;

<span class="fc" id="L29">    public UserController(UserService userService, ItemService itemService) {</span>
<span class="fc" id="L30">        this.userService = userService;</span>
<span class="fc" id="L31">        this.itemService = itemService;</span>
<span class="fc" id="L32">    }</span>

    @GetMapping(&quot;/me&quot;)
    @Operation(summary = &quot;获取当前用户信息&quot;, description = &quot;获取当前登录用户的详细信息&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; getCurrentUser() {
<span class="fc" id="L37">        User user = userService.getCurrentUser();</span>
        // 不返回密码
<span class="fc" id="L39">        user.setPassword(null);</span>
        // 包装成前端期望的格式，添加兼容性属性
<span class="fc" id="L41">        return ResponseEntity.ok(ApiResponse.success(&quot;获取用户信息成功&quot;, Map.of(</span>
                &quot;user&quot;, user)));
    }

    @GetMapping(&quot;/{id}&quot;)
    @Operation(summary = &quot;获取用户信息&quot;, description = &quot;根据用户ID获取用户信息&quot;)
    public ResponseEntity&lt;ApiResponse&lt;User&gt;&gt; getUserById(@PathVariable Long id) {
<span class="fc" id="L48">        User user = userService.getUserById(id);</span>
        // 不返回密码
<span class="fc" id="L50">        user.setPassword(null);</span>
<span class="fc" id="L51">        return ResponseEntity.ok(ApiResponse.success(user));</span>
    }

    @PutMapping(&quot;/me&quot;)
    @Operation(summary = &quot;更新用户信息&quot;, description = &quot;更新当前登录用户的信息&quot;)
    public ResponseEntity&lt;ApiResponse&lt;User&gt;&gt; updateUser(@RequestBody UserUpdateRequest userUpdateRequest) {
<span class="fc" id="L57">        User currentUser = userService.getCurrentUser();</span>

<span class="fc bfc" id="L59" title="All 2 branches covered.">        if (userUpdateRequest.getUsername() != null) {</span>
<span class="fc" id="L60">            currentUser.setUsername(userUpdateRequest.getUsername());</span>
        }

<span class="fc bfc" id="L63" title="All 2 branches covered.">        if (userUpdateRequest.getAvatar_url() != null) {</span>
<span class="fc" id="L64">            currentUser.setAvatarUrl(userUpdateRequest.getAvatar_url());</span>
        }

<span class="fc" id="L67">        User updatedUser = userService.saveUser(currentUser);</span>
        // 不返回密码
<span class="fc" id="L69">        updatedUser.setPassword(null);</span>
<span class="fc" id="L70">        return ResponseEntity.ok(ApiResponse.success(&quot;用户信息更新成功&quot;, updatedUser));</span>
    }

    @PostMapping(&quot;/me/check-in&quot;)
    @Operation(summary = &quot;每日签到&quot;, description = &quot;用户每日签到以获取平台活跃度积分&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; checkIn() {
<span class="fc" id="L76">        User user = userService.getCurrentUser();</span>
<span class="fc" id="L77">        LocalDate today = LocalDate.now();</span>

        // 检查用户是否已经签到
<span class="fc bfc" id="L80" title="All 4 branches covered.">        if (user.getLastCheckInDate() != null &amp;&amp; user.getLastCheckInDate().equals(today)) {</span>
<span class="fc" id="L81">            return ResponseEntity.status(409).body(ApiResponse.error(&quot;CONFLICT&quot;, &quot;今日已签到&quot;));</span>
        }

        // 更新签到日期和积分
<span class="fc" id="L85">        user.setLastCheckInDate(today);</span>

        // 假设每次签到获得10积分
<span class="fc bfc" id="L88" title="All 2 branches covered.">        Integer currentPoints = user.getPoints() != null ? user.getPoints() : 0;</span>
<span class="fc" id="L89">        user.setPoints(currentPoints + 10);</span>

<span class="fc" id="L91">        userService.saveUser(user);</span>

<span class="fc" id="L93">        return ResponseEntity.ok(ApiResponse.success(&quot;签到成功，获得10积分&quot;, null));</span>
    }

    @GetMapping(&quot;/me/favorites&quot;)
    @Operation(summary = &quot;获取当前用户的收藏列表&quot;, description = &quot;获取当前登录用户收藏的所有物品列表，支持分页&quot;)
    public ResponseEntity&lt;ApiResponse&lt;ItemPageResponse&gt;&gt; getFavorites(
            @RequestParam(value = &quot;page&quot;, defaultValue = &quot;1&quot;) int page,
            @RequestParam(value = &quot;size&quot;, defaultValue = &quot;20&quot;) int size) {

        // 获取当前用户
<span class="fc" id="L103">        User currentUser = userService.getCurrentUser();</span>

        // 获取收藏列表
<span class="fc" id="L106">        List&lt;ItemResponse&gt; favoriteItems = itemService.getFavoriteItems(currentUser.getId());</span>

        // 创建分页响应对象
        // 这里简化处理分页，实际上可能需要更复杂的分页逻辑
<span class="fc" id="L110">        int totalItems = favoriteItems.size();</span>
<span class="fc" id="L111">        int totalPages = (int) Math.ceil((double) totalItems / size);</span>

        // 调整页码，API文档从1开始，而Java代码从0开始
<span class="pc bpc" id="L114" title="1 of 2 branches missed.">        page = page &gt; 0 ? page - 1 : 0;</span>

        // 计算当前页的内容
<span class="fc" id="L117">        int start = Math.min(page * size, totalItems);</span>
<span class="fc" id="L118">        int end = Math.min(start + size, totalItems);</span>
<span class="fc" id="L119">        List&lt;ItemResponse&gt; pageContent = favoriteItems.subList(start, end);</span>

        // 创建分页响应
<span class="fc" id="L122">        ItemPageResponse response = new ItemPageResponse();</span>
<span class="fc" id="L123">        response.setContent(pageContent);</span>
<span class="fc" id="L124">        response.setPage(page + 1);</span>
<span class="fc" id="L125">        response.setTotalElements(totalItems);</span>
<span class="fc" id="L126">        response.setTotalPages(totalPages);</span>

<span class="fc" id="L128">        return ResponseEntity.ok(ApiResponse.success(&quot;获取收藏列表成功&quot;, response));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>