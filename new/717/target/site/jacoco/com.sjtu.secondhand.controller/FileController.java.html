<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">FileController.java</span></div><h1>FileController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.service.FileStorageService;
import com.sjtu.secondhand.service.OssStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(&quot;/upload&quot;)
@Tag(name = &quot;文件上传&quot;, description = &quot;文件上传相关接口&quot;)
public class FileController {

    private final FileStorageService fileStorageService;
    private final OssStorageService ossStorageService;

    @Value(&quot;${storage.type:local}&quot;)
    private String storageType;

<span class="fc" id="L36">    public FileController(FileStorageService fileStorageService, OssStorageService ossStorageService) {</span>
<span class="fc" id="L37">        this.fileStorageService = fileStorageService;</span>
<span class="fc" id="L38">        this.ossStorageService = ossStorageService;</span>
<span class="fc" id="L39">    }</span>

    @PostMapping(&quot;/image&quot;)
    @Operation(summary = &quot;上传图片&quot;, description = &quot;上传商品图片&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; uploadImage(@RequestParam(&quot;file&quot;) MultipartFile file) {
<span class="fc bfc" id="L45" title="All 2 branches covered.">        if (&quot;oss&quot;.equals(storageType)) {</span>
            // 使用OSS存储
<span class="fc" id="L47">            String fileUrl = ossStorageService.uploadFile(file);</span>
<span class="fc" id="L48">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileUrl));</span>
        } else {
            // 使用本地存储
<span class="fc" id="L51">            String fileName = fileStorageService.storeFile(file);</span>
<span class="fc" id="L52">            String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()</span>
<span class="fc" id="L53">                    .path(&quot;/upload/files/&quot;)</span>
<span class="fc" id="L54">                    .path(fileName)</span>
<span class="fc" id="L55">                    .toUriString();</span>
<span class="fc" id="L56">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileDownloadUri));</span>
        }
    }

    @PostMapping(&quot;/images&quot;)
    @Operation(summary = &quot;批量上传图片&quot;, description = &quot;批量上传商品图片&quot;, security = @SecurityRequirement(name = &quot;JWT&quot;))
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;String&gt;&gt;&gt; uploadMultipleImages(
            @RequestParam(&quot;files&quot;) MultipartFile[] files) {
        
<span class="fc bfc" id="L66" title="All 2 branches covered.">        if (&quot;oss&quot;.equals(storageType)) {</span>
            // 使用OSS存储
<span class="fc" id="L68">            List&lt;String&gt; fileUrls = Arrays.stream(files)</span>
<span class="fc" id="L69">                    .map(ossStorageService::uploadFile)</span>
<span class="fc" id="L70">                    .collect(Collectors.toList());</span>
<span class="fc" id="L71">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileUrls));</span>
        } else {
            // 使用本地存储
<span class="fc" id="L74">            List&lt;String&gt; fileDownloadUrls = Arrays.stream(files)</span>
<span class="fc" id="L75">                    .map(file -&gt; {</span>
<span class="fc" id="L76">                        String fileName = fileStorageService.storeFile(file);</span>
<span class="fc" id="L77">                        return ServletUriComponentsBuilder.fromCurrentContextPath()</span>
<span class="fc" id="L78">                                .path(&quot;/upload/files/&quot;)</span>
<span class="fc" id="L79">                                .path(fileName)</span>
<span class="fc" id="L80">                                .toUriString();</span>
                    })
<span class="fc" id="L82">                    .collect(Collectors.toList());</span>
<span class="fc" id="L83">            return ResponseEntity.ok(ApiResponse.success(&quot;文件上传成功&quot;, fileDownloadUrls));</span>
        }
    }

    @GetMapping(&quot;/files/{fileName:.+}&quot;)
    @Operation(summary = &quot;下载文件&quot;, description = &quot;下载已上传的文件&quot;)
    public ResponseEntity&lt;Resource&gt; downloadFile(@PathVariable String fileName, HttpServletRequest request) {
        // 对于OSS存储方式，应该直接重定向到OSS的URL，不需要通过服务器下载
        // 这里保留原有逻辑，只处理本地存储的文件
<span class="fc" id="L92">        Resource resource = fileStorageService.loadFileAsResource(fileName);</span>

<span class="fc" id="L94">        String contentType = null;</span>
        try {
<span class="nc" id="L96">            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());</span>
<span class="fc" id="L97">        } catch (IOException ex) {</span>
            // 默认为二进制流
<span class="nc" id="L99">        }</span>

<span class="pc bpc" id="L101" title="1 of 2 branches missed.">        if (contentType == null) {</span>
<span class="fc" id="L102">            contentType = &quot;application/octet-stream&quot;;</span>
        }

<span class="fc" id="L105">        return ResponseEntity.ok()</span>
<span class="fc" id="L106">                .contentType(MediaType.parseMediaType(contentType))</span>
<span class="fc" id="L107">                .header(HttpHeaders.CONTENT_DISPOSITION, &quot;attachment; filename=\&quot;&quot; + resource.getFilename() + &quot;\&quot;&quot;)</span>
<span class="fc" id="L108">                .body(resource);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>