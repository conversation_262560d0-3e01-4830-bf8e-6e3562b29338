<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EventStreamController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">EventStreamController.java</span></div><h1>EventStreamController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.security.JwtTokenProvider;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.UserService;
import com.sjtu.secondhand.service.impl.NotificationEventServiceImpl;

import io.jsonwebtoken.JwtException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(&quot;/events&quot;)
@Tag(name = &quot;事件流 (Event Stream)&quot;, description = &quot;Server-Sent Events (SSE) 实时通知接口&quot;)
public class EventStreamController {

<span class="fc" id="L34">    private static final Logger logger = LoggerFactory.getLogger(EventStreamController.class);</span>
    private final UserService userService;
    private final NotificationEventServiceImpl notificationEventService;
    private final JwtTokenProvider jwtTokenProvider;
    private final UserDetailsService userDetailsService;

    // SSE连接超时设置，2小时
<span class="fc" id="L41">    private static final long SSE_TIMEOUT_MS = TimeUnit.HOURS.toMillis(2);</span>

    @Autowired
    public EventStreamController(UserService userService,
            NotificationEventServiceImpl notificationEventService,
            JwtTokenProvider jwtTokenProvider,
<span class="fc" id="L47">            UserDetailsService userDetailsService) {</span>
<span class="fc" id="L48">        this.userService = userService;</span>
<span class="fc" id="L49">        this.notificationEventService = notificationEventService;</span>
<span class="fc" id="L50">        this.jwtTokenProvider = jwtTokenProvider;</span>
<span class="fc" id="L51">        this.userDetailsService = userDetailsService;</span>
<span class="fc" id="L52">    }</span>

    /**
     * 创建SSE连接，为当前登录用户建立实时通知流
     * 支持从URL参数和Authorization头获取token
     * 
     * @param token JWT token (可选，如果不提供则从Authorization头获取)
     * @return SseEmitter - 服务器发送事件发射器
     */
    @GetMapping(path = &quot;/stream&quot;, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = &quot;订阅实时通知&quot;, description = &quot;创建一个Server-Sent Events连接，接收实时通知和订单更新&quot;)
    public SseEmitter stream(@RequestParam(required = false) String token) {
<span class="fc" id="L64">        User currentUser = null;</span>

        try {
            // 首先尝试获取当前认证的用户
<span class="fc" id="L68">            currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L69">        } catch (Exception e) {</span>
            // 如果从SecurityContext获取失败，尝试从URL参数token获取
<span class="fc bfc" id="L71" title="All 4 branches covered.">            if (token != null &amp;&amp; !token.isEmpty()) {</span>
                try {
<span class="fc bfc" id="L73" title="All 2 branches covered.">                    if (jwtTokenProvider.validateToken(token)) {</span>
<span class="fc" id="L74">                        String username = jwtTokenProvider.getUsername(token);</span>
<span class="fc" id="L75">                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);</span>
<span class="fc" id="L76">                        Long userId = jwtTokenProvider.getUserId(token);</span>
<span class="fc" id="L77">                        currentUser = userService.getUserById(userId);</span>
<span class="fc" id="L78">                        logger.info(&quot;通过URL参数token获取用户: {}&quot;, username);</span>
                    }
<span class="nc" id="L80">                } catch (JwtException e2) {</span>
<span class="nc" id="L81">                    logger.error(&quot;JWT Token验证失败: {}&quot;, e2.getMessage());</span>
<span class="fc" id="L82">                }</span>
            }
<span class="fc" id="L84">        }</span>

        // 如果仍然获取不到用户，抛出异常
<span class="fc bfc" id="L87" title="All 2 branches covered.">        if (currentUser == null) {</span>
<span class="fc" id="L88">            logger.warn(&quot;未认证的用户尝试创建SSE连接&quot;);</span>
<span class="fc" id="L89">            throw new IllegalStateException(&quot;用户未认证&quot;);</span>
        }

<span class="fc" id="L92">        Long userId = currentUser.getId();</span>
<span class="fc" id="L93">        logger.info(&quot;用户 {} 创建了SSE连接&quot;, userId);</span>

        // 创建SSE发射器，设置超时时间
<span class="fc" id="L96">        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT_MS);</span>

        // 注册到通知事件服务
<span class="fc" id="L99">        notificationEventService.addEmitter(userId, emitter);</span>

        // 发送初始事件表示连接成功
        try {
<span class="fc" id="L103">            emitter.send(SseEmitter.event()</span>
<span class="fc" id="L104">                    .name(&quot;connect&quot;)</span>
<span class="fc" id="L105">                    .data(&quot;{\&quot;status\&quot;:\&quot;connected\&quot;,\&quot;userId\&quot;:&quot; + userId + &quot;,\&quot;timestamp\&quot;:&quot;</span>
<span class="fc" id="L106">                            + System.currentTimeMillis() + &quot;}&quot;));</span>
<span class="nc" id="L107">        } catch (Exception e) {</span>
<span class="nc" id="L108">            logger.error(&quot;发送SSE连接确认事件失败: {}&quot;, e.getMessage());</span>
<span class="fc" id="L109">        }</span>

<span class="fc" id="L111">        return emitter;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>