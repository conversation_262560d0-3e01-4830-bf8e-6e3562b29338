<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NotificationController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">NotificationController.java</span></div><h1>NotificationController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.NotificationDto;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(&quot;/notifications&quot;)
public class NotificationController {

    private final NotificationService notificationService;
    private final UserRepository userRepository;

    @Autowired
<span class="fc" id="L24">    public NotificationController(NotificationService notificationService, UserRepository userRepository) {</span>
<span class="fc" id="L25">        this.notificationService = notificationService;</span>
<span class="fc" id="L26">        this.userRepository = userRepository;</span>
<span class="fc" id="L27">    }</span>

    @GetMapping
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;List&lt;NotificationDto&gt;&gt; getAllNotifications() {
<span class="fc" id="L32">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L33">        List&lt;NotificationDto&gt; notifications = notificationService.getAllNotifications(userId);</span>
<span class="fc" id="L34">        return ResponseEntity.ok(notifications);</span>
    }

    @GetMapping(&quot;/unread&quot;)
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;List&lt;NotificationDto&gt;&gt; getUnreadNotifications() {
<span class="fc" id="L40">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L41">        List&lt;NotificationDto&gt; notifications = notificationService.getUnreadNotifications(userId);</span>
<span class="fc" id="L42">        return ResponseEntity.ok(notifications);</span>
    }

    @GetMapping(&quot;/count&quot;)
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;Long&gt; countUnreadNotifications() {
<span class="fc" id="L48">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L49">        long count = notificationService.countUnreadNotifications(userId);</span>
<span class="fc" id="L50">        return ResponseEntity.ok(count);</span>
    }

    @PutMapping(&quot;/{id}/read&quot;)
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;NotificationDto&gt; markAsRead(@PathVariable(&quot;id&quot;) Long id) {
<span class="fc" id="L56">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L57">        NotificationDto notification = notificationService.markAsRead(id, userId);</span>
<span class="fc" id="L58">        return ResponseEntity.ok(notification);</span>
    }

    @PutMapping(&quot;/read-all&quot;)
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; markAllAsRead() {
<span class="fc" id="L64">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L65">        notificationService.markAllAsRead(userId);</span>
<span class="fc" id="L66">        return ResponseEntity.ok(ApiResponse.success(&quot;所有通知已标记为已读&quot;, null));</span>
    }

    @DeleteMapping(&quot;/{id}&quot;)
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; deleteNotification(@PathVariable(&quot;id&quot;) Long id) {
<span class="fc" id="L72">        Long userId = getCurrentUserId();</span>
<span class="fc" id="L73">        notificationService.deleteNotification(id, userId);</span>
<span class="fc" id="L74">        return ResponseEntity.ok(ApiResponse.success(&quot;通知删除成功&quot;, null));</span>
    }

    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId() {
<span class="fc" id="L79">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="fc" id="L80">        String username = authentication.getName();</span>
<span class="fc" id="L81">        return userRepository.findByUsername(username)</span>
<span class="fc" id="L82">                .orElseThrow(() -&gt; new RuntimeException(&quot;用户不存在&quot;))</span>
<span class="fc" id="L83">                .getId();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>