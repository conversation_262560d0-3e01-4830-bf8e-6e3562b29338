<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">AuthController.java</span></div><h1>AuthController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.LoginRequest;
import com.sjtu.secondhand.dto.request.RegisterRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.JwtAuthResponse;
import com.sjtu.secondhand.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping(&quot;/auth&quot;)
@Tag(name = &quot;认证&quot;, description = &quot;用户认证相关接口&quot;)
public class AuthController {

    private final AuthService authService;

<span class="fc" id="L25">    public AuthController(AuthService authService) {</span>
<span class="fc" id="L26">        this.authService = authService;</span>
<span class="fc" id="L27">    }</span>

    @PostMapping(&quot;/login&quot;)
    @Operation(summary = &quot;用户登录&quot;, description = &quot;通过用户名和密码进行登录认证&quot;)
    public ResponseEntity&lt;ApiResponse&lt;JwtAuthResponse&gt;&gt; login(@Valid @RequestBody LoginRequest loginRequest) {
<span class="fc" id="L32">        JwtAuthResponse jwtAuthResponse = authService.login(loginRequest);</span>
<span class="fc" id="L33">        return ResponseEntity.ok(ApiResponse.success(&quot;登录成功&quot;, jwtAuthResponse));</span>
    }

    @PostMapping(&quot;/register&quot;)
    @Operation(summary = &quot;用户注册&quot;, description = &quot;注册新用户&quot;)
    public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; register(@Valid @RequestBody RegisterRequest registerRequest) {
<span class="fc" id="L39">        String result = authService.register(registerRequest);</span>
<span class="fc" id="L40">        return ResponseEntity.ok(ApiResponse.success(&quot;注册成功，请登录&quot;, result));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>