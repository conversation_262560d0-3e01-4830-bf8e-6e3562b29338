<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_class">OrderController</span></div><h1>OrderController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 181</td><td class="ctr2">100%</td><td class="bar">0 of 8</td><td class="ctr2">100%</td><td class="ctr1">0</td><td class="ctr2">14</td><td class="ctr1">0</td><td class="ctr2">29</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a5"><a href="OrderController.java.html#L114" class="el_method">getMyBoughtOrdersByStatus(String, int, int)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="31" alt="31"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">4</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="OrderController.java.html#L129" class="el_method">getMySoldOrdersByStatus(String, int, int)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="31" alt="31"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="OrderController.java.html#L58" class="el_method">getMyBoughtOrders(int, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="116" height="10" title="30" alt="30"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="OrderController.java.html#L72" class="el_method">getMySoldOrders(int, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="116" height="10" title="30" alt="30"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">4</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="OrderController.java.html#L37" class="el_method">createOrder(OrderRequest)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="13" alt="13"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a8"><a href="OrderController.java.html#L46" class="el_method">getOrderById(Long)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="10" alt="10"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="OrderController.java.html#L83" class="el_method">confirmOrder(Long)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="10" alt="10"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="OrderController.java.html#L92" class="el_method">cancelOrder(Long)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="10" alt="10"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="OrderController.java.html#L101" class="el_method">acknowledgeOrder(Long)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="10" alt="10"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="OrderController.java.html#L28" class="el_method">OrderController(OrderService)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>