<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UploadController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_class">UploadController</span></div><h1>UploadController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">86 of 516</td><td class="ctr2">83%</td><td class="bar">5 of 22</td><td class="ctr2">77%</td><td class="ctr1">3</td><td class="ctr2">19</td><td class="ctr1">17</td><td class="ctr2">95</td><td class="ctr1">0</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a1"><a href="UploadController.java.html#L138" class="el_method">getOssInfo()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="71" alt="71"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="46" alt="46"/></td><td class="ctr2" id="c7">39%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">33%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h0">14</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="UploadController.java.html#L180" class="el_method">updateOssCors()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="106" height="10" title="107" alt="107"/></td><td class="ctr2" id="c6">88%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="UploadController.java.html#L120" class="el_method">deleteFile(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="36" alt="36"/></td><td class="ctr2" id="c5">97%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">83%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="UploadController.java.html#L231" class="el_method">getRequestInfo(HttpServletRequest)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="104" height="10" title="105" alt="105"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="UploadController.java.html#L68" class="el_method">getUploadSignature(String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="93" alt="93"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="UploadController.java.html#L104" class="el_method">uploadFile(MultipartFile)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="30" alt="30"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="UploadController.java.html#L56" class="el_method">UploadController(FileStorageService, OssStorageService)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="9" alt="9"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="UploadController.java.html#L30" class="el_method">static {...}</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>