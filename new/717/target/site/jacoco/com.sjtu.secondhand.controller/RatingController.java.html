<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RatingController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">RatingController.java</span></div><h1>RatingController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.service.RatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(&quot;/ratings&quot;)
@Tag(name = &quot;评价 (Ratings)&quot;, description = &quot;交易评价相关接口&quot;)
public class RatingController {

    private final RatingService ratingService;

<span class="fc" id="L24">    public RatingController(RatingService ratingService) {</span>
<span class="fc" id="L25">        this.ratingService = ratingService;</span>
<span class="fc" id="L26">    }</span>

    @PostMapping
    @Operation(summary = &quot;提交交易评价&quot;, description = &quot;在交易完成后，交易双方可调用此接口提交评价。需要指明是为哪种类型的交易（IDLE/WANTED）和具体的交易ID进行评价。&quot;)
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @SecurityRequirement(name = &quot;JWT&quot;)
    public ResponseEntity&lt;ApiResponse&lt;RatingResponse&gt;&gt; createRating(@Valid @RequestBody RatingRequest ratingRequest) {
<span class="fc" id="L33">        RatingResponse ratingResponse = ratingService.createRating(ratingRequest);</span>
<span class="fc" id="L34">        return new ResponseEntity&lt;&gt;(ApiResponse.success(&quot;评价创建成功&quot;, ratingResponse), HttpStatus.CREATED);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>