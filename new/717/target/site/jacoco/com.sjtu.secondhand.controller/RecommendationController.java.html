<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecommendationController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">RecommendationController.java</span></div><h1>RecommendationController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.security.CustomUserDetails;
import com.sjtu.secondhand.service.RecommendationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能推荐系统控制器
 */
@RestController
@RequestMapping(&quot;/recommendations&quot;)
public class RecommendationController {

<span class="fc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(RecommendationController.class);</span>
    private final RecommendationService recommendationService;
    
    // 默认推荐数量参数
    private static final int DEFAULT_HOME_RECOMMENDATIONS = 16;  // 首页推荐默认数量
    private static final int DEFAULT_SIMILAR_ITEMS = 4;        // 商品详情页相似物品默认数量

    @Autowired
<span class="fc" id="L32">    public RecommendationController(RecommendationService recommendationService) {</span>
<span class="fc" id="L33">        this.recommendationService = recommendationService;</span>
<span class="fc" id="L34">    }</span>

    /**
     * 获取全局热门推荐
     * 用于首页热门展示和冷启动场景
     * 
     * @param limit 限制返回结果数量，默认为16
     * @return 热门物品列表
     */
    @GetMapping(&quot;/hot&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;ItemResponse&gt;&gt;&gt; getHotRecommendations(
            @RequestParam(value = &quot;limit&quot;, defaultValue = &quot;16&quot;) int limit) {
<span class="fc" id="L46">        logger.info(&quot;请求热门推荐，限制数量: {}&quot;, limit);</span>
<span class="fc" id="L47">        List&lt;ItemResponse&gt; recommendations = recommendationService.getHotRecommendations(limit);</span>
<span class="fc" id="L48">        return ResponseEntity.ok(ApiResponse.success(recommendations));</span>
    }

    /**
     * 获取首页&quot;为你推荐&quot;个性化推荐列表
     * 基于用户收藏的物品进行协同过滤推荐
     * 如果用户没有收藏（冷启动），则返回热门推荐
     * 
     * @param userDetails 当前登录用户
     * @param limit 限制返回结果数量，默认为16
     * @return 个性化推荐物品列表
     */
    @GetMapping(&quot;/for-you&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;ItemResponse&gt;&gt;&gt; getForYouRecommendations(
            @AuthenticationPrincipal CustomUserDetails userDetails,
            @RequestParam(value = &quot;limit&quot;, defaultValue = &quot;16&quot;) int limit) {
        
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (userDetails == null) {</span>
            // 未登录用户，返回热门推荐
<span class="nc" id="L67">            logger.info(&quot;未登录用户请求首页推荐，返回热门物品&quot;);</span>
<span class="nc" id="L68">            List&lt;ItemResponse&gt; recommendations = recommendationService.getHotRecommendations(limit);</span>
<span class="nc" id="L69">            return ResponseEntity.ok(ApiResponse.success(recommendations));</span>
        }
        
        // 已登录用户，返回个性化推荐
<span class="nc" id="L73">        logger.info(&quot;用户 {} 请求首页个性化推荐&quot;, userDetails.getUsername());</span>
<span class="nc" id="L74">        List&lt;ItemResponse&gt; recommendations = recommendationService.getItemCFRecommendations(</span>
<span class="nc" id="L75">                userDetails.getUserId(), limit);</span>
<span class="nc" id="L76">        return ResponseEntity.ok(ApiResponse.success(recommendations));</span>
    }
    
    /**
     * 获取商品详情页的&quot;猜你喜欢&quot;相似物品
     * 直接从item_similarity表获取相似度最高的物品
     * 
     * @param itemId 当前浏览的物品ID
     * @param limit 限制返回结果数量，默认为4
     * @return 相似物品列表
     */
    @GetMapping(&quot;/similar/{itemId}&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;ItemResponse&gt;&gt;&gt; getSimilarItems(
            @PathVariable Long itemId,
            @RequestParam(value = &quot;limit&quot;, defaultValue = &quot;4&quot;) int limit) {
<span class="fc" id="L91">        logger.info(&quot;请求物品 {} 的相似推荐，限制数量: {}&quot;, itemId, limit);</span>
<span class="fc" id="L92">        List&lt;ItemResponse&gt; recommendations = recommendationService.getContentBasedRecommendations(itemId, limit);</span>
<span class="fc" id="L93">        return ResponseEntity.ok(ApiResponse.success(recommendations));</span>
    }

    /**
     * 手动触发离线计算物品相似度
     * 仅限管理员使用（此功能在简化方案中已不需要，保留API兼容性）
     */
    @PostMapping(&quot;/admin/recalculate&quot;)
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; triggerSimilarityCalculation() {
<span class="fc" id="L103">        logger.info(&quot;手动触发物品相似度计算&quot;);</span>
<span class="fc" id="L104">        recommendationService.calculateItemSimilarities();</span>
<span class="fc" id="L105">        return ResponseEntity.ok(ApiResponse.success(&quot;操作成功，使用预置的相似度数据&quot;));</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>