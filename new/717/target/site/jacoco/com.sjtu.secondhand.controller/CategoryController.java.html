<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CategoryController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_source">CategoryController.java</span></div><h1>CategoryController.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.dto.response.ApiResponse;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(&quot;/categories&quot;)
@Tag(name = &quot;分类&quot;, description = &quot;物品分类相关接口&quot;)
public class CategoryController {

    private final CategoryService categoryService;

    @Autowired
<span class="fc" id="L24">    public CategoryController(CategoryService categoryService) {</span>
<span class="fc" id="L25">        this.categoryService = categoryService;</span>
<span class="fc" id="L26">    }</span>

    @GetMapping
    @Operation(summary = &quot;获取所有物品分类&quot;, description = &quot;返回一个包含所有分类的列表，支持多级分类&quot;)
    public ResponseEntity&lt;ApiResponse&lt;List&lt;Category&gt;&gt;&gt; getAllCategories() {
<span class="fc" id="L31">        List&lt;Category&gt; categories = categoryService.getAllCategories();</span>
<span class="fc" id="L32">        return ResponseEntity.ok(ApiResponse.success(&quot;获取分类成功&quot;, categories));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>