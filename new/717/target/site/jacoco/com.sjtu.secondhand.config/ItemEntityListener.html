<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemEntityListener</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_class">ItemEntityListener</span></div><h1>ItemEntityListener</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 153</td><td class="ctr2">100%</td><td class="bar">0 of 10</td><td class="ctr2">100%</td><td class="ctr1">0</td><td class="ctr2">17</td><td class="ctr1">0</td><td class="ctr2">49</td><td class="ctr1">0</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a0"><a href="ItemEntityListener.java.html#L109" class="el_method">getElasticsearchSyncService()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="ItemEntityListener.java.html#L77" class="el_method">postRemove(Item)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="113" height="10" title="34" alt="34"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="ItemEntityListener.java.html#L51" class="el_method">onSaveOrUpdate(Item)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="27" alt="27"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="ItemEntityListener.java.html#L92" class="el_method">lambda$postRemove$0(Item, ElasticsearchSyncService)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="10" alt="10"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="ItemEntityListener.java.html#L61" class="el_method">lambda$onSaveOrUpdate$0(Item, ElasticsearchSyncService)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="9" alt="9"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="ItemEntityListener.java.html#L40" class="el_method">setApplicationContext(ApplicationContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="7" alt="7"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a9"><a href="ItemEntityListener.java.html#L134" class="el_method">resetServiceCache()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="7" alt="7"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="ItemEntityListener.java.html#L95" class="el_method">lambda$postRemove$1(Item)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="6" alt="6"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="ItemEntityListener.java.html#L64" class="el_method">lambda$onSaveOrUpdate$1(Item)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="ItemEntityListener.java.html#L143" class="el_method">isServiceAvailable()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a11"><a href="ItemEntityListener.java.html#L26" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="ItemEntityListener.java.html#L24" class="el_method">ItemEntityListener()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>