<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OssConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">OssConfig.java</span></div><h1>OssConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.aliyun.oss.model.SetBucketCORSRequest.CORSRule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
<span class="fc" id="L21">public class OssConfig {</span>

<span class="fc" id="L23">    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);</span>

    @Value(&quot;${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}&quot;)
    private String endpoint;

    @Value(&quot;${aliyun.oss.access-key-id:LTAI5tLKLwDpsP947BPBLsnX}&quot;)
    private String accessKeyId;

    @Value(&quot;${aliyun.oss.access-key-secret:******************************}&quot;)
    private String accessKeySecret;

    @Value(&quot;${aliyun.oss.bucket-name:shareu-youyu}&quot;)
    private String bucketName;

    @Value(&quot;${aliyun.oss.bucket-domain:shareu-youyu.oss-cn-shanghai.aliyuncs.com}&quot;)
    private String bucketDomain;
    
    @Value(&quot;${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}&quot;)
    private String allowedOrigins;

    @Bean
    public OSS ossClient() {
<span class="fc" id="L45">        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);</span>
    }
    
    // 将CORS配置移到单独的Bean中以避免循环依赖
    @Configuration
<span class="fc" id="L50">    public static class OssCorsSetting {</span>
        
<span class="fc" id="L52">        private static final Logger logger = LoggerFactory.getLogger(OssCorsSetting.class);</span>
        
        @Value(&quot;${aliyun.oss.bucket-name:shareu-youyu}&quot;)
        private String bucketName;
        
        @Value(&quot;${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}&quot;)
        private String allowedOrigins;
        
        @Autowired
        private OSS ossClient;
        
        @PostConstruct
        public void initOssCorsRules() {
            try {
<span class="fc" id="L66">                logger.info(&quot;开始设置OSS CORS规则，Bucket: {}&quot;, bucketName);</span>
<span class="fc" id="L67">                logger.info(&quot;允许的域名: {}&quot;, allowedOrigins);</span>
                
                // 创建CORS规则
<span class="fc" id="L70">                SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);</span>
                
                // 创建一个CORS规则
<span class="fc" id="L73">                CORSRule rule = new CORSRule();</span>
                
                // 设置允许的来源
<span class="fc" id="L76">                List&lt;String&gt; origins = Arrays.asList(allowedOrigins.split(&quot;,&quot;));</span>
<span class="fc" id="L77">                rule.setAllowedOrigins(origins);</span>
                
                // 设置允许的方法 - 阿里云OSS只支持GET、PUT、DELETE、POST、HEAD方法
<span class="fc" id="L80">                rule.setAllowedMethods(Arrays.asList(&quot;GET&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;POST&quot;, &quot;HEAD&quot;));</span>
                
                // 设置允许的头部
<span class="fc" id="L83">                rule.setAllowedHeaders(Arrays.asList(&quot;*&quot;));</span>
                
                // 设置暴露的头部
<span class="fc" id="L86">                rule.setExposeHeaders(Arrays.asList(&quot;ETag&quot;, &quot;x-oss-request-id&quot;, &quot;Content-Type&quot;, &quot;Content-Length&quot;));</span>
                
                // 设置最大缓存时间（秒）
<span class="fc" id="L89">                rule.setMaxAgeSeconds(3600);</span>
                
                // 创建CORS规则列表
<span class="fc" id="L92">                List&lt;CORSRule&gt; corsRules = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L93">                corsRules.add(rule);</span>
                
                // 设置CORS规则
<span class="fc" id="L96">                request.setCorsRules(corsRules);</span>
                
                // 设置CORS规则
<span class="fc" id="L99">                ossClient.setBucketCORS(request);</span>
                
<span class="fc" id="L101">                logger.info(&quot;OSS CORS规则设置成功&quot;);</span>
<span class="nc" id="L102">            } catch (Exception e) {</span>
<span class="nc" id="L103">                logger.error(&quot;设置OSS CORS规则失败&quot;, e);</span>
<span class="fc" id="L104">            }</span>
<span class="fc" id="L105">        }</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>