<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">WebConfig.java</span></div><h1>WebConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

/**
 * Web配置类
 * 用于配置CORS跨域等Web相关配置
 */
@Configuration
<span class="fc" id="L24">public class WebConfig implements WebMvcConfigurer {</span>

<span class="fc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);</span>

    @Value(&quot;${file.upload-dir:uploads}&quot;)
    private String uploadDir;
    
    @Value(&quot;${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}&quot;)
    private String allowedOrigins;

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
<span class="fc" id="L39">        String[] origins = allowedOrigins.split(&quot;,&quot;);</span>
<span class="fc" id="L40">        logger.info(&quot;配置CORS，允许的域名: {}&quot;, allowedOrigins);</span>
        
<span class="fc" id="L42">        registry.addMapping(&quot;/**&quot;)</span>
<span class="fc" id="L43">                .allowedOrigins(origins)</span>
<span class="fc" id="L44">                .allowedMethods(&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;OPTIONS&quot;, &quot;HEAD&quot;, &quot;PATCH&quot;)</span>
<span class="fc" id="L45">                .allowedHeaders(&quot;*&quot;)</span>
<span class="fc" id="L46">                .exposedHeaders(&quot;Authorization&quot;, &quot;Content-Type&quot;, &quot;Content-Length&quot;, </span>
                               &quot;Content-Disposition&quot;, &quot;ETag&quot;, &quot;x-oss-request-id&quot;)
<span class="fc" id="L48">                .allowCredentials(true)</span>
<span class="fc" id="L49">                .maxAge(3600);</span>
                
<span class="fc" id="L51">        logger.info(&quot;CORS配置完成&quot;);</span>
<span class="fc" id="L52">    }</span>

    /**
     * 添加CORS过滤器，确保所有请求都经过CORS处理
     */
    @Bean
    public CorsFilter corsFilter() {
<span class="fc" id="L59">        logger.info(&quot;初始化CORS过滤器&quot;);</span>
<span class="fc" id="L60">        CorsConfiguration corsConfiguration = new CorsConfiguration();</span>
<span class="fc" id="L61">        String[] origins = allowedOrigins.split(&quot;,&quot;);</span>
        
        // 允许所有来源
<span class="fc" id="L64">        corsConfiguration.setAllowedOrigins(Arrays.asList(origins));</span>
        // 允许所有头
<span class="fc" id="L66">        corsConfiguration.addAllowedHeader(&quot;*&quot;);</span>
        // 允许所有方法
<span class="fc" id="L68">        corsConfiguration.addAllowedMethod(&quot;*&quot;);</span>
        // 允许携带凭证
<span class="fc" id="L70">        corsConfiguration.setAllowCredentials(true);</span>
        // 暴露响应头
<span class="fc" id="L72">        corsConfiguration.addExposedHeader(&quot;Authorization&quot;);</span>
<span class="fc" id="L73">        corsConfiguration.addExposedHeader(&quot;Content-Type&quot;);</span>
<span class="fc" id="L74">        corsConfiguration.addExposedHeader(&quot;Content-Length&quot;);</span>
<span class="fc" id="L75">        corsConfiguration.addExposedHeader(&quot;Content-Disposition&quot;);</span>
<span class="fc" id="L76">        corsConfiguration.addExposedHeader(&quot;ETag&quot;);</span>
<span class="fc" id="L77">        corsConfiguration.addExposedHeader(&quot;x-oss-request-id&quot;);</span>
        
<span class="fc" id="L79">        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();</span>
<span class="fc" id="L80">        source.registerCorsConfiguration(&quot;/**&quot;, corsConfiguration);</span>
        
<span class="fc" id="L82">        logger.info(&quot;CORS过滤器配置完成&quot;);</span>
<span class="fc" id="L83">        return new CorsFilter(source);</span>
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件目录的静态资源映射
<span class="fc" id="L89">        Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();</span>
<span class="fc" id="L90">        logger.info(&quot;配置静态资源映射: /uploads/** -&gt; {}&quot;, uploadPath.toString());</span>
        
<span class="fc" id="L92">        registry.addResourceHandler(&quot;/uploads/**&quot;)</span>
<span class="fc" id="L93">                .addResourceLocations(&quot;file:&quot; + uploadPath.toString() + &quot;/&quot;);</span>
<span class="fc" id="L94">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>