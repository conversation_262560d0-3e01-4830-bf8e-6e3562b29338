<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenApiConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">OpenApiConfig.java</span></div><h1>OpenApiConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
<span class="fc" id="L14">public class OpenApiConfig {</span>

    @Bean
    public OpenAPI customOpenAPI() {
<span class="fc" id="L18">        return new OpenAPI()</span>
<span class="fc" id="L19">                .info(new Info()</span>
<span class="fc" id="L20">                        .title(&quot;高校二手交易平台 API&quot;)</span>
<span class="fc" id="L21">                        .description(&quot;高校二手交易平台后端服务API文档&quot;)</span>
<span class="fc" id="L22">                        .version(&quot;v1.0&quot;)</span>
<span class="fc" id="L23">                        .contact(new Contact()</span>
<span class="fc" id="L24">                                .name(&quot;SJTU SE25-15&quot;)</span>
<span class="fc" id="L25">                                .email(&quot;<EMAIL>&quot;)</span>
<span class="fc" id="L26">                                .url(&quot;https://github.com/SE25-15&quot;))</span>
<span class="fc" id="L27">                        .license(new License()</span>
<span class="fc" id="L28">                                .name(&quot;MIT&quot;)</span>
<span class="fc" id="L29">                                .url(&quot;https://opensource.org/licenses/MIT&quot;)))</span>
<span class="fc" id="L30">                .addSecurityItem(new SecurityRequirement().addList(&quot;JWT&quot;))</span>
<span class="fc" id="L31">                .components(new Components()</span>
<span class="fc" id="L32">                        .addSecuritySchemes(&quot;JWT&quot;, new SecurityScheme()</span>
<span class="fc" id="L33">                                .type(SecurityScheme.Type.HTTP)</span>
<span class="fc" id="L34">                                .scheme(&quot;bearer&quot;)</span>
<span class="fc" id="L35">                                .bearerFormat(&quot;JWT&quot;)</span>
<span class="fc" id="L36">                                .in(SecurityScheme.In.HEADER)</span>
<span class="fc" id="L37">                                .name(&quot;Authorization&quot;)));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>