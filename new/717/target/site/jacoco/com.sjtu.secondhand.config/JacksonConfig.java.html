<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JacksonConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">JacksonConfig.java</span></div><h1>JacksonConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson配置类
 * 用于配置全局的Jackson序列化/反序列化行为
 */
@Configuration
<span class="fc" id="L23">public class JacksonConfig {</span>

    private static final String DATE_FORMAT = &quot;yyyy-MM-dd&quot;;
    private static final String DATE_TIME_FORMAT = &quot;yyyy-MM-dd HH:mm:ss&quot;;
    private static final String TIME_FORMAT = &quot;HH:mm:ss&quot;;

    /**
     * 配置ObjectMapper为主要Bean
     * 添加JavaTimeModule以支持Java 8日期时间类型的序列化/反序列化
     * 
     * @return 配置好的ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
<span class="fc" id="L38">        ObjectMapper objectMapper = new ObjectMapper();</span>

        // 创建Java时间模块并自定义日期时间格式
<span class="fc" id="L41">        JavaTimeModule javaTimeModule = new JavaTimeModule();</span>

        // 配置LocalDate序列化格式
<span class="fc" id="L44">        javaTimeModule.addSerializer(LocalDate.class,</span>
<span class="fc" id="L45">                new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));</span>

        // 配置LocalDateTime序列化格式
<span class="fc" id="L48">        javaTimeModule.addSerializer(LocalDateTime.class,</span>
<span class="fc" id="L49">                new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));</span>

        // 配置LocalTime序列化格式
<span class="fc" id="L52">        javaTimeModule.addSerializer(LocalTime.class,</span>
<span class="fc" id="L53">                new LocalTimeSerializer(DateTimeFormatter.ofPattern(TIME_FORMAT)));</span>

        // 注册模块
<span class="fc" id="L56">        objectMapper.registerModule(javaTimeModule);</span>

        // 禁用将日期转换为时间戳
<span class="fc" id="L59">        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);</span>

<span class="fc" id="L61">        return objectMapper;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>