<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemEntityListener.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">ItemEntityListener.java</span></div><h1>ItemEntityListener.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;
import javax.persistence.PostRemove;
import java.util.Optional;

/**
 * Item实体监听器，用于同步数据到Elasticsearch
 * 
 * 这个监听器会在Item实体的生命周期事件（创建、更新、删除）发生时
 * 自动同步数据到Elasticsearch搜索引擎
 */
@Component
<span class="fc" id="L24">public class ItemEntityListener {</span>

<span class="fc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(ItemEntityListener.class);</span>
    
    // 使用非静态字段，提高可测试性
    private ApplicationContext applicationContext;
    
    // 缓存服务实例，避免重复查找
    private ElasticsearchSyncService elasticsearchSyncService;
    
    /**
     * 设置应用上下文
     * @param applicationContext Spring应用上下文
     */
    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
<span class="fc" id="L40">        this.applicationContext = applicationContext;</span>
<span class="fc" id="L41">        logger.debug(&quot;ApplicationContext已设置&quot;);</span>
<span class="fc" id="L42">    }</span>

    /**
     * 物品保存或更新后触发，同步到Elasticsearch
     * @param item 被操作的Item实体
     */
    @PostPersist
    @PostUpdate
    public void onSaveOrUpdate(Item item) {
<span class="fc bfc" id="L51" title="All 2 branches covered.">        if (item == null) {</span>
<span class="fc" id="L52">            logger.warn(&quot;尝试同步空的Item实体到Elasticsearch&quot;);</span>
<span class="fc" id="L53">            return;</span>
        }
        
<span class="fc" id="L56">        logger.debug(&quot;开始同步Item到Elasticsearch: itemId={}&quot;, item.getId());</span>
        
        try {
<span class="fc" id="L59">            getElasticsearchSyncService().ifPresentOrElse(</span>
                service -&gt; {
<span class="fc" id="L61">                    service.syncItemToElasticsearch(item);</span>
<span class="fc" id="L62">                    logger.info(&quot;成功同步Item到Elasticsearch: itemId={}&quot;, item.getId());</span>
<span class="fc" id="L63">                },</span>
<span class="fc" id="L64">                () -&gt; logger.error(&quot;无法获取ElasticsearchSyncService，同步失败: itemId={}&quot;, item.getId())</span>
            );
<span class="fc" id="L66">        } catch (Exception e) {</span>
<span class="fc" id="L67">            logger.error(&quot;同步Item到Elasticsearch时发生异常: itemId={}&quot;, item.getId(), e);</span>
<span class="fc" id="L68">        }</span>
<span class="fc" id="L69">    }</span>

    /**
     * 物品删除后触发，从Elasticsearch中删除
     * @param item 被操作的Item实体
     */
    @PostRemove
    public void postRemove(Item item) {
<span class="fc bfc" id="L77" title="All 2 branches covered.">        if (item == null) {</span>
<span class="fc" id="L78">            logger.warn(&quot;尝试从Elasticsearch删除空的Item实体&quot;);</span>
<span class="fc" id="L79">            return;</span>
        }
        
<span class="fc bfc" id="L82" title="All 2 branches covered.">        if (item.getId() == null) {</span>
<span class="fc" id="L83">            logger.warn(&quot;尝试从Elasticsearch删除没有ID的Item实体&quot;);</span>
<span class="fc" id="L84">            return;</span>
        }
        
<span class="fc" id="L87">        logger.debug(&quot;开始从Elasticsearch删除Item: itemId={}&quot;, item.getId());</span>
        
        try {
<span class="fc" id="L90">            getElasticsearchSyncService().ifPresentOrElse(</span>
                service -&gt; {
<span class="fc" id="L92">                    service.deleteItemFromElasticsearch(item.getId());</span>
<span class="fc" id="L93">                    logger.info(&quot;成功从Elasticsearch删除Item: itemId={}&quot;, item.getId());</span>
<span class="fc" id="L94">                },</span>
<span class="fc" id="L95">                () -&gt; logger.error(&quot;无法获取ElasticsearchSyncService，删除失败: itemId={}&quot;, item.getId())</span>
            );
<span class="fc" id="L97">        } catch (Exception e) {</span>
<span class="fc" id="L98">            logger.error(&quot;从Elasticsearch删除Item时发生异常: itemId={}&quot;, item.getId(), e);</span>
<span class="fc" id="L99">        }</span>
<span class="fc" id="L100">    }</span>

    /**
     * 获取ElasticsearchSyncService的辅助方法
     * 使用缓存机制，避免重复查找Bean
     * @return 一个包含ElasticsearchSyncService的Optional
     */
    private Optional&lt;ElasticsearchSyncService&gt; getElasticsearchSyncService() {
        // 如果已经缓存了服务实例，直接返回
<span class="fc bfc" id="L109" title="All 2 branches covered.">        if (elasticsearchSyncService != null) {</span>
<span class="fc" id="L110">            return Optional.of(elasticsearchSyncService);</span>
        }
        
        // 检查应用上下文是否可用
<span class="fc bfc" id="L114" title="All 2 branches covered.">        if (applicationContext == null) {</span>
<span class="fc" id="L115">            logger.error(&quot;ApplicationContext未初始化，无法获取ElasticsearchSyncService&quot;);</span>
<span class="fc" id="L116">            return Optional.empty();</span>
        }
        
        try {
            // 从Spring容器中获取服务实例
<span class="fc" id="L121">            elasticsearchSyncService = applicationContext.getBean(ElasticsearchSyncService.class);</span>
<span class="fc" id="L122">            logger.debug(&quot;成功获取ElasticsearchSyncService实例&quot;);</span>
<span class="fc" id="L123">            return Optional.of(elasticsearchSyncService);</span>
<span class="fc" id="L124">        } catch (BeansException e) {</span>
<span class="fc" id="L125">            logger.error(&quot;从Spring容器获取ElasticsearchSyncService失败&quot;, e);</span>
<span class="fc" id="L126">            return Optional.empty();</span>
        }
    }
    
    /**
     * 重置缓存的服务实例（主要用于测试）
     */
    protected void resetServiceCache() {
<span class="fc" id="L134">        this.elasticsearchSyncService = null;</span>
<span class="fc" id="L135">        logger.debug(&quot;ElasticsearchSyncService缓存已重置&quot;);</span>
<span class="fc" id="L136">    }</span>
    
    /**
     * 检查服务是否可用（主要用于测试和监控）
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isServiceAvailable() {
<span class="fc" id="L143">        return getElasticsearchSyncService().isPresent();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>