<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">ElasticsearchConfig.java</span></div><h1>ElasticsearchConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.beans.factory.annotation.Value;

@Configuration
@EnableElasticsearchRepositories(basePackages = &quot;com.sjtu.secondhand.repository.es&quot;)
<span class="fc" id="L11">public class ElasticsearchConfig extends ElasticsearchConfiguration {</span>

    @Value(&quot;${spring.elasticsearch.uris:http://localhost:9200}&quot;)
    private String elasticsearchUri;

    @Override
    public ClientConfiguration clientConfiguration() {
<span class="fc" id="L18">        return ClientConfiguration.builder()</span>
<span class="fc" id="L19">                .connectedTo(elasticsearchUri.replace(&quot;http://&quot;, &quot;&quot;))</span>
<span class="fc" id="L20">                .build();</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>