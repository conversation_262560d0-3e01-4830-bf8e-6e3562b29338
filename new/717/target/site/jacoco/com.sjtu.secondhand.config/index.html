<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.sjtu.secondhand.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <span class="el_package">com.sjtu.secondhand.config</span></div><h1>com.sjtu.secondhand.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">5 of 914</td><td class="ctr2">99%</td><td class="bar">0 of 10</td><td class="ctr2">100%</td><td class="ctr1">0</td><td class="ctr2">46</td><td class="ctr1">2</td><td class="ctr2">199</td><td class="ctr1">0</td><td class="ctr2">41</td><td class="ctr1">0</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a7"><a href="OssConfig$OssCorsSetting.html" class="el_class">OssConfig.OssCorsSetting</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="55" height="10" title="116" alt="116"/></td><td class="ctr2" id="c11">95%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i3">21</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a10"><a href="SecurityConfig.html" class="el_class">SecurityConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="249" alt="249"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i2">32</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">6</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a11"><a href="WebConfig.html" class="el_class">WebConfig</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="188" alt="188"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">35</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="ItemEntityListener.html" class="el_class">ItemEntityListener</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="153" alt="153"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="OpenApiConfig.html" class="el_class">OpenApiConfig</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="60" alt="60"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">21</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k6">2</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a8"><a href="RedisConfig.html" class="el_class">RedisConfig</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="56" alt="56"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">19</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a4"><a href="JacksonConfig.html" class="el_class">JacksonConfig</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="48" alt="48"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k7">2</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a6"><a href="OssConfig.html" class="el_class">OssConfig</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="18" alt="18"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k5">3</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a2"><a href="ElasticsearchConfig.html" class="el_class">ElasticsearchConfig</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">2</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a0"><a href="AsyncConfig.html" class="el_class">AsyncConfig</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a9"><a href="SchedulingConfig.html" class="el_class">SchedulingConfig</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l10">0</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a1"><a href="CacheConfig.html" class="el_class">CacheConfig</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td><td class="ctr1" id="l11">0</td><td class="ctr2" id="m11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>