<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">SecurityConfig.java</span></div><h1>SecurityConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import com.sjtu.secondhand.security.JwtAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
<span class="fc" id="L29">public class SecurityConfig {</span>

<span class="fc" id="L31">    private static final Logger logger = LoggerFactory.getLogger(SecurityConfig.class);</span>

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Value(&quot;${aliyun.oss.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}&quot;)
    private String allowedOrigins;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
<span class="fc" id="L41">        logger.info(&quot;配置Spring Security过滤链&quot;);</span>

<span class="fc" id="L43">        http</span>
<span class="fc" id="L44">                .cors().configurationSource(corsConfigurationSource()).and()</span>
<span class="fc" id="L45">                .csrf().disable()</span>
<span class="fc" id="L46">                .authorizeRequests()</span>
<span class="fc" id="L47">                .antMatchers(&quot;/auth/**&quot;, &quot;/api-docs/**&quot;, &quot;/swagger-ui/**&quot;, &quot;/swagger-ui.html&quot;, &quot;/h2-console/**&quot;,</span>
                        &quot;/cache-test/**&quot;, &quot;/upload/files/**&quot;, &quot;/items/**&quot;, &quot;/categories/**&quot;,
                        &quot;/orders/**&quot;, &quot;/notifications/**&quot;, &quot;/ratings/**&quot;, &quot;/offers/**&quot;,
                        &quot;/upload/image/**&quot;, &quot;/upload/images/**&quot;, &quot;/files/**&quot;, &quot;/test/**&quot;,
                        &quot;/uploads/**&quot;, &quot;/upload/cors-test&quot;)
<span class="fc" id="L52">                .permitAll()</span>
<span class="fc" id="L53">                .anyRequest().authenticated()</span>
<span class="fc" id="L54">                .and()</span>
<span class="fc" id="L55">                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);</span>

        // 允许H2控制台的frame显示
<span class="fc" id="L58">        http.headers().frameOptions().disable();</span>

        // 添加JWT过滤器
<span class="fc" id="L61">        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);</span>

<span class="fc" id="L63">        logger.info(&quot;Spring Security配置完成&quot;);</span>
<span class="fc" id="L64">        return http.build();</span>
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration)
            throws Exception {
<span class="fc" id="L70">        return authenticationConfiguration.getAuthenticationManager();</span>
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
<span class="fc" id="L75">        return new BCryptPasswordEncoder();</span>
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
<span class="fc" id="L80">        logger.info(&quot;配置Spring Security CORS源&quot;);</span>

<span class="fc" id="L82">        CorsConfiguration configuration = new CorsConfiguration();</span>

        // 设置允许的来源
<span class="fc" id="L85">        String[] origins = allowedOrigins.split(&quot;,&quot;);</span>
<span class="fc" id="L86">        logger.info(&quot;Security CORS配置，允许的域名: {}&quot;, allowedOrigins);</span>
<span class="fc" id="L87">        configuration.setAllowedOrigins(Arrays.asList(origins));</span>

        // 设置允许的HTTP方法
<span class="fc" id="L90">        configuration.setAllowedMethods(Arrays.asList(&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;OPTIONS&quot;, &quot;HEAD&quot;, &quot;PATCH&quot;));</span>

        // 设置允许的头
<span class="fc" id="L93">        configuration.setAllowedHeaders(Arrays.asList(&quot;*&quot;));</span>

        // 设置暴露的头
<span class="fc" id="L96">        configuration.setExposedHeaders(Arrays.asList(&quot;Authorization&quot;, &quot;Content-Type&quot;, &quot;Content-Length&quot;,</span>
                &quot;Content-Disposition&quot;, &quot;ETag&quot;, &quot;x-oss-request-id&quot;));

        // 允许凭证
<span class="fc" id="L100">        configuration.setAllowCredentials(true);</span>

        // 设置最大缓存时间
<span class="fc" id="L103">        configuration.setMaxAge(3600L);</span>

<span class="fc" id="L105">        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();</span>
<span class="fc" id="L106">        source.registerCorsConfiguration(&quot;/**&quot;, configuration);</span>

<span class="fc" id="L108">        logger.info(&quot;Security CORS配置完成&quot;);</span>
<span class="fc" id="L109">        return source;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>