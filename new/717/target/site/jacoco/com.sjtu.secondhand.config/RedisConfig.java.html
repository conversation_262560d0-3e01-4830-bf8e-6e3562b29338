<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RedisConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.config</a> &gt; <span class="el_source">RedisConfig.java</span></div><h1>RedisConfig.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * Redis缓存配置类
 */
@Configuration
@EnableCaching
<span class="fc" id="L27">public class RedisConfig {</span>

        /**
         * 配置RedisTemplate，用于操作Redis
         * 
         * @param connectionFactory Redis连接工厂
         * @return RedisTemplate&lt;String, Object&gt;
         */
        @Bean
        public RedisTemplate&lt;String, Object&gt; redisTemplate(RedisConnectionFactory connectionFactory) {
<span class="fc" id="L37">                RedisTemplate&lt;String, Object&gt; template = new RedisTemplate&lt;&gt;();</span>
<span class="fc" id="L38">                template.setConnectionFactory(connectionFactory);</span>

                // 使用StringRedisSerializer来序列化和反序列化redis的key值
<span class="fc" id="L41">                template.setKeySerializer(new StringRedisSerializer());</span>
                // 使用GenericJackson2JsonRedisSerializer来序列化和反序列化redis的value值
<span class="fc" id="L43">                template.setValueSerializer(new GenericJackson2JsonRedisSerializer());</span>
<span class="fc" id="L44">                template.setHashKeySerializer(new StringRedisSerializer());</span>
<span class="fc" id="L45">                template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());</span>

<span class="fc" id="L47">                template.afterPropertiesSet();</span>
<span class="fc" id="L48">                return template;</span>
        }

        /**
         * 配置缓存管理器
         * 
         * @param redisConnectionFactory Redis连接工厂
         * @return RedisCacheManager
         */
        @Bean
        public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
                // 设置缓存配置，过期时间为30秒
<span class="fc" id="L60">                RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()</span>
<span class="fc" id="L61">                                .entryTtl(Duration.ofSeconds(30))</span>
<span class="fc" id="L62">                                .serializeKeysWith(</span>
                                                RedisSerializationContext.SerializationPair
<span class="fc" id="L64">                                                                .fromSerializer(new StringRedisSerializer()))</span>
<span class="fc" id="L65">                                .serializeValuesWith(RedisSerializationContext.SerializationPair</span>
<span class="fc" id="L66">                                                .fromSerializer(new GenericJackson2JsonRedisSerializer()))</span>
<span class="fc" id="L67">                                .disableCachingNullValues();</span>

<span class="fc" id="L69">                return RedisCacheManager.builder(redisConnectionFactory)</span>
<span class="fc" id="L70">                                .cacheDefaults(config)</span>
<span class="fc" id="L71">                                .build();</span>
        }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>