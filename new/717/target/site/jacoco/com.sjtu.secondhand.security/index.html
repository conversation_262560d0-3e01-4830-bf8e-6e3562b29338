<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.sjtu.secondhand.security</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <span class="el_package">com.sjtu.secondhand.security</span></div><h1>com.sjtu.secondhand.security</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1 of 265</td><td class="ctr2">99%</td><td class="bar">1 of 16</td><td class="ctr2">93%</td><td class="ctr1">1</td><td class="ctr2">34</td><td class="ctr1">0</td><td class="ctr2">66</td><td class="ctr1">0</td><td class="ctr2">26</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="JwtTokenProvider.html" class="el_class">JwtTokenProvider</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="119" height="10" title="155" alt="155"/></td><td class="ctr2" id="c3">99%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="110" height="10" title="11" alt="11"/></td><td class="ctr2" id="e1">91%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">15</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">34</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CustomUserDetailsService.html" class="el_class">CustomUserDetailsService</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="40" alt="40"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CustomUserDetails.html" class="el_class">CustomUserDetails</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="35" alt="35"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k0">10</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="JwtAuthenticationFilter.html" class="el_class">JwtAuthenticationFilter</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="34" alt="34"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>