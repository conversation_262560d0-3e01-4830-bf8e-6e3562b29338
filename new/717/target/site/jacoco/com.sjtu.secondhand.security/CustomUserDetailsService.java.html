<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomUserDetailsService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.security</a> &gt; <span class="el_source">CustomUserDetailsService.java</span></div><h1>CustomUserDetailsService.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.security;

import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;

<span class="fc" id="L17">    public CustomUserDetailsService(UserRepository userRepository) {</span>
<span class="fc" id="L18">        this.userRepository = userRepository;</span>
<span class="fc" id="L19">    }</span>

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
<span class="fc" id="L23">        User user = userRepository.findByUsername(username)</span>
<span class="fc" id="L24">                .orElseThrow(() -&gt; new UsernameNotFoundException(&quot;用户名不存在: &quot; + username));</span>

<span class="fc" id="L26">        return new CustomUserDetails(user);</span>
    }

    public User loadUserById(Long id) {
<span class="fc" id="L30">        return userRepository.findById(id)</span>
<span class="fc" id="L31">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>