<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtAuthenticationFilter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.security</a> &gt; <span class="el_source">JwtAuthenticationFilter.java</span></div><h1>JwtAuthenticationFilter.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.security;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenProvider jwtTokenProvider;

<span class="fc" id="L19">    public JwtAuthenticationFilter(JwtTokenProvider jwtTokenProvider) {</span>
<span class="fc" id="L20">        this.jwtTokenProvider = jwtTokenProvider;</span>
<span class="fc" id="L21">    }</span>

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // Parse token from Authorization header or URL parameters
<span class="fc" id="L27">        String token = jwtTokenProvider.resolveToken(request);</span>

        try {
<span class="fc bfc" id="L30" title="All 4 branches covered.">            if (token != null &amp;&amp; jwtTokenProvider.validateToken(token)) {</span>
<span class="fc" id="L31">                Authentication auth = jwtTokenProvider.getAuthentication(token);</span>
<span class="fc" id="L32">                SecurityContextHolder.getContext().setAuthentication(auth);</span>
            }
<span class="fc" id="L34">        } catch (Exception ex) {</span>
<span class="fc" id="L35">            SecurityContextHolder.clearContext();</span>
<span class="fc" id="L36">        }</span>

<span class="fc" id="L38">        filterChain.doFilter(request, response);</span>
<span class="fc" id="L39">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>