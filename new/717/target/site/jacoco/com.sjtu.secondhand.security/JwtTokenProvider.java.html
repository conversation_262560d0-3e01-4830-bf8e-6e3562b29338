<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtTokenProvider.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.security</a> &gt; <span class="el_source">JwtTokenProvider.java</span></div><h1>JwtTokenProvider.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.security;

import io.jsonwebtoken.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.Date;

@Component
public class JwtTokenProvider {

    @Value(&quot;${jwt.secret}&quot;)
    private String secretKey;

    @Value(&quot;${jwt.expiration}&quot;)
    private long validityInSeconds;

    private final UserDetailsService userDetailsService;

<span class="fc" id="L27">    public JwtTokenProvider(UserDetailsService userDetailsService) {</span>
<span class="fc" id="L28">        this.userDetailsService = userDetailsService;</span>
<span class="fc" id="L29">    }</span>

    @PostConstruct
    protected void init() {
<span class="fc" id="L33">        secretKey = Base64.getEncoder().encodeToString(secretKey.getBytes());</span>
<span class="fc" id="L34">    }</span>

    public String createToken(String username, Long userId) {
<span class="fc" id="L37">        Claims claims = Jwts.claims().setSubject(username);</span>
<span class="fc" id="L38">        claims.put(&quot;userId&quot;, userId);</span>

<span class="fc" id="L40">        Date now = new Date();</span>
<span class="fc" id="L41">        Date validity = new Date(now.getTime() + validityInSeconds * 1000);</span>

<span class="fc" id="L43">        return Jwts.builder()</span>
<span class="fc" id="L44">                .setClaims(claims)</span>
<span class="fc" id="L45">                .setIssuedAt(now)</span>
<span class="fc" id="L46">                .setExpiration(validity)</span>
<span class="fc" id="L47">                .signWith(SignatureAlgorithm.HS256, secretKey)</span>
<span class="fc" id="L48">                .compact();</span>
    }

    public String getUsername(String token) {
<span class="fc" id="L52">        return Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody().getSubject();</span>
    }

    public Long getUserId(String token) {
<span class="fc" id="L56">        return Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody().get(&quot;userId&quot;, Long.class);</span>
    }

    /**
     * 根据用户名获取用户ID
     * 
     * @param username 用户名
     * @return 用户ID
     */
    public Long getUserIdFromUsername(String username) {
        // 从 UserDetails 中获取用户ID
<span class="fc" id="L67">        UserDetails userDetails = userDetailsService.loadUserByUsername(username);</span>
<span class="fc bfc" id="L68" title="All 2 branches covered.">        if (userDetails instanceof CustomUserDetails) {</span>
<span class="fc" id="L69">            return ((CustomUserDetails) userDetails).getUserId();</span>
        }
<span class="fc" id="L71">        throw new IllegalStateException(&quot;无法获取用户ID&quot;);</span>
    }

    public String resolveToken(HttpServletRequest request) {
        // 首先尝试从授权头获取token
<span class="fc" id="L76">        String bearerToken = request.getHeader(&quot;Authorization&quot;);</span>
<span class="fc bfc" id="L77" title="All 4 branches covered.">        if (bearerToken != null &amp;&amp; bearerToken.startsWith(&quot;Bearer &quot;)) {</span>
<span class="fc" id="L78">            return bearerToken.substring(7);</span>
        }

        // 如果授权头中没有找到，尝试从URL参数获取token
<span class="fc" id="L82">        String tokenParam = request.getParameter(&quot;token&quot;);</span>
<span class="fc bfc" id="L83" title="All 4 branches covered.">        if (tokenParam != null &amp;&amp; !tokenParam.isEmpty()) {</span>
<span class="fc" id="L84">            return tokenParam;</span>
        }

<span class="fc" id="L87">        return null;</span>
    }

    public boolean validateToken(String token) {
        try {
<span class="fc" id="L92">            Jws&lt;Claims&gt; claims = Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token);</span>
<span class="pc bpc" id="L93" title="1 of 2 branches missed.">            return !claims.getBody().getExpiration().before(new Date());</span>
<span class="fc" id="L94">        } catch (JwtException | IllegalArgumentException e) {</span>
<span class="fc" id="L95">            return false;</span>
        }
    }

    public Authentication getAuthentication(String token) {
<span class="fc" id="L100">        UserDetails userDetails = userDetailsService.loadUserByUsername(getUsername(token));</span>
<span class="fc" id="L101">        return new UsernamePasswordAuthenticationToken(userDetails, &quot;&quot;, userDetails.getAuthorities());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>