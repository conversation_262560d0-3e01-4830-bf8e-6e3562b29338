<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">OrderServiceImpl.java</span></div><h1>OrderServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OrderRequest;
import com.sjtu.secondhand.dto.response.OrderResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OfferRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.OrderService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class OrderServiceImpl implements OrderService {

    private final OrderRepository orderRepository;
    private final ItemRepository itemRepository;
    private final OfferRepository offerRepository;
    private final UserService userService;
    private final NotificationService notificationService;
    private final NotificationEventService notificationEventService;

    public OrderServiceImpl(OrderRepository orderRepository,
            ItemRepository itemRepository,
            OfferRepository offerRepository,
            UserService userService,
            NotificationService notificationService,
<span class="fc" id="L42">            NotificationEventService notificationEventService) {</span>
<span class="fc" id="L43">        this.orderRepository = orderRepository;</span>
<span class="fc" id="L44">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L45">        this.offerRepository = offerRepository;</span>
<span class="fc" id="L46">        this.userService = userService;</span>
<span class="fc" id="L47">        this.notificationService = notificationService;</span>
<span class="fc" id="L48">        this.notificationEventService = notificationEventService;</span>
<span class="fc" id="L49">    }</span>

    @Override
    @Transactional
    public OrderResponse createOrder(OrderRequest orderRequest) {
<span class="fc" id="L54">        User buyer = userService.getCurrentUser();</span>
<span class="fc" id="L55">        Item item = itemRepository.findById(orderRequest.getItem_id().longValue())</span>
<span class="fc" id="L56">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;商品不存在&quot;));</span>

        // 检查商品状态
<span class="fc bfc" id="L59" title="All 2 branches covered.">        if (item.getStatus() != Item.ItemStatus.FOR_SALE) {</span>
<span class="fc" id="L60">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;该商品不可购买&quot;);</span>
        }

        // 检查是否已有进行中的订单
<span class="fc" id="L64">        boolean hasActiveOrder = orderRepository.existsActiveOrderForItem(item);</span>
<span class="fc bfc" id="L65" title="All 2 branches covered.">        if (hasActiveOrder) {</span>
<span class="fc" id="L66">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;该商品已有进行中的订单&quot;);</span>
        }

        // 检查是否是自己的商品
<span class="fc bfc" id="L70" title="All 2 branches covered.">        if (item.getUser().getId().equals(buyer.getId())) {</span>
<span class="fc" id="L71">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;不能购买自己的商品&quot;);</span>
        }

        // 创建订单
<span class="fc" id="L75">        Order order = new Order();</span>
<span class="fc" id="L76">        order.setItem(item);</span>
<span class="fc" id="L77">        order.setBuyer(buyer);</span>
<span class="fc" id="L78">        order.setSeller(item.getUser());</span>
<span class="fc" id="L79">        order.setStatus(Order.OrderStatus.PENDING_CONFIRMATION);</span>

        // 注意：根据API文档，OrderRequest中不再包含offer_id字段
        // 如果需要关联报价，应该在其他地方处理

<span class="fc" id="L84">        Order savedOrder = orderRepository.save(order);</span>

        // 更新商品状态
<span class="fc" id="L87">        item.setStatus(Item.ItemStatus.RESERVED);</span>
<span class="fc" id="L88">        itemRepository.save(item);</span>

        // 发送通知
<span class="fc" id="L91">        notificationService.createNewOrderNotification(savedOrder);</span>

        // 发送实时更新
        try {
<span class="fc" id="L95">            notificationEventService.sendOrderUpdateEvent(</span>
                    savedOrder,
                    &quot;created&quot;,
                    &quot;新订单已创建，等待卖家确认&quot;);
<span class="fc" id="L99">        } catch (Exception e) {</span>
            // Just log the error, don't fail the operation
<span class="fc" id="L101">            System.err.println(&quot;Failed to send order update event: &quot; + e.getMessage());</span>
<span class="fc" id="L102">        }</span>

<span class="fc" id="L104">        return convertToOrderResponse(savedOrder);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public OrderResponse getOrderById(Long orderId) {
<span class="fc" id="L110">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L111">        Order order = getOrderAndCheckPermission(orderId, currentUser);</span>

<span class="fc" id="L113">        return new OrderResponse(order);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OrderResponse&gt; getMyBoughtOrders(Pageable pageable) {
<span class="fc" id="L119">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L120">        Page&lt;Order&gt; orders = orderRepository.findByBuyer(currentUser, pageable);</span>

<span class="fc" id="L122">        return orders.map(OrderResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OrderResponse&gt; getMySoldOrders(Pageable pageable) {
<span class="fc" id="L128">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L129">        Page&lt;Order&gt; orders = orderRepository.findBySeller(currentUser, pageable);</span>

<span class="fc" id="L131">        return orders.map(OrderResponse::new);</span>
    }

    @Override
    @Transactional
    public OrderResponse confirmOrder(Long orderId) {
<span class="fc" id="L137">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L138">        Order order = orderRepository.findById(orderId)</span>
<span class="pc" id="L139">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证当前用户是否为卖家
<span class="fc bfc" id="L142" title="All 2 branches covered.">        if (!order.getSeller().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L143">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有卖家可以确认订单&quot;);</span>
        }

        // 检查订单状态
<span class="fc bfc" id="L147" title="All 2 branches covered.">        if (order.getStatus() != Order.OrderStatus.PENDING_CONFIRMATION) {</span>
<span class="fc" id="L148">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有待确认的订单可以被确认&quot;);</span>
        }

        // 更新订单状态
<span class="fc" id="L152">        order.setStatus(Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT);</span>
<span class="fc" id="L153">        order.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L154">        Order updatedOrder = orderRepository.save(order);</span>

        // 发送通知
<span class="fc" id="L157">        notificationService.createOrderConfirmedNotification(updatedOrder);</span>

        // 发送实时更新
        try {
<span class="fc" id="L161">            notificationEventService.sendOrderUpdateEvent(</span>
                    updatedOrder,
                    &quot;confirmed&quot;,
                    &quot;订单已确认，等待买家确认联系方式&quot;);
<span class="nc" id="L165">        } catch (Exception e) {</span>
            // Just log the error, don't fail the operation
<span class="nc" id="L167">            System.err.println(&quot;Failed to send order update event: &quot; + e.getMessage());</span>
<span class="fc" id="L168">        }</span>

<span class="fc" id="L170">        return new OrderResponse(updatedOrder);</span>
    }

    @Override
    @Transactional
    public OrderResponse cancelOrder(Long orderId) {
<span class="fc" id="L176">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L177">        Order order = orderRepository.findById(orderId)</span>
<span class="pc" id="L178">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证当前用户是买家或卖家
<span class="fc" id="L181">        boolean isBuyer = order.getBuyer().getId().equals(currentUser.getId());</span>
<span class="fc" id="L182">        boolean isSeller = order.getSeller().getId().equals(currentUser.getId());</span>

<span class="fc bfc" id="L184" title="All 4 branches covered.">        if (!isBuyer &amp;&amp; !isSeller) {</span>
<span class="fc" id="L185">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有买家或卖家可以取消订单&quot;);</span>
        }

        // 检查订单状态
<span class="pc bpc" id="L189" title="1 of 4 branches missed.">        if (order.getStatus() == Order.OrderStatus.COMPLETED || order.getStatus() == Order.OrderStatus.CANCELLED) {</span>
<span class="fc" id="L190">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;已完成或已取消的订单不能被取消&quot;);</span>
        }

        // 更新订单状态
<span class="fc" id="L194">        order.setStatus(Order.OrderStatus.CANCELLED);</span>
<span class="fc" id="L195">        order.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L196">        Order updatedOrder = orderRepository.save(order);</span>

        // 更新商品状态
<span class="fc" id="L199">        Item item = order.getItem();</span>
<span class="fc" id="L200">        item.setStatus(Item.ItemStatus.FOR_SALE);</span>
<span class="fc" id="L201">        itemRepository.save(item);</span>

        // 发送通知
<span class="fc" id="L204">        notificationService.createOrderCancelledNotification(updatedOrder, currentUser);</span>

        // 发送实时更新
        try {
<span class="fc" id="L208">            notificationEventService.sendOrderUpdateEvent(</span>
                    updatedOrder,
                    &quot;cancelled&quot;,
                    &quot;订单已取消，物品重新上架&quot;);
<span class="nc" id="L212">        } catch (Exception e) {</span>
            // Just log the error, don't fail the operation
<span class="nc" id="L214">            System.err.println(&quot;Failed to send order update event: &quot; + e.getMessage());</span>
<span class="fc" id="L215">        }</span>

<span class="fc" id="L217">        return convertToOrderResponse(updatedOrder);</span>
    }

    @Override
    @Transactional
    public OrderResponse confirmContact(Long orderId) {
<span class="fc" id="L223">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L224">        Order order = orderRepository.findById(orderId)</span>
<span class="pc" id="L225">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证当前用户是否为买家
<span class="fc bfc" id="L228" title="All 2 branches covered.">        if (!order.getBuyer().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L229">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有买家可以确认联系&quot;);</span>
        }

        // 检查订单状态
<span class="fc bfc" id="L233" title="All 2 branches covered.">        if (order.getStatus() != Order.OrderStatus.AWAITING_ACKNOWLEDGEMENT) {</span>
<span class="fc" id="L234">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有待确认联系的订单可以被确认联系&quot;);</span>
        }

        // 更新订单状态
<span class="fc" id="L238">        order.setStatus(Order.OrderStatus.CONFIRMED);</span>
<span class="fc" id="L239">        order.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L240">        Order updatedOrder = orderRepository.save(order);</span>

        // 发送通知
<span class="fc" id="L243">        notificationService.createContactConfirmedNotification(updatedOrder);</span>

<span class="fc" id="L245">        return convertToOrderResponse(updatedOrder);</span>
    }

    @Override
    @Transactional
    public OrderResponse completeOrder(Long orderId) {
<span class="fc" id="L251">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L252">        Order order = orderRepository.findById(orderId)</span>
<span class="pc" id="L253">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证当前用户是否为买家
<span class="fc bfc" id="L256" title="All 2 branches covered.">        if (!order.getBuyer().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L257">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有买家可以完成订单&quot;);</span>
        }

        // 检查订单状态
<span class="fc bfc" id="L261" title="All 2 branches covered.">        if (order.getStatus() != Order.OrderStatus.CONFIRMED) {</span>
<span class="fc" id="L262">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有已确认的订单可以被完成&quot;);</span>
        }

        // 更新订单状态
<span class="fc" id="L266">        order.setStatus(Order.OrderStatus.COMPLETED);</span>
<span class="fc" id="L267">        order.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L268">        Order updatedOrder = orderRepository.save(order);</span>

        // 更新商品状态
<span class="fc" id="L271">        Item item = order.getItem();</span>
<span class="fc" id="L272">        item.setStatus(Item.ItemStatus.SOLD);</span>
<span class="fc" id="L273">        itemRepository.save(item);</span>

        // 发送通知
<span class="fc" id="L276">        notificationService.createOrderCompletedNotification(updatedOrder);</span>

        // 发送实时更新
        try {
<span class="fc" id="L280">            notificationEventService.sendOrderUpdateEvent(</span>
                    updatedOrder,
                    &quot;completed&quot;,
                    &quot;交易已完成，物品已售出&quot;);
<span class="fc" id="L284">        } catch (Exception e) {</span>
            // Just log the error, don't fail the operation
<span class="fc" id="L286">            System.err.println(&quot;Failed to send order update event: &quot; + e.getMessage());</span>
<span class="fc" id="L287">        }</span>

<span class="fc" id="L289">        return convertToOrderResponse(updatedOrder);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OrderResponse&gt; getMyBoughtOrdersByStatus(String status, Pageable pageable) {
<span class="nc" id="L295">        User currentUser = userService.getCurrentUser();</span>
<span class="nc" id="L296">        Order.OrderStatus orderStatus = parseOrderStatus(status);</span>

<span class="nc" id="L298">        Page&lt;Order&gt; orders = orderRepository.findByBuyerAndStatus(currentUser, orderStatus, pageable);</span>
<span class="nc" id="L299">        return orders.map(OrderResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OrderResponse&gt; getMySoldOrdersByStatus(String status, Pageable pageable) {
<span class="nc" id="L305">        User currentUser = userService.getCurrentUser();</span>
<span class="nc" id="L306">        Order.OrderStatus orderStatus = parseOrderStatus(status);</span>

<span class="nc" id="L308">        Page&lt;Order&gt; orders = orderRepository.findBySellerAndStatus(currentUser, orderStatus, pageable);</span>
<span class="nc" id="L309">        return orders.map(OrderResponse::new);</span>
    }

    // 辅助方法：获取订单并检查权限
    private Order getOrderAndCheckPermission(Long orderId, User currentUser) {
<span class="fc" id="L314">        Order order = orderRepository.findById(orderId)</span>
<span class="fc" id="L315">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证是否是买家或卖家
<span class="fc bfc" id="L318" title="All 2 branches covered.">        if (!order.getBuyer().getId().equals(currentUser.getId()) &amp;&amp;</span>
<span class="fc bfc" id="L319" title="All 2 branches covered.">                !order.getSeller().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L320">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;没有权限访问该订单&quot;);</span>
        }

<span class="fc" id="L323">        return order;</span>
    }

    // 辅助方法：解析订单状态字符串
    private Order.OrderStatus parseOrderStatus(String status) {
        try {
<span class="nc" id="L329">            return Order.OrderStatus.valueOf(status.toUpperCase());</span>
<span class="nc" id="L330">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L331">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的订单状态&quot;);</span>
        }
    }

    // 辅助方法：将Order对象转换为OrderResponse
    private OrderResponse convertToOrderResponse(Order order) {
<span class="fc" id="L337">        return new OrderResponse(order);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>