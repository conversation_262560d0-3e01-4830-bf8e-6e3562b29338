<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">UserServiceImpl.java</span></div><h1>UserServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.security.CustomUserDetailsService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final CustomUserDetailsService userDetailsService;

    @Autowired
<span class="fc" id="L22">    public UserServiceImpl(UserRepository userRepository, CustomUserDetailsService userDetailsService) {</span>
<span class="fc" id="L23">        this.userRepository = userRepository;</span>
<span class="fc" id="L24">        this.userDetailsService = userDetailsService;</span>
<span class="fc" id="L25">    }</span>

    @Override
    public User getCurrentUser() {
<span class="fc" id="L29">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="fc" id="L30">        String username = authentication.getName();</span>
<span class="fc" id="L31">        return userRepository.findByUsername(username)</span>
<span class="fc" id="L32">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;当前用户不存在&quot;));</span>
    }

    @Override
    public User getUserById(Long userId) {
<span class="fc" id="L37">        return userRepository.findById(userId)</span>
<span class="fc" id="L38">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
    }

    @Override
    public User getUserByUsername(String username) {
<span class="fc" id="L43">        return userRepository.findByUsername(username)</span>
<span class="fc" id="L44">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
    }

    @Override
    public User updateUser(User userUpdates) {
<span class="fc" id="L49">        User currentUser = getCurrentUser();</span>

<span class="fc bfc" id="L51" title="All 2 branches covered.">        if (userUpdates.getUsername() != null) {</span>
<span class="fc" id="L52">            currentUser.setUsername(userUpdates.getUsername());</span>
        }

<span class="fc bfc" id="L55" title="All 2 branches covered.">        if (userUpdates.getEmail() != null) {</span>
<span class="fc" id="L56">            currentUser.setEmail(userUpdates.getEmail());</span>
        }

<span class="fc bfc" id="L59" title="All 2 branches covered.">        if (userUpdates.getAvatarUrl() != null) {</span>
<span class="fc" id="L60">            currentUser.setAvatarUrl(userUpdates.getAvatarUrl());</span>
        }

<span class="fc" id="L63">        return userRepository.save(currentUser);</span>
    }

    @Override
    @Transactional
    public User saveUser(User user) {
<span class="fc" id="L69">        return userRepository.save(user);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>