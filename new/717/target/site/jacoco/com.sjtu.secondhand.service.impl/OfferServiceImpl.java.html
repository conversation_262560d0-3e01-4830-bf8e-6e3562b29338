<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OfferServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">OfferServiceImpl.java</span></div><h1>OfferServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.OfferRequest;
import com.sjtu.secondhand.dto.response.OfferResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OfferRepository;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.OfferService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class OfferServiceImpl implements OfferService {

    private final OfferRepository offerRepository;
    private final ItemRepository itemRepository;
    private final UserService userService;
    private final NotificationService notificationService;

    @Autowired
    public OfferServiceImpl(OfferRepository offerRepository, ItemRepository itemRepository, 
<span class="fc" id="L34">                           UserService userService, NotificationService notificationService) {</span>
<span class="fc" id="L35">        this.offerRepository = offerRepository;</span>
<span class="fc" id="L36">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L37">        this.userService = userService;</span>
<span class="fc" id="L38">        this.notificationService = notificationService;</span>
<span class="fc" id="L39">    }</span>

    @Override
    @Transactional
    public OfferResponse createOffer(OfferRequest offerRequest) {
        // 这个方法不应该被调用，因为我们现在使用带有wantedItemId参数的方法
<span class="fc" id="L45">        throw new UnsupportedOperationException(&quot;请使用带有wantedItemId参数的createOffer方法&quot;);</span>
    }

    @Override
    @Transactional
    public OfferResponse createOffer(Long wantedItemId, OfferRequest offerRequest) {
<span class="fc" id="L51">        User currentUser = userService.getCurrentUser();</span>
        
        // 获取求购物品
<span class="fc" id="L54">        Item wantedItem = itemRepository.findById(wantedItemId)</span>
<span class="fc" id="L55">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;求购物品不存在&quot;));</span>
        
        // 检查物品类型
<span class="fc bfc" id="L58" title="All 2 branches covered.">        if (wantedItem.getItemType() != Item.ItemType.WANTED) {</span>
<span class="fc" id="L59">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只能对求购类型的物品创建Offer&quot;);</span>
        }
        
        // 检查物品状态
<span class="fc bfc" id="L63" title="All 2 branches covered.">        if (wantedItem.getStatus() != Item.ItemStatus.FOR_SALE) {</span>
<span class="fc" id="L64">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;该求购物品已不可响应&quot;);</span>
        }
        
        // 检查是否是自己的求购物品
<span class="fc bfc" id="L68" title="All 2 branches covered.">        if (wantedItem.getUser().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L69">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;不能响应自己的求购&quot;);</span>
        }
        
        // 创建Offer
<span class="fc" id="L73">        Offer offer = new Offer();</span>
<span class="fc" id="L74">        offer.setWantedItem(wantedItem);</span>
<span class="fc" id="L75">        offer.setOfferer(currentUser);</span>
<span class="fc" id="L76">        offer.setRequester(wantedItem.getUser());</span>
<span class="fc" id="L77">        offer.setStatus(Offer.OfferStatus.PENDING_ACCEPTANCE);</span>
        
<span class="fc" id="L79">        Offer savedOffer = offerRepository.save(offer);</span>
        
        // 发送通知
<span class="fc" id="L82">        notificationService.createNewOfferNotification(savedOffer);</span>
        
<span class="fc" id="L84">        return new OfferResponse(savedOffer);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public OfferResponse getOfferById(Long offerId) {
<span class="fc" id="L90">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L91">        Offer offer = getOfferAndCheckPermission(offerId, currentUser);</span>
        
<span class="fc" id="L93">        return new OfferResponse(offer);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OfferResponse&gt; getMyOffers(Pageable pageable) {
<span class="fc" id="L99">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L100">        Page&lt;Offer&gt; offers = offerRepository.findByOfferer(currentUser, pageable);</span>
        
<span class="fc" id="L102">        return offers.map(OfferResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OfferResponse&gt; getMyRequests(Pageable pageable) {
<span class="fc" id="L108">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L109">        Page&lt;Offer&gt; offers = offerRepository.findByRequester(currentUser, pageable);</span>
        
<span class="fc" id="L111">        return offers.map(OfferResponse::new);</span>
    }

    @Override
    @Transactional
    public OfferResponse acceptOffer(Long offerId) {
<span class="fc" id="L117">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L118">        Offer offer = offerRepository.findById(offerId)</span>
<span class="pc" id="L119">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为求购者
<span class="fc bfc" id="L122" title="All 2 branches covered.">        if (!offer.getRequester().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L123">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有求购者可以接受Offer&quot;);</span>
        }
        
        // 检查Offer状态
<span class="fc bfc" id="L127" title="All 2 branches covered.">        if (offer.getStatus() != Offer.OfferStatus.PENDING_ACCEPTANCE) {</span>
<span class="fc" id="L128">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有待接受的Offer可以被接受&quot;);</span>
        }
        
        // 更新Offer状态
<span class="fc" id="L132">        offer.setStatus(Offer.OfferStatus.ACCEPTED);</span>
<span class="fc" id="L133">        offer.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L134">        Offer updatedOffer = offerRepository.save(offer);</span>
        
        // 更新物品状态
<span class="fc" id="L137">        Item wantedItem = offer.getWantedItem();</span>
<span class="fc" id="L138">        wantedItem.setStatus(Item.ItemStatus.RESERVED);</span>
<span class="fc" id="L139">        itemRepository.save(wantedItem);</span>
        
        // 发送通知
<span class="fc" id="L142">        notificationService.createOfferAcceptedNotification(updatedOffer);</span>
        
<span class="fc" id="L144">        return new OfferResponse(updatedOffer);</span>
    }

    @Override
    @Transactional
    public OfferResponse rejectOffer(Long offerId) {
<span class="fc" id="L150">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L151">        Offer offer = offerRepository.findById(offerId)</span>
<span class="pc" id="L152">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为求购者
<span class="fc bfc" id="L155" title="All 2 branches covered.">        if (!offer.getRequester().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L156">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有求购者可以拒绝Offer&quot;);</span>
        }
        
        // 检查Offer状态
<span class="fc bfc" id="L160" title="All 2 branches covered.">        if (offer.getStatus() != Offer.OfferStatus.PENDING_ACCEPTANCE) {</span>
<span class="fc" id="L161">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有待接受的Offer可以被拒绝&quot;);</span>
        }
        
        // 更新Offer状态
<span class="fc" id="L165">        offer.setStatus(Offer.OfferStatus.REJECTED);</span>
<span class="fc" id="L166">        offer.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L167">        Offer updatedOffer = offerRepository.save(offer);</span>
        
        // 发送通知
<span class="fc" id="L170">        notificationService.createOfferRejectedNotification(updatedOffer);</span>
        
<span class="fc" id="L172">        return new OfferResponse(updatedOffer);</span>
    }

    @Override
    @Transactional
    public OfferResponse confirmOffer(Long offerId) {
<span class="fc" id="L178">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L179">        Offer offer = offerRepository.findById(offerId)</span>
<span class="pc" id="L180">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为响应者
<span class="fc bfc" id="L183" title="All 2 branches covered.">        if (!offer.getOfferer().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L184">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有响应者可以确认Offer&quot;);</span>
        }
        
        // 检查Offer状态
<span class="fc bfc" id="L188" title="All 2 branches covered.">        if (offer.getStatus() != Offer.OfferStatus.ACCEPTED) {</span>
<span class="fc" id="L189">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有已接受的Offer可以被确认&quot;);</span>
        }
        
        // 更新Offer状态
<span class="fc" id="L193">        offer.setStatus(Offer.OfferStatus.CONFIRMED);</span>
<span class="fc" id="L194">        offer.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L195">        Offer updatedOffer = offerRepository.save(offer);</span>
        
        // 发送通知
<span class="fc" id="L198">        notificationService.createOfferConfirmedNotification(updatedOffer);</span>
        
<span class="fc" id="L200">        return new OfferResponse(updatedOffer);</span>
    }

    @Override
    @Transactional
    public OfferResponse completeOffer(Long offerId) {
<span class="fc" id="L206">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L207">        Offer offer = offerRepository.findById(offerId)</span>
<span class="pc" id="L208">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为交易参与方
<span class="fc" id="L211">        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());</span>
<span class="fc" id="L212">        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());</span>
        
<span class="fc bfc" id="L214" title="All 4 branches covered.">        if (!isOfferer &amp;&amp; !isRequester) {</span>
<span class="fc" id="L215">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有交易参与方可以完成交易&quot;);</span>
        }
        
        // 检查Offer状态
<span class="fc bfc" id="L219" title="All 2 branches covered.">        if (offer.getStatus() != Offer.OfferStatus.CONFIRMED) {</span>
<span class="fc" id="L220">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只有已确认的Offer可以被标记为完成&quot;);</span>
        }
        
        // 更新Offer状态
<span class="fc" id="L224">        offer.setStatus(Offer.OfferStatus.COMPLETED);</span>
<span class="fc" id="L225">        offer.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L226">        Offer updatedOffer = offerRepository.save(offer);</span>
        
        // 更新物品状态
<span class="fc" id="L229">        Item wantedItem = offer.getWantedItem();</span>
<span class="fc" id="L230">        wantedItem.setStatus(Item.ItemStatus.SOLD);</span>
<span class="fc" id="L231">        itemRepository.save(wantedItem);</span>
        
        // 发送通知 - 使用通用的创建通知方法
<span class="fc" id="L234">        notificationService.createNotification(</span>
<span class="fc" id="L235">            offer.getRequester(), </span>
<span class="fc" id="L236">            offer.getOfferer(), </span>
            Notification.NotificationType.TRANSACTION_COMPLETED, 
<span class="fc" id="L238">            offer.getId(), </span>
            &quot;您的交易已完成&quot;
        );
        
<span class="fc" id="L242">        return new OfferResponse(updatedOffer);</span>
    }

    @Override
    @Transactional
    public OfferResponse cancelOffer(Long offerId) {
<span class="fc" id="L248">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L249">        Offer offer = offerRepository.findById(offerId)</span>
<span class="pc" id="L250">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为交易参与方
<span class="fc" id="L253">        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());</span>
<span class="fc" id="L254">        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());</span>
        
<span class="fc bfc" id="L256" title="All 4 branches covered.">        if (!isOfferer &amp;&amp; !isRequester) {</span>
<span class="fc" id="L257">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;只有交易参与方可以取消交易&quot;);</span>
        }
        
        // 检查Offer状态
<span class="fc bfc" id="L261" title="All 4 branches covered.">        if (offer.getStatus() == Offer.OfferStatus.COMPLETED || offer.getStatus() == Offer.OfferStatus.CANCELLED) {</span>
<span class="fc" id="L262">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;已完成或已取消的Offer不能被取消&quot;);</span>
        }
        
        // 更新Offer状态
<span class="fc" id="L266">        offer.setStatus(Offer.OfferStatus.CANCELLED);</span>
<span class="fc" id="L267">        offer.setUpdatedAt(LocalDateTime.now());</span>
<span class="fc" id="L268">        Offer updatedOffer = offerRepository.save(offer);</span>
        
        // 如果Offer已被接受，需要更新物品状态
<span class="pc bpc" id="L271" title="2 of 4 branches missed.">        if (offer.getStatus() == Offer.OfferStatus.ACCEPTED || offer.getStatus() == Offer.OfferStatus.CONFIRMED) {</span>
<span class="nc" id="L272">            Item wantedItem = offer.getWantedItem();</span>
<span class="nc" id="L273">            wantedItem.setStatus(Item.ItemStatus.FOR_SALE);</span>
<span class="nc" id="L274">            itemRepository.save(wantedItem);</span>
        }
        
        // 发送通知 - 使用通用的创建通知方法
<span class="fc bfc" id="L278" title="All 2 branches covered.">        User recipient = isOfferer ? offer.getRequester() : offer.getOfferer();</span>
<span class="fc" id="L279">        notificationService.createNotification(</span>
            recipient, 
            currentUser, 
            Notification.NotificationType.WANTED_OFFER_REJECTED, // 使用已有的通知类型
<span class="fc" id="L283">            offer.getId(), </span>
            &quot;交易已被取消&quot;
        );
        
<span class="fc" id="L287">        return new OfferResponse(updatedOffer);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OfferResponse&gt; getMyOffersByStatus(String status, Pageable pageable) {
<span class="nc" id="L293">        User currentUser = userService.getCurrentUser();</span>
<span class="nc" id="L294">        Offer.OfferStatus offerStatus = parseOfferStatus(status);</span>
<span class="nc" id="L295">        Page&lt;Offer&gt; offers = offerRepository.findByOffererAndStatus(currentUser, offerStatus, pageable);</span>
        
<span class="nc" id="L297">        return offers.map(OfferResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;OfferResponse&gt; getMyRequestsByStatus(String status, Pageable pageable) {
<span class="nc" id="L303">        User currentUser = userService.getCurrentUser();</span>
<span class="nc" id="L304">        Offer.OfferStatus offerStatus = parseOfferStatus(status);</span>
<span class="nc" id="L305">        Page&lt;Offer&gt; offers = offerRepository.findByRequesterAndStatus(currentUser, offerStatus, pageable);</span>
        
<span class="nc" id="L307">        return offers.map(OfferResponse::new);</span>
    }
    
    private Offer getOfferAndCheckPermission(Long offerId, User currentUser) {
<span class="fc" id="L311">        Offer offer = offerRepository.findById(offerId)</span>
<span class="fc" id="L312">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;Offer不存在&quot;));</span>
        
        // 验证当前用户是否为交易参与方
<span class="fc" id="L315">        boolean isOfferer = offer.getOfferer().getId().equals(currentUser.getId());</span>
<span class="fc" id="L316">        boolean isRequester = offer.getRequester().getId().equals(currentUser.getId());</span>
        
<span class="pc bpc" id="L318" title="1 of 4 branches missed.">        if (!isOfferer &amp;&amp; !isRequester) {</span>
<span class="fc" id="L319">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您无权访问此Offer&quot;);</span>
        }
        
<span class="fc" id="L322">        return offer;</span>
    }
    
    private Offer.OfferStatus parseOfferStatus(String status) {
        try {
<span class="nc" id="L327">            return Offer.OfferStatus.valueOf(status.toUpperCase());</span>
<span class="nc" id="L328">        } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L329">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的Offer状态&quot;);</span>
        }
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>