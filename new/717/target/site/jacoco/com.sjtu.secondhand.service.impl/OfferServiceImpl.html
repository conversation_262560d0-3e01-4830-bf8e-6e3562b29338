<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OfferServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">OfferServiceImpl</span></div><h1>OfferServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">90 of 633</td><td class="ctr2">85%</td><td class="bar">3 of 42</td><td class="ctr2">92%</td><td class="ctr1">11</td><td class="ctr2">43</td><td class="ctr1">14</td><td class="ctr2">130</td><td class="ctr1">8</td><td class="ctr2">22</td></tr></tfoot><tbody><tr><td id="a7"><a href="OfferServiceImpl.java.html#L293" class="el_method">getMyOffersByStatus(String, Pageable)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="19" alt="19"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="OfferServiceImpl.java.html#L303" class="el_method">getMyRequestsByStatus(String, Pageable)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="19" alt="19"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="OfferServiceImpl.java.html#L248" class="el_method">cancelOffer(Long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="107" height="10" title="93" alt="93"/></td><td class="ctr2" id="c13">89%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="102" height="10" title="12" alt="12"/></td><td class="ctr2" id="e5">85%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a20"><a href="OfferServiceImpl.java.html#L327" class="el_method">parseOfferStatus(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a13"><a href="OfferServiceImpl.java.html#L250" class="el_method">lambda$cancelOffer$0()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a14"><a href="OfferServiceImpl.java.html#L208" class="el_method">lambda$completeOffer$0()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a15"><a href="OfferServiceImpl.java.html#L180" class="el_method">lambda$confirmOffer$0()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a18"><a href="OfferServiceImpl.java.html#L152" class="el_method">lambda$rejectOffer$0()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="OfferServiceImpl.java.html#L119" class="el_method">lambda$acceptOffer$0()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="OfferServiceImpl.java.html#L206" class="el_method">completeOffer(Long)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="86" alt="86"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="OfferServiceImpl.java.html#L51" class="el_method">createOffer(Long, OfferRequest)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="77" alt="77"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="OfferServiceImpl.java.html#L117" class="el_method">acceptOffer(Long)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="77" height="10" title="67" alt="67"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i3">15</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a21"><a href="OfferServiceImpl.java.html#L150" class="el_method">rejectOffer(Long)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="64" height="10" title="56" alt="56"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="OfferServiceImpl.java.html#L178" class="el_method">confirmOffer(Long)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="64" height="10" title="56" alt="56"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">100%</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a10"><a href="OfferServiceImpl.java.html#L311" class="el_method">getOfferAndCheckPermission(Long, User)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="39" height="10" title="34" alt="34"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e6">75%</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a19"><a href="OfferServiceImpl.java.html#L34" class="el_method">OfferServiceImpl(OfferRepository, ItemRepository, UserService, NotificationService)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="15" alt="15"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a11"><a href="OfferServiceImpl.java.html#L90" class="el_method">getOfferById(Long)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a6"><a href="OfferServiceImpl.java.html#L99" class="el_method">getMyOffers(Pageable)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a8"><a href="OfferServiceImpl.java.html#L108" class="el_method">getMyRequests(Pageable)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a17"><a href="OfferServiceImpl.java.html#L312" class="el_method">lambda$getOfferAndCheckPermission$0()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a16"><a href="OfferServiceImpl.java.html#L55" class="el_method">lambda$createOffer$0()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a5"><a href="OfferServiceImpl.java.html#L45" class="el_method">createOffer(OfferRequest)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>