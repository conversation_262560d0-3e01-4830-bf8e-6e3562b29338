<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OrderServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">OrderServiceImpl</span></div><h1>OrderServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">85 of 584</td><td class="ctr2">85%</td><td class="bar">1 of 30</td><td class="ctr2">96%</td><td class="ctr1">8</td><td class="ctr2">35</td><td class="ctr1">15</td><td class="ctr2">127</td><td class="ctr1">7</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a7"><a href="OrderServiceImpl.java.html#L295" class="el_method">getMyBoughtOrdersByStatus(String, Pageable)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="19" alt="19"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="OrderServiceImpl.java.html#L305" class="el_method">getMySoldOrdersByStatus(String, Pageable)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="19" alt="19"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a19"><a href="OrderServiceImpl.java.html#L329" class="el_method">parseOrderStatus(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="OrderServiceImpl.java.html#L176" class="el_method">cancelOrder(Long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="89" alt="89"/></td><td class="ctr2" id="c11">93%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="7" alt="7"/></td><td class="ctr2" id="e5">87%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="OrderServiceImpl.java.html#L137" class="el_method">confirmOrder(Long)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="63" alt="63"/></td><td class="ctr2" id="c12">91%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a13"><a href="OrderServiceImpl.java.html#L253" class="el_method">lambda$completeOrder$0()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a14"><a href="OrderServiceImpl.java.html#L225" class="el_method">lambda$confirmContact$0()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a12"><a href="OrderServiceImpl.java.html#L178" class="el_method">lambda$cancelOrder$0()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="OrderServiceImpl.java.html#L139" class="el_method">lambda$confirmOrder$0()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="OrderServiceImpl.java.html#L54" class="el_method">createOrder(OrderRequest)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="103" alt="103"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i0">24</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="OrderServiceImpl.java.html#L251" class="el_method">completeOrder(Long)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="79" alt="79"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i2">19</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="OrderServiceImpl.java.html#L223" class="el_method">confirmContact(Long)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="64" height="10" title="55" alt="55"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a10"><a href="OrderServiceImpl.java.html#L314" class="el_method">getOrderAndCheckPermission(Long, User)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="30" alt="30"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">100%</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a18"><a href="OrderServiceImpl.java.html#L42" class="el_method">OrderServiceImpl(OrderRepository, ItemRepository, OfferRepository, UserService, NotificationService, NotificationEventService)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="21" alt="21"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a11"><a href="OrderServiceImpl.java.html#L110" class="el_method">getOrderById(Long)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a6"><a href="OrderServiceImpl.java.html#L119" class="el_method">getMyBoughtOrders(Pageable)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a8"><a href="OrderServiceImpl.java.html#L128" class="el_method">getMySoldOrders(Pageable)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="14" alt="14"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a17"><a href="OrderServiceImpl.java.html#L315" class="el_method">lambda$getOrderAndCheckPermission$0()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a16"><a href="OrderServiceImpl.java.html#L56" class="el_method">lambda$createOrder$0()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a4"><a href="OrderServiceImpl.java.html#L337" class="el_method">convertToOrderResponse(Order)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>