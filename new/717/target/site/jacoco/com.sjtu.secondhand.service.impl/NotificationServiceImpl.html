<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NotificationServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">NotificationServiceImpl</span></div><h1>NotificationServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">68 of 821</td><td class="ctr2">91%</td><td class="bar">20 of 48</td><td class="ctr2">58%</td><td class="ctr1">20</td><td class="ctr2">61</td><td class="ctr1">24</td><td class="ctr2">183</td><td class="ctr1">0</td><td class="ctr2">29</td></tr></tfoot><tbody><tr><td id="a16"><a href="NotificationServiceImpl.java.html#L354" class="el_method">getNotificationTitle(Notification.NotificationType)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="13" alt="13"/></td><td class="ctr2" id="c28">31%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">22%</td><td class="ctr1" id="f0">14</td><td class="ctr2" id="g0">18</td><td class="ctr1" id="h0">14</td><td class="ctr2" id="i2">19</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a24"><a href="NotificationServiceImpl.java.html#L289" class="el_method">mapToDto(Notification)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="106" height="10" title="127" alt="127"/></td><td class="ctr2" id="c25">88%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">62%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i0">31</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="NotificationServiceImpl.java.html#L185" class="el_method">createOrderConfirmedNotification(Order)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="73" height="10" title="88" alt="88"/></td><td class="ctr2" id="c26">86%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i1">24</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="NotificationServiceImpl.java.html#L51" class="el_method">createJsonContent(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="51" alt="51"/></td><td class="ctr2" id="c24">91%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="NotificationServiceImpl.java.html#L95" class="el_method">createNotification(User, User, Notification.NotificationType, Long, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="28" alt="28"/></td><td class="ctr2" id="c27">84%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="NotificationServiceImpl.java.html#L224" class="el_method">createOrderCancelledNotification(Order, User)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="38" alt="38"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a26"><a href="NotificationServiceImpl.java.html#L127" class="el_method">markAsRead(Long, Long)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="34" alt="34"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a25"><a href="NotificationServiceImpl.java.html#L141" class="el_method">markAllAsRead(Long)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="34" alt="34"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a5"><a href="NotificationServiceImpl.java.html#L77" class="el_method">createNotification(User, String, Notification.NotificationType, Long)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="33" alt="33"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a14"><a href="NotificationServiceImpl.java.html#L153" class="el_method">deleteNotification(Long, Long)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="25" alt="25"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="NotificationServiceImpl.java.html#L262" class="el_method">createOfferAcceptedNotification(Offer)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="NotificationServiceImpl.java.html#L271" class="el_method">createOfferRejectedNotification(Offer)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="24" alt="24"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">6</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="NotificationServiceImpl.java.html#L253" class="el_method">createNewOfferNotification(Offer)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="23" alt="23"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="NotificationServiceImpl.java.html#L280" class="el_method">createOfferConfirmedNotification(Offer)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="23" alt="23"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i14">4</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a15"><a href="NotificationServiceImpl.java.html#L111" class="el_method">getAllNotifications(Long)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i15">4</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a17"><a href="NotificationServiceImpl.java.html#L119" class="el_method">getUnreadNotifications(Long)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i16">4</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a4"><a href="NotificationServiceImpl.java.html#L178" class="el_method">createNewOrderNotification(Order)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i17">4</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a11"><a href="NotificationServiceImpl.java.html#L239" class="el_method">createOrderCompletedNotification(Order)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i18">4</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a1"><a href="NotificationServiceImpl.java.html#L246" class="el_method">createContactConfirmedNotification(Order)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i19">4</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a27"><a href="NotificationServiceImpl.java.html#L40" class="el_method">NotificationServiceImpl(NotificationRepository, UserRepository, ObjectMapper, NotificationEventService)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="15" alt="15"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a0"><a href="NotificationServiceImpl.java.html#L165" class="el_method">countUnreadNotifications(Long)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="13" alt="13"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">3</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a13"><a href="NotificationServiceImpl.java.html#L172" class="el_method">createRatingReceivedNotification(Long, User, User)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="13" alt="13"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i21">3</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a18"><a href="NotificationServiceImpl.java.html#L166" class="el_method">lambda$countUnreadNotifications$0()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a19"><a href="NotificationServiceImpl.java.html#L154" class="el_method">lambda$deleteNotification$0()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a22"><a href="NotificationServiceImpl.java.html#L142" class="el_method">lambda$markAllAsRead$0()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a23"><a href="NotificationServiceImpl.java.html#L128" class="el_method">lambda$markAsRead$0()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a21"><a href="NotificationServiceImpl.java.html#L120" class="el_method">lambda$getUnreadNotifications$0()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a20"><a href="NotificationServiceImpl.java.html#L112" class="el_method">lambda$getAllNotifications$0()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a28"><a href="NotificationServiceImpl.java.html#L29" class="el_method">static {...}</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>