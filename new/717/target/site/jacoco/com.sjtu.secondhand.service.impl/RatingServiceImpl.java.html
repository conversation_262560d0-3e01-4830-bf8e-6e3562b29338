<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RatingServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">RatingServiceImpl.java</span></div><h1>RatingServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.RatingRequest;
import com.sjtu.secondhand.dto.response.RatingResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.Rating;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.RatingRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.NotificationService;
import com.sjtu.secondhand.service.RatingService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RatingServiceImpl implements RatingService {

    private final RatingRepository ratingRepository;
    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final UserService userService;
    private final NotificationService notificationService;

    public RatingServiceImpl(RatingRepository ratingRepository,
            UserRepository userRepository,
            OrderRepository orderRepository,
            UserService userService,
<span class="fc" id="L34">            NotificationService notificationService) {</span>
<span class="fc" id="L35">        this.ratingRepository = ratingRepository;</span>
<span class="fc" id="L36">        this.userRepository = userRepository;</span>
<span class="fc" id="L37">        this.orderRepository = orderRepository;</span>
<span class="fc" id="L38">        this.userService = userService;</span>
<span class="fc" id="L39">        this.notificationService = notificationService;</span>
<span class="fc" id="L40">    }</span>

    @Override
    @Transactional
    public RatingResponse createRating(RatingRequest ratingRequest) {
<span class="fc" id="L45">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L46">        Rating rating = new Rating();</span>
<span class="fc" id="L47">        rating.setRater(currentUser);</span>
<span class="fc" id="L48">        rating.setScore(ratingRequest.getScore().byteValue());</span>
        
        // 设置评价类型
        Rating.TransactionType transactionType;
<span class="fc bfc" id="L52" title="All 2 branches covered.">        if (&quot;IDLE&quot;.equals(ratingRequest.getTransaction_type())) {</span>
<span class="fc" id="L53">            transactionType = Rating.TransactionType.IDLE;</span>
<span class="fc bfc" id="L54" title="All 2 branches covered.">        } else if (&quot;WANTED&quot;.equals(ratingRequest.getTransaction_type())) {</span>
<span class="fc" id="L55">            transactionType = Rating.TransactionType.WANTED;</span>
        } else {
<span class="fc" id="L57">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的交易类型&quot;);</span>
        }
<span class="fc" id="L59">        rating.setTransactionType(transactionType);</span>

        User ratee;
<span class="fc" id="L62">        Long transactionId = ratingRequest.getRelated_transaction_id().longValue();</span>
<span class="fc" id="L63">        rating.setRelatedTransactionId(transactionId);</span>

        // 处理订单评价
<span class="fc" id="L66">        Order order = orderRepository.findById(transactionId)</span>
<span class="fc" id="L67">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;订单不存在&quot;));</span>

        // 验证订单状态
<span class="fc bfc" id="L70" title="All 2 branches covered.">        if (order.getStatus() != Order.OrderStatus.COMPLETED) {</span>
<span class="fc" id="L71">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;只能评价已完成的订单&quot;);</span>
        }

        // 检查是否已经评价
<span class="fc bfc" id="L75" title="All 2 branches covered.">        if (ratingRepository.existsByRelatedTransactionIdAndRater(transactionId, currentUser)) {</span>
<span class="fc" id="L76">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;您已经对该交易进行了评价&quot;);</span>
        }

        // 根据当前用户是买家还是卖家来确定被评价人
<span class="fc bfc" id="L80" title="All 2 branches covered.">        if (currentUser.getId().equals(order.getBuyer().getId())) {</span>
            // 当前用户是买家，评价卖家
<span class="fc" id="L82">            ratee = order.getSeller();</span>
<span class="fc" id="L83">            order.setIsBuyerRated(true);</span>
<span class="fc bfc" id="L84" title="All 2 branches covered.">        } else if (currentUser.getId().equals(order.getSeller().getId())) {</span>
            // 当前用户是卖家，评价买家
<span class="fc" id="L86">            ratee = order.getBuyer();</span>
<span class="fc" id="L87">            order.setIsSellerRated(true);</span>
        } else {
<span class="fc" id="L89">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您不是该订单的买家或卖家&quot;);</span>
        }

        // 更新订单评价状态
<span class="fc" id="L93">        orderRepository.save(order);</span>

<span class="fc" id="L95">        rating.setRatee(ratee);</span>

        // 保存评价
<span class="fc" id="L98">        Rating savedRating = ratingRepository.save(rating);</span>

        // 更新用户信用分数
<span class="fc" id="L101">        updateUserCreditScore(ratee.getId(), rating.getScore());</span>

        // 发送通知
<span class="fc" id="L104">        notificationService.createRatingReceivedNotification(savedRating.getId(), ratee, currentUser);</span>

<span class="fc" id="L106">        return new RatingResponse(savedRating);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public RatingResponse getRatingById(Long ratingId) {
<span class="fc" id="L112">        Rating rating = ratingRepository.findById(ratingId)</span>
<span class="fc" id="L113">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;评价不存在&quot;));</span>

<span class="fc" id="L115">        return new RatingResponse(rating);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getMyRatings(Pageable pageable) {
<span class="fc" id="L121">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L122">        Page&lt;Rating&gt; ratings = ratingRepository.findByRater(currentUser, pageable);</span>

<span class="fc" id="L124">        return ratings.map(RatingResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getRatingsAboutMe(Pageable pageable) {
<span class="fc" id="L130">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L131">        Page&lt;Rating&gt; ratings = ratingRepository.findByRatee(currentUser, pageable);</span>

<span class="fc" id="L133">        return ratings.map(RatingResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getUserRatings(Long userId, Pageable pageable) {
<span class="fc" id="L139">        User user = userRepository.findById(userId)</span>
<span class="pc" id="L140">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>

<span class="nc" id="L142">        Page&lt;Rating&gt; ratings = ratingRepository.findByRatee(user, pageable);</span>

<span class="nc" id="L144">        return ratings.map(RatingResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getMyRatingsByTransactionType(String type, Pageable pageable) {
<span class="fc" id="L150">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L151">        Rating.TransactionType transactionType = parseTransactionType(type);</span>

<span class="fc" id="L153">        Page&lt;Rating&gt; ratings = ratingRepository.findByRaterAndTransactionType(currentUser, transactionType, pageable);</span>

<span class="fc" id="L155">        return ratings.map(RatingResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getRatingsAboutMeByTransactionType(String type, Pageable pageable) {
<span class="fc" id="L161">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L162">        Rating.TransactionType transactionType = parseTransactionType(type);</span>

<span class="fc" id="L164">        Page&lt;Rating&gt; ratings = ratingRepository.findByRateeAndTransactionType(currentUser, transactionType, pageable);</span>

<span class="fc" id="L166">        return ratings.map(RatingResponse::new);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Double getUserAverageRating(Long userId) {
<span class="fc" id="L172">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L173">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>

<span class="fc" id="L175">        Double averageRating = ratingRepository.calculateAverageRating(user);</span>

<span class="fc bfc" id="L177" title="All 2 branches covered.">        return averageRating != null ? averageRating : 0.0;</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;RatingResponse&gt; getPendingOrderRatings(Pageable pageable) {
<span class="fc" id="L183">        User currentUser = userService.getCurrentUser();</span>

        // 获取当前用户作为买家但未评价的已完成订单
<span class="fc" id="L186">        Page&lt;Order&gt; buyerOrders = orderRepository.findByBuyerAndStatusAndIsBuyerRatedFalse(</span>
                currentUser, Order.OrderStatus.COMPLETED, pageable);

        // 获取当前用户作为卖家但未评价的已完成订单
<span class="fc" id="L190">        Page&lt;Order&gt; sellerOrders = orderRepository.findBySellerAndStatusAndIsSellerRatedFalse(</span>
                currentUser, Order.OrderStatus.COMPLETED, pageable);

        // 这里需要合并两个结果，但由于分页的复杂性，实际实现可能需要自定义查询
        // 这里只是一个简化的示例
<span class="nc" id="L195">        return buyerOrders.map(order -&gt; new RatingResponse(new Rating()));</span>
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasRatedTransaction(Long transactionId) {
<span class="fc" id="L201">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L202">        return ratingRepository.existsByRelatedTransactionIdAndRater(transactionId, currentUser);</span>
    }

    // 辅助方法：更新用户信用分数
    private void updateUserCreditScore(Long userId, Byte ratingScore) {
<span class="fc" id="L207">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L208">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>

        // 获取用户当前的信用分数
<span class="fc" id="L211">        Integer currentScore = user.getCreditScore();</span>

        // 根据评分调整信用分数
        // 这里使用一个简单的算法：
        // 5分：+2分
        // 4分：+1分
        // 3分：+0分
        // 2分：-1分
        // 1分：-2分
        int adjustment;
<span class="fc bfc" id="L221" title="All 6 branches covered.">        switch (ratingScore) {</span>
            case 5:
<span class="fc" id="L223">                adjustment = 2;</span>
<span class="fc" id="L224">                break;</span>
            case 4:
<span class="fc" id="L226">                adjustment = 1;</span>
<span class="fc" id="L227">                break;</span>
            case 3:
<span class="fc" id="L229">                adjustment = 0;</span>
<span class="fc" id="L230">                break;</span>
            case 2:
<span class="fc" id="L232">                adjustment = -1;</span>
<span class="fc" id="L233">                break;</span>
            case 1:
<span class="fc" id="L235">                adjustment = -2;</span>
<span class="fc" id="L236">                break;</span>
            default:
<span class="fc" id="L238">                adjustment = 0;</span>
        }

        // 更新信用分数，确保在0-100之间
<span class="fc" id="L242">        int newScore = Math.min(100, Math.max(0, currentScore + adjustment));</span>
<span class="fc" id="L243">        user.setCreditScore(newScore);</span>

<span class="fc" id="L245">        userRepository.save(user);</span>
<span class="fc" id="L246">    }</span>

    // 辅助方法：解析交易类型
    private Rating.TransactionType parseTransactionType(String type) {
<span class="fc bfc" id="L250" title="All 2 branches covered.">        if (&quot;IDLE&quot;.equalsIgnoreCase(type)) {</span>
<span class="fc" id="L251">            return Rating.TransactionType.IDLE;</span>
<span class="fc bfc" id="L252" title="All 2 branches covered.">        } else if (&quot;WANTED&quot;.equalsIgnoreCase(type)) {</span>
<span class="fc" id="L253">            return Rating.TransactionType.WANTED;</span>
        } else {
            try {
<span class="nc" id="L256">                return Rating.TransactionType.valueOf(type.toUpperCase());</span>
<span class="fc" id="L257">            } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L258">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的交易类型&quot;);</span>
            }
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>