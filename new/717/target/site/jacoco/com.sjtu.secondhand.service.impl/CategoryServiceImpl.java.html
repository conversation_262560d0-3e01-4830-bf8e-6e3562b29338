<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CategoryServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">CategoryServiceImpl.java</span></div><h1>CategoryServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CategoryServiceImpl implements CategoryService {

    private final CategoryRepository categoryRepository;

    @Autowired
<span class="fc" id="L20">    public CategoryServiceImpl(CategoryRepository categoryRepository) {</span>
<span class="fc" id="L21">        this.categoryRepository = categoryRepository;</span>
<span class="fc" id="L22">    }</span>

    @Override
    public List&lt;Category&gt; getAllCategories() {
<span class="fc" id="L26">        return categoryRepository.findAll();</span>
    }

    @Override
    public List&lt;Category&gt; getCategoryTree() {
<span class="fc" id="L31">        List&lt;Category&gt; allCategories = categoryRepository.findAll();</span>
<span class="fc" id="L32">        List&lt;Category&gt; rootCategories = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L33">        Map&lt;Integer, Category&gt; categoryMap = new HashMap&lt;&gt;();</span>

        // 将所有分类添加到map中
<span class="fc bfc" id="L36" title="All 2 branches covered.">        for (Category category : allCategories) {</span>
<span class="fc" id="L37">            categoryMap.put(category.getId(), category);</span>
<span class="fc" id="L38">        }</span>

        // 构建树形结构
<span class="fc bfc" id="L41" title="All 2 branches covered.">        for (Category category : allCategories) {</span>
<span class="fc bfc" id="L42" title="All 2 branches covered.">            if (category.getParent() == null) {</span>
<span class="fc" id="L43">                rootCategories.add(category);</span>
            } else {
<span class="fc" id="L45">                Category parent = category.getParent();</span>
<span class="pc bpc" id="L46" title="1 of 2 branches missed.">                if (parent != null) {</span>
<span class="fc" id="L47">                    parent.addChild(category);</span>
                }
            }
<span class="fc" id="L50">        }</span>

<span class="fc" id="L52">        return rootCategories;</span>
    }

    @Override
    public Category getCategoryById(Long id) {
        // 将Long类型转换为Integer类型
<span class="fc" id="L58">        Integer idInt = id.intValue();</span>
<span class="fc" id="L59">        return categoryRepository.findById(idInt)</span>
<span class="fc" id="L60">                .orElseThrow(() -&gt; new RuntimeException(&quot;分类不存在&quot;));</span>
    }

    @Override
    public Category createCategory(Category category) {
<span class="fc" id="L65">        return categoryRepository.save(category);</span>
    }

    @Override
    public Category updateCategory(Long id, Category categoryDetails) {
        // 将Long类型转换为Integer类型
<span class="fc" id="L71">        Integer idInt = id.intValue();</span>
<span class="fc" id="L72">        Category category = categoryRepository.findById(idInt)</span>
<span class="pc" id="L73">                .orElseThrow(() -&gt; new RuntimeException(&quot;分类不存在&quot;));</span>
<span class="fc" id="L74">        category.setName(categoryDetails.getName());</span>
<span class="fc" id="L75">        category.setParent(categoryDetails.getParent());</span>
<span class="fc" id="L76">        return categoryRepository.save(category);</span>
    }

    @Override
    public void deleteCategory(Long id) {
        // 将Long类型转换为Integer类型
<span class="fc" id="L82">        Integer idInt = id.intValue();</span>
<span class="fc" id="L83">        Category category = categoryRepository.findById(idInt)</span>
<span class="pc" id="L84">                .orElseThrow(() -&gt; new RuntimeException(&quot;分类不存在&quot;));</span>
<span class="fc" id="L85">        categoryRepository.delete(category);</span>
<span class="fc" id="L86">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>