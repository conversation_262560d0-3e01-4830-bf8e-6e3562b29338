<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FootprintServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">FootprintServiceImpl.java</span></div><h1>FootprintServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Footprint;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.FootprintRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.FootprintService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FootprintServiceImpl implements FootprintService {

<span class="fc" id="L29">    private static final Logger log = LoggerFactory.getLogger(FootprintServiceImpl.class);</span>

    private final FootprintRepository footprintRepository;
    private final UserRepository userRepository;
    private final ItemRepository itemRepository;
    
<span class="fc" id="L35">    public FootprintServiceImpl(FootprintRepository footprintRepository, UserRepository userRepository, ItemRepository itemRepository) {</span>
<span class="fc" id="L36">        this.footprintRepository = footprintRepository;</span>
<span class="fc" id="L37">        this.userRepository = userRepository;</span>
<span class="fc" id="L38">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L39">    }</span>

    @Override
    @Transactional
    public boolean recordFootprint(Long userId, Long itemId) {
        try {
<span class="fc" id="L45">            User user = userRepository.findById(userId)</span>
<span class="fc" id="L46">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>

<span class="fc" id="L48">            Item item = itemRepository.findById(itemId)</span>
<span class="fc" id="L49">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;商品不存在&quot;));</span>

            // 查询是否已有足迹记录
<span class="fc" id="L52">            Optional&lt;Footprint&gt; existingFootprint = footprintRepository.findByUserAndItemId(user, itemId);</span>
            
<span class="fc bfc" id="L54" title="All 2 branches covered.">            if (existingFootprint.isPresent()) {</span>
                // 更新已有记录的时间
<span class="fc" id="L56">                Footprint footprint = existingFootprint.get();</span>
<span class="fc" id="L57">                footprint.setViewTime(LocalDateTime.now());</span>
<span class="fc" id="L58">                footprintRepository.save(footprint);</span>
<span class="fc" id="L59">            } else {</span>
                // 创建新的足迹记录
<span class="fc" id="L61">                Footprint footprint = Footprint.builder()</span>
<span class="fc" id="L62">                    .user(user)</span>
<span class="fc" id="L63">                    .item(item)</span>
<span class="fc" id="L64">                    .viewTime(LocalDateTime.now())</span>
<span class="fc" id="L65">                    .build();</span>
<span class="fc" id="L66">                footprintRepository.save(footprint);</span>
            }
            
<span class="fc" id="L69">            return true;</span>
<span class="fc" id="L70">        } catch (Exception e) {</span>
<span class="fc" id="L71">            log.error(&quot;记录足迹失败&quot;, e);</span>
<span class="fc" id="L72">            return false;</span>
        }
    }

    @Override
    public ItemPageResponse getUserFootprints(Long userId, Pageable pageable) {
<span class="fc" id="L78">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L79">            .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>

        // 查询足迹
<span class="fc" id="L82">        Page&lt;Footprint&gt; footprintPage = footprintRepository.findByUserOrderByViewTimeDesc(user, pageable);</span>
        
        // 转换为商品列表
<span class="fc" id="L85">        List&lt;ItemResponse&gt; items = footprintPage.getContent().stream()</span>
<span class="fc" id="L86">            .map(footprint -&gt; {</span>
<span class="fc" id="L87">                Item item = footprint.getItem();</span>
<span class="fc" id="L88">                ItemResponse response = new ItemResponse(item);</span>
                // 设置查看时间
<span class="fc" id="L90">                response.setViewTime(footprint.getViewTime());</span>
<span class="fc" id="L91">                return response;</span>
            })
<span class="fc" id="L93">            .collect(Collectors.toList());</span>
            
        // 构建分页响应
<span class="fc" id="L96">        return ItemPageResponse.builder()</span>
<span class="fc" id="L97">            .items(items)</span>
<span class="fc" id="L98">            .totalPages(footprintPage.getTotalPages())</span>
<span class="fc" id="L99">            .totalItems(footprintPage.getTotalElements())</span>
<span class="fc" id="L100">            .currentPage(pageable.getPageNumber())</span>
<span class="fc" id="L101">            .build();</span>
    }

    @Override
    @Transactional
    public boolean deleteFootprint(Long userId, Long itemId) {
        try {
<span class="fc" id="L108">            User user = userRepository.findById(userId)</span>
<span class="fc" id="L109">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
                
<span class="fc" id="L111">            footprintRepository.deleteByUserAndItemId(user, itemId);</span>
<span class="fc" id="L112">            return true;</span>
<span class="fc" id="L113">        } catch (Exception e) {</span>
<span class="fc" id="L114">            log.error(&quot;删除足迹失败&quot;, e);</span>
<span class="fc" id="L115">            return false;</span>
        }
    }

    @Override
    @Transactional
    public boolean clearAllFootprints(Long userId) {
        try {
<span class="fc" id="L123">            User user = userRepository.findById(userId)</span>
<span class="fc" id="L124">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
                
<span class="fc" id="L126">            footprintRepository.deleteAllByUser(user);</span>
<span class="fc" id="L127">            return true;</span>
<span class="fc" id="L128">        } catch (Exception e) {</span>
<span class="fc" id="L129">            log.error(&quot;清空足迹失败&quot;, e);</span>
<span class="fc" id="L130">            return false;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>