<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileStorageServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">FileStorageServiceImpl.java</span></div><h1>FileStorageServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.service.FileStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Service
<span class="fc" id="L23">public class FileStorageServiceImpl implements FileStorageService {</span>

    @Value(&quot;${file.upload-dir:uploads}&quot;)
    private String uploadDir;

    private Path fileStoragePath;

    @Override
    @PostConstruct
    public void init() {
<span class="fc" id="L33">        this.fileStoragePath = Paths.get(uploadDir).toAbsolutePath().normalize();</span>
        try {
<span class="fc" id="L35">            Files.createDirectories(this.fileStoragePath);</span>
<span class="nc" id="L36">        } catch (Exception ex) {</span>
<span class="nc" id="L37">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;无法创建文件上传目录&quot;);</span>
<span class="fc" id="L38">        }</span>
<span class="fc" id="L39">    }</span>

    // Method for testing purposes
    public void initForTest(Path path) {
<span class="fc" id="L43">        this.fileStoragePath = path;</span>
        try {
<span class="fc" id="L45">            Files.createDirectories(this.fileStoragePath);</span>
<span class="nc" id="L46">        } catch (Exception ex) {</span>
<span class="nc" id="L47">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;无法创建测试文件上传目录&quot;);</span>
<span class="fc" id="L48">        }</span>
<span class="fc" id="L49">    }</span>

    @Override
    public String storeFile(MultipartFile file) {
<span class="fc" id="L53">        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());</span>
<span class="fc bfc" id="L54" title="All 2 branches covered.">        if (originalFilename.contains(&quot;..&quot;)) {</span>
<span class="fc" id="L55">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;文件名包含无效路径序列 &quot; + originalFilename);</span>
        }

<span class="fc" id="L58">        String fileExtension = &quot;&quot;;</span>
<span class="pc bpc" id="L59" title="1 of 2 branches missed.">        if (originalFilename.contains(&quot;.&quot;)) {</span>
<span class="fc" id="L60">            fileExtension = originalFilename.substring(originalFilename.lastIndexOf(&quot;.&quot;));</span>
        }

<span class="fc" id="L63">        String filename = UUID.randomUUID().toString() + fileExtension;</span>
<span class="fc" id="L64">        Path targetLocation = fileStoragePath.resolve(filename);</span>

        try {
<span class="fc" id="L67">            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);</span>
<span class="nc" id="L68">        } catch (IOException ex) {</span>
<span class="nc" id="L69">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;无法存储文件 &quot; + filename);</span>
<span class="fc" id="L70">        }</span>

<span class="fc" id="L72">        return filename;</span>
    }

    @Override
    public Resource loadFileAsResource(String filename) {
        try {
<span class="fc" id="L78">            Path filePath = fileStoragePath.resolve(filename).normalize();</span>
<span class="fc" id="L79">            Resource resource = new UrlResource(filePath.toUri());</span>
<span class="fc bfc" id="L80" title="All 2 branches covered.">            if (resource.exists()) {</span>
<span class="fc" id="L81">                return resource;</span>
            } else {
<span class="fc" id="L83">                throw new ApiException(HttpStatus.NOT_FOUND, &quot;文件不存在 &quot; + filename);</span>
            }
<span class="nc" id="L85">        } catch (MalformedURLException ex) {</span>
<span class="nc" id="L86">            throw new ApiException(HttpStatus.NOT_FOUND, &quot;文件不存在 &quot; + filename);</span>
        }
    }

    @Override
    public boolean deleteFile(String filename) {
        try {
<span class="fc" id="L93">            Path filePath = fileStoragePath.resolve(filename).normalize();</span>
<span class="fc" id="L94">            return Files.deleteIfExists(filePath);</span>
<span class="nc" id="L95">        } catch (IOException ex) {</span>
<span class="nc" id="L96">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;无法删除文件 &quot; + filename);</span>
        }
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>