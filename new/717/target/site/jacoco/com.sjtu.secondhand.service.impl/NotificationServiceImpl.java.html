<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NotificationServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">NotificationServiceImpl.java</span></div><h1>NotificationServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sjtu.secondhand.dto.response.NotificationDto;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Notification;
import com.sjtu.secondhand.model.Notification.NotificationType;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.Offer;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.NotificationRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.NotificationEventService;
import com.sjtu.secondhand.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class NotificationServiceImpl implements NotificationService {

<span class="fc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);</span>
    private final NotificationRepository notificationRepository;
    private final UserRepository userRepository;
    private final ObjectMapper objectMapper;
    private final NotificationEventService notificationEventService;

    @Autowired
    public NotificationServiceImpl(
            NotificationRepository notificationRepository,
            UserRepository userRepository,
            ObjectMapper objectMapper,
<span class="fc" id="L40">            NotificationEventService notificationEventService) {</span>
<span class="fc" id="L41">        this.notificationRepository = notificationRepository;</span>
<span class="fc" id="L42">        this.userRepository = userRepository;</span>
<span class="fc" id="L43">        this.objectMapper = objectMapper;</span>
<span class="fc" id="L44">        this.notificationEventService = notificationEventService;</span>
<span class="fc" id="L45">    }</span>

    // 辅助方法：创建JSON格式的通知内容
    private String createJsonContent(String message) {
        try {
            // 检查message是否已经是有效的JSON格式
<span class="fc bfc" id="L51" title="All 6 branches covered.">            if (message != null &amp;&amp; message.trim().startsWith(&quot;{&quot;) &amp;&amp; message.trim().endsWith(&quot;}&quot;)) {</span>
                try {
                    // 验证是否为有效JSON
<span class="fc" id="L54">                    objectMapper.readTree(message);</span>
<span class="fc" id="L55">                    return message; // 如果已经是有效JSON，直接返回</span>
<span class="nc" id="L56">                } catch (Exception e) {</span>
                    // 如果解析失败，则继续按普通字符串处理
<span class="nc" id="L58">                    logger.warn(&quot;输入的消息看起来像JSON但解析失败，将作为普通文本处理: {}&quot;, message);</span>
                }
            }

            // 创建一个新的JSON对象
<span class="fc" id="L63">            ObjectNode contentNode = objectMapper.createObjectNode();</span>
<span class="fc" id="L64">            contentNode.put(&quot;message&quot;, message);</span>
<span class="fc" id="L65">            contentNode.put(&quot;timestamp&quot;, System.currentTimeMillis());</span>
<span class="fc" id="L66">            return objectMapper.writeValueAsString(contentNode);</span>
<span class="fc" id="L67">        } catch (Exception e) {</span>
<span class="fc" id="L68">            logger.error(&quot;创建通知内容失败&quot;, e);</span>
<span class="fc" id="L69">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;创建通知内容失败: &quot; + e.getMessage());</span>
        }
    }

    @Override
    public NotificationDto createNotification(User recipient, String content,
            NotificationType type, Long relatedEntityId) {
        // 确保content是有效的JSON字符串
<span class="fc" id="L77">        String jsonContent = createJsonContent(content);</span>
<span class="fc" id="L78">        Notification notification = new Notification(recipient, null, type, relatedEntityId, jsonContent);</span>
<span class="fc" id="L79">        Notification savedNotification = notificationRepository.save(notification);</span>

        // Send real-time notification via SSE
        try {
<span class="fc" id="L83">            notificationEventService.sendNotificationEvent(savedNotification);</span>
<span class="fc" id="L84">        } catch (Exception e) {</span>
<span class="fc" id="L85">            logger.error(&quot;Failed to send notification event&quot;, e);</span>
<span class="fc" id="L86">        }</span>

<span class="fc" id="L88">        return mapToDto(savedNotification);</span>
    }

    @Override
    public NotificationDto createNotification(User recipient, User sender, NotificationType type,
            Long relatedEntityId, String content) {
        // 确保content是有效的JSON字符串
<span class="fc" id="L95">        String jsonContent = createJsonContent(content);</span>
<span class="fc" id="L96">        Notification notification = new Notification(recipient, sender, type, relatedEntityId, jsonContent);</span>
<span class="fc" id="L97">        Notification savedNotification = notificationRepository.save(notification);</span>

        // Send real-time notification via SSE
        try {
<span class="fc" id="L101">            notificationEventService.sendNotificationEvent(savedNotification);</span>
<span class="nc" id="L102">        } catch (Exception e) {</span>
<span class="nc" id="L103">            logger.error(&quot;Failed to send notification event&quot;, e);</span>
<span class="fc" id="L104">        }</span>

<span class="fc" id="L106">        return mapToDto(savedNotification);</span>
    }

    @Override
    public List&lt;NotificationDto&gt; getAllNotifications(Long userId) {
<span class="fc" id="L111">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L112">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
<span class="fc" id="L113">        List&lt;Notification&gt; notifications = notificationRepository.findByRecipient(user);</span>
<span class="fc" id="L114">        return notifications.stream().map(this::mapToDto).collect(Collectors.toList());</span>
    }

    @Override
    public List&lt;NotificationDto&gt; getUnreadNotifications(Long userId) {
<span class="fc" id="L119">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L120">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
<span class="fc" id="L121">        List&lt;Notification&gt; notifications = notificationRepository.findByRecipientAndIsReadFalse(user);</span>
<span class="fc" id="L122">        return notifications.stream().map(this::mapToDto).collect(Collectors.toList());</span>
    }

    @Override
    public NotificationDto markAsRead(Long notificationId, Long userId) {
<span class="fc" id="L127">        Notification notification = notificationRepository.findById(notificationId)</span>
<span class="fc" id="L128">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;通知不存在&quot;));</span>

<span class="fc bfc" id="L130" title="All 2 branches covered.">        if (!notification.getRecipient().getId().equals(userId)) {</span>
<span class="fc" id="L131">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您无权操作此通知&quot;);</span>
        }

<span class="fc" id="L134">        notification.setIsRead(true);</span>
<span class="fc" id="L135">        Notification updatedNotification = notificationRepository.save(notification);</span>
<span class="fc" id="L136">        return mapToDto(updatedNotification);</span>
    }

    @Override
    public void markAllAsRead(Long userId) {
<span class="fc" id="L141">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L142">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
<span class="fc" id="L143">        List&lt;Notification&gt; unreadNotifications = notificationRepository.findByRecipientAndIsReadFalse(user);</span>

<span class="fc bfc" id="L145" title="All 2 branches covered.">        for (Notification notification : unreadNotifications) {</span>
<span class="fc" id="L146">            notification.setIsRead(true);</span>
<span class="fc" id="L147">            notificationRepository.save(notification);</span>
<span class="fc" id="L148">        }</span>
<span class="fc" id="L149">    }</span>

    @Override
    public void deleteNotification(Long notificationId, Long userId) {
<span class="fc" id="L153">        Notification notification = notificationRepository.findById(notificationId)</span>
<span class="fc" id="L154">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;通知不存在&quot;));</span>

<span class="fc bfc" id="L156" title="All 2 branches covered.">        if (!notification.getRecipient().getId().equals(userId)) {</span>
<span class="fc" id="L157">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您无权操作此通知&quot;);</span>
        }

<span class="fc" id="L160">        notificationRepository.delete(notification);</span>
<span class="fc" id="L161">    }</span>

    @Override
    public long countUnreadNotifications(Long userId) {
<span class="fc" id="L165">        User user = userRepository.findById(userId)</span>
<span class="fc" id="L166">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;用户不存在&quot;));</span>
<span class="fc" id="L167">        return notificationRepository.countByRecipientAndIsReadFalse(user);</span>
    }

    @Override
    public void createRatingReceivedNotification(Long ratingId, User ratee, User rater) {
<span class="fc" id="L172">        String content = rater.getUsername() + &quot; 给您做了评价&quot;;</span>
<span class="fc" id="L173">        createNotification(ratee, rater, NotificationType.NEW_RATING_RECEIVED, ratingId, content);</span>
<span class="fc" id="L174">    }</span>

    @Override
    public void createNewOrderNotification(Order order) {
<span class="fc" id="L178">        User seller = order.getSeller();</span>
<span class="fc" id="L179">        String content = &quot;用户 &quot; + order.getBuyer().getUsername() + &quot; 预订了您的商品 \&quot;&quot; + order.getItem().getName() + &quot;\&quot;&quot;;</span>
<span class="fc" id="L180">        createNotification(seller, order.getBuyer(), NotificationType.IDLE_NEW_ORDER, order.getId(), content);</span>
<span class="fc" id="L181">    }</span>

    @Override
    public void createOrderConfirmedNotification(Order order) {
<span class="fc" id="L185">        User buyer = order.getBuyer();</span>
<span class="fc" id="L186">        User seller = order.getSeller();</span>

        // 创建包含卖家邮箱的通知内容
        try {
<span class="fc" id="L190">            ObjectNode contentNode = objectMapper.createObjectNode();</span>
<span class="fc" id="L191">            contentNode.put(&quot;message&quot;,</span>
<span class="fc" id="L192">                    &quot;卖家 &quot; + seller.getUsername() + &quot; 已确认您对 \&quot;&quot; + order.getItem().getName() + &quot;\&quot; 的订单，邮箱为 \&quot;&quot; + seller.getEmail() + &quot;\&quot;&quot;);</span>
<span class="fc" id="L193">            contentNode.put(&quot;sellerEmail&quot;, seller.getEmail()); // 添加卖家邮箱</span>
<span class="fc" id="L194">            contentNode.put(&quot;sellerUsername&quot;, seller.getUsername()); // 添加卖家用户名</span>
<span class="fc" id="L195">            contentNode.put(&quot;itemName&quot;, order.getItem().getName()); // 添加商品名称</span>
<span class="fc" id="L196">            contentNode.put(&quot;timestamp&quot;, System.currentTimeMillis());</span>

<span class="fc" id="L198">            String jsonContent = objectMapper.writeValueAsString(contentNode);</span>
<span class="fc" id="L199">            Notification notification = new Notification(buyer, seller, NotificationType.IDLE_ORDER_CONFIRMED,</span>
<span class="fc" id="L200">                    order.getId(), jsonContent);</span>
<span class="fc" id="L201">            Notification savedNotification = notificationRepository.save(notification);</span>

            // 发送实时通知
            try {
<span class="fc" id="L205">                notificationEventService.sendNotificationEvent(savedNotification);</span>
<span class="nc" id="L206">            } catch (Exception e) {</span>
<span class="nc" id="L207">                logger.error(&quot;Failed to send notification event&quot;, e);</span>
<span class="fc" id="L208">            }</span>
<span class="fc" id="L209">        } catch (Exception e) {</span>
<span class="fc" id="L210">            logger.error(&quot;创建订单确认通知失败&quot;, e);</span>
            // 如果JSON处理失败，使用简单文本作为后备方案
<span class="fc" id="L212">            String content = &quot;卖家 &quot; + seller.getUsername() + &quot; 已确认您对 \&quot;&quot; + order.getItem().getName() + &quot;\&quot; 的订单。卖家邮箱: &quot;</span>
<span class="fc" id="L213">                    + seller.getEmail();</span>
<span class="nc" id="L214">            createNotification(buyer, seller, NotificationType.IDLE_ORDER_CONFIRMED, order.getId(), content);</span>
<span class="fc" id="L215">        }</span>
<span class="fc" id="L216">    }</span>

    @Override
    public void createOrderCancelledNotification(Order order, User canceller) {
        User recipient;
        String content;

        // 确定通知接收者和内容
<span class="fc bfc" id="L224" title="All 2 branches covered.">        if (canceller.getId().equals(order.getBuyer().getId())) {</span>
            // 买家取消订单，通知卖家
<span class="fc" id="L226">            recipient = order.getSeller();</span>
<span class="fc" id="L227">            content = &quot;买家 &quot; + canceller.getUsername() + &quot; 取消了对 \&quot;&quot; + order.getItem().getName() + &quot;\&quot; 的订单&quot;;</span>
        } else {
            // 卖家取消订单，通知买家
<span class="fc" id="L230">            recipient = order.getBuyer();</span>
<span class="fc" id="L231">            content = &quot;卖家 &quot; + canceller.getUsername() + &quot; 取消了您对 \&quot;&quot; + order.getItem().getName() + &quot;\&quot; 的订单&quot;;</span>
        }

<span class="fc" id="L234">        createNotification(recipient, canceller, NotificationType.IDLE_ORDER_CANCELLED, order.getId(), content);</span>
<span class="fc" id="L235">    }</span>

    @Override
    public void createOrderCompletedNotification(Order order) {
<span class="fc" id="L239">        User seller = order.getSeller();</span>
<span class="fc" id="L240">        String content = &quot;买家 &quot; + order.getBuyer().getUsername() + &quot; 已确认完成 \&quot;&quot; + order.getItem().getName() + &quot;\&quot; 的交易&quot;;</span>
<span class="fc" id="L241">        createNotification(seller, order.getBuyer(), NotificationType.IDLE_ORDER_COMPLETED, order.getId(), content);</span>
<span class="fc" id="L242">    }</span>

    @Override
    public void createContactConfirmedNotification(Order order) {
<span class="fc" id="L246">        User seller = order.getSeller();</span>
<span class="fc" id="L247">        String content = &quot;买家 &quot; + order.getBuyer().getUsername() + &quot; 已确认联系，请尽快安排交易&quot;;</span>
<span class="fc" id="L248">        createNotification(seller, order.getBuyer(), NotificationType.IDLE_BUYER_ACKNOWLEDGED, order.getId(), content);</span>
<span class="fc" id="L249">    }</span>

    @Override
    public void createNewOfferNotification(Offer offer) {
<span class="fc" id="L253">        User wantedItemOwner = offer.getWantedItem().getUser();</span>
<span class="fc" id="L254">        String content = &quot;用户 &quot; + offer.getOfferer().getUsername() + &quot; 对您的求购 \&quot;&quot; + offer.getWantedItem().getName()</span>
                + &quot;\&quot; 发起了报价&quot;;
<span class="fc" id="L256">        createNotification(wantedItemOwner, offer.getOfferer(), NotificationType.WANTED_NEW_OFFER, offer.getId(),</span>
                content);
<span class="fc" id="L258">    }</span>

    @Override
    public void createOfferAcceptedNotification(Offer offer) {
<span class="fc" id="L262">        User offerer = offer.getOfferer();</span>
<span class="fc" id="L263">        String content = &quot;用户 &quot; + offer.getWantedItem().getUser().getUsername() + &quot; 接受了您对 \&quot;&quot;</span>
<span class="fc" id="L264">                + offer.getWantedItem().getName() + &quot;\&quot; 的报价&quot;;</span>
<span class="fc" id="L265">        createNotification(offerer, offer.getWantedItem().getUser(), NotificationType.WANTED_OFFER_ACCEPTED,</span>
<span class="fc" id="L266">                offer.getId(), content);</span>
<span class="fc" id="L267">    }</span>

    @Override
    public void createOfferRejectedNotification(Offer offer) {
<span class="fc" id="L271">        User offerer = offer.getOfferer();</span>
<span class="fc" id="L272">        String content = &quot;用户 &quot; + offer.getWantedItem().getUser().getUsername() + &quot; 拒绝了您对 \&quot;&quot;</span>
<span class="fc" id="L273">                + offer.getWantedItem().getName() + &quot;\&quot; 的报价&quot;;</span>
<span class="fc" id="L274">        createNotification(offerer, offer.getWantedItem().getUser(), NotificationType.WANTED_OFFER_REJECTED,</span>
<span class="fc" id="L275">                offer.getId(), content);</span>
<span class="fc" id="L276">    }</span>

    @Override
    public void createOfferConfirmedNotification(Offer offer) {
<span class="fc" id="L280">        User wantedItemOwner = offer.getWantedItem().getUser();</span>
<span class="fc" id="L281">        String content = &quot;用户 &quot; + offer.getOfferer().getUsername() + &quot; 已确认您对 \&quot;&quot; + offer.getWantedItem().getName()</span>
                + &quot;\&quot; 的报价&quot;;
<span class="fc" id="L283">        createNotification(wantedItemOwner, offer.getOfferer(), NotificationType.WANTED_OFFER_CONFIRMED, offer.getId(),</span>
                content);
<span class="fc" id="L285">    }</span>

    // 辅助方法：将实体映射为DTO
    private NotificationDto mapToDto(Notification notification) {
<span class="fc" id="L289">        NotificationDto dto = new NotificationDto();</span>
<span class="fc" id="L290">        dto.setId(notification.getId());</span>
<span class="fc" id="L291">        dto.setType(notification.getType());</span>

        // 处理content字段，将JSON字符串转换为前端期望的格式
        try {
            // 解析存储的JSON内容
<span class="fc" id="L296">            JsonNode contentNode = objectMapper.readTree(notification.getContent());</span>

            // 提取message字段，如果存在的话
<span class="pc bpc" id="L299" title="1 of 2 branches missed.">            if (contentNode.has(&quot;message&quot;)) {</span>
<span class="fc" id="L300">                dto.setContent(contentNode.get(&quot;message&quot;).asText());</span>

                // 对于订单确认通知，提取卖家邮箱
<span class="fc bfc" id="L303" title="All 2 branches covered.">                if (notification.getType() == NotificationType.IDLE_ORDER_CONFIRMED) {</span>
                    // 提取卖家邮箱
<span class="pc bpc" id="L305" title="1 of 2 branches missed.">                    if (contentNode.has(&quot;sellerEmail&quot;)) {</span>
<span class="fc" id="L306">                        dto.setSellerEmail(contentNode.get(&quot;sellerEmail&quot;).asText());</span>
<span class="fc" id="L307">                        logger.info(&quot;为通知 #{} 提取到卖家邮箱: {}&quot;, notification.getId(), dto.getSellerEmail());</span>
                    }

                    // 提取卖家用户名
<span class="pc bpc" id="L311" title="1 of 2 branches missed.">                    if (contentNode.has(&quot;sellerUsername&quot;)) {</span>
<span class="fc" id="L312">                        dto.setSellerUsername(contentNode.get(&quot;sellerUsername&quot;).asText());</span>
                    }

                    // 提取商品名称
<span class="pc bpc" id="L316" title="1 of 2 branches missed.">                    if (contentNode.has(&quot;itemName&quot;)) {</span>
<span class="fc" id="L317">                        dto.setItemName(contentNode.get(&quot;itemName&quot;).asText());</span>
                    }
                }
            } else {
                // 兼容处理：如果不是我们期望的JSON格式，直接使用原始内容
<span class="nc" id="L322">                dto.setContent(notification.getContent());</span>
            }
<span class="fc" id="L324">        } catch (Exception e) {</span>
<span class="fc" id="L325">            logger.warn(&quot;解析通知内容失败: {}&quot;, notification.getContent(), e);</span>
            // 出错时直接使用原始内容
<span class="fc" id="L327">            dto.setContent(notification.getContent());</span>
<span class="fc" id="L328">        }</span>

        // 设置标题，根据通知类型生成对应的标题
<span class="fc" id="L331">        dto.setTitle(getNotificationTitle(notification.getType()));</span>

<span class="fc" id="L333">        dto.setIsRead(notification.getIsRead());</span>
<span class="fc" id="L334">        dto.setIs_read(notification.getIsRead()); // 同时设置前端期望的字段</span>
<span class="fc" id="L335">        dto.setCreatedAt(notification.getCreatedAt());</span>
<span class="fc" id="L336">        dto.setRelatedEntityId(notification.getRelatedEntityId());</span>

<span class="fc bfc" id="L338" title="All 2 branches covered.">        if (notification.getSender() != null) {</span>
<span class="fc" id="L339">            dto.setSenderId(notification.getSender().getId());</span>
<span class="fc" id="L340">            dto.setSenderUsername(notification.getSender().getUsername());</span>

            // 如果是订单确认通知且没有从JSON中提取到卖家邮箱，则直接使用发送者的邮箱
<span class="pc bpc" id="L343" title="2 of 4 branches missed.">            if (notification.getType() == NotificationType.IDLE_ORDER_CONFIRMED &amp;&amp; dto.getSellerEmail() == null) {</span>
<span class="nc" id="L344">                dto.setSellerEmail(notification.getSender().getEmail());</span>
<span class="nc" id="L345">                logger.info(&quot;为通知 #{} 使用发送者邮箱: {}&quot;, notification.getId(), dto.getSellerEmail());</span>
            }
        }

<span class="fc" id="L349">        return dto;</span>
    }

    // 辅助方法：根据通知类型获取标题
    private String getNotificationTitle(NotificationType type) {
<span class="pc bpc" id="L354" title="14 of 18 branches missed.">        switch (type) {</span>
            case IDLE_NEW_ORDER:
<span class="nc" id="L356">                return &quot;新订单&quot;;</span>
            case IDLE_ORDER_CONFIRMED:
<span class="fc" id="L358">                return &quot;订单已确认&quot;;</span>
            case IDLE_ORDER_REJECTED:
<span class="nc" id="L360">                return &quot;订单被拒绝&quot;;</span>
            case IDLE_BUYER_ACKNOWLEDGED:
<span class="nc" id="L362">                return &quot;买家已确认&quot;;</span>
            case IDLE_ORDER_CANCELLED:
<span class="nc" id="L364">                return &quot;订单已取消&quot;;</span>
            case IDLE_ORDER_COMPLETED:
<span class="nc" id="L366">                return &quot;交易已完成&quot;;</span>
            case IDLE_CONTACT_CONFIRMED:
<span class="nc" id="L368">                return &quot;联系已确认&quot;;</span>
            case WANTED_NEW_OFFER:
<span class="nc" id="L370">                return &quot;收到响应&quot;;</span>
            case WANTED_OFFER_ACCEPTED:
<span class="nc" id="L372">                return &quot;响应已接受&quot;;</span>
            case WANTED_OFFER_REJECTED:
<span class="nc" id="L374">                return &quot;响应已拒绝&quot;;</span>
            case WANTED_OFFER_CONFIRMED:
<span class="nc" id="L376">                return &quot;响应已确认&quot;;</span>
            case WANTED_OFFERER_CONFIRMED:
<span class="nc" id="L378">                return &quot;响应者已确认&quot;;</span>
            case TRANSACTION_COMPLETED:
<span class="nc" id="L380">                return &quot;交易已完成&quot;;</span>
            case NEW_COMMENT_ON_ITEM:
<span class="fc" id="L382">                return &quot;新评论&quot;;</span>
            case NEW_REPLY_TO_COMMENT:
<span class="nc" id="L384">                return &quot;新回复&quot;;</span>
            case NEW_RATING_RECEIVED:
<span class="fc" id="L386">                return &quot;收到评价&quot;;</span>
            case SYSTEM_WELCOME:
<span class="fc" id="L388">                return &quot;系统通知&quot;;</span>
            default:
<span class="nc" id="L390">                return &quot;通知&quot;;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>