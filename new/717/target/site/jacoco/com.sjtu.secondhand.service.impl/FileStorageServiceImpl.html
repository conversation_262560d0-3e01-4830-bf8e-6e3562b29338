<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileStorageServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">FileStorageServiceImpl</span></div><h1>FileStorageServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">38 of 153</td><td class="ctr2">75%</td><td class="bar">1 of 6</td><td class="ctr2">83%</td><td class="ctr1">1</td><td class="ctr2">9</td><td class="ctr1">10</td><td class="ctr2">37</td><td class="ctr1">0</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a5"><a href="FileStorageServiceImpl.java.html#L53" class="el_method">storeFile(MultipartFile)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="51" alt="51"/></td><td class="ctr2" id="c1">86%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="FileStorageServiceImpl.java.html#L78" class="el_method">loadFileAsResource(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="24" alt="24"/></td><td class="ctr2" id="c2">75%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="FileStorageServiceImpl.java.html#L93" class="el_method">deleteFile(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="9" alt="9"/></td><td class="ctr2" id="c5">52%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="FileStorageServiceImpl.java.html#L33" class="el_method">init()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="17" alt="17"/></td><td class="ctr2" id="c3">70%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="FileStorageServiceImpl.java.html#L43" class="el_method">initForTest(Path)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">61%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i3">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="FileStorageServiceImpl.java.html#L23" class="el_method">FileStorageServiceImpl()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>