<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">ItemServiceImpl.java</span></div><h1>ItemServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.Category;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.Item.ItemCondition;
import com.sjtu.secondhand.model.Item.ItemStatus;
import com.sjtu.secondhand.model.Item.ItemType;
import com.sjtu.secondhand.model.ItemImage;
import com.sjtu.secondhand.model.Order;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.CategoryRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.OrderRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

@Service
public class ItemServiceImpl implements ItemService {

    private final ItemRepository itemRepository;
    private final UserService userService;
    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final CategoryRepository categoryRepository;

    @Autowired
    public ItemServiceImpl(ItemRepository itemRepository, UserService userService, OrderRepository orderRepository,
<span class="fc" id="L48">            UserRepository userRepository, CategoryRepository categoryRepository) {</span>
<span class="fc" id="L49">        this.itemRepository = itemRepository;</span>
<span class="fc" id="L50">        this.userService = userService;</span>
<span class="fc" id="L51">        this.orderRepository = orderRepository;</span>
<span class="fc" id="L52">        this.userRepository = userRepository;</span>
<span class="fc" id="L53">        this.categoryRepository = categoryRepository;</span>
<span class="fc" id="L54">    }</span>

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, allEntries = true)
    public ItemResponse createItem(ItemRequest itemRequest) {
        try {
<span class="fc" id="L61">            System.out.println(&quot;开始创建商品，类型: &quot; + itemRequest.getItem_type());</span>
<span class="fc" id="L62">            System.out.println(&quot;商品名称: &quot; + itemRequest.getName());</span>
<span class="fc" id="L63">            System.out.println(&quot;商品分类ID: &quot; + itemRequest.getCategory_id());</span>
<span class="fc" id="L64">            System.out.println(&quot;商品状态: &quot; + itemRequest.getCondition());</span>
<span class="fc" id="L65">            System.out.println(&quot;商品价格: &quot; + itemRequest.getPrice());</span>
<span class="fc" id="L66">            System.out.println(&quot;商品最低价格: &quot; + itemRequest.getPrice_min());</span>
<span class="fc" id="L67">            System.out.println(&quot;商品最高价格: &quot; + itemRequest.getPrice_max());</span>

            // 获取当前用户
<span class="fc" id="L70">            User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L71">            System.out.println(&quot;当前用户: &quot; + currentUser.getUsername());</span>

            // 获取分类
            Category category;
            try {
<span class="fc" id="L76">                category = categoryRepository.findById(itemRequest.getCategory_id())</span>
<span class="fc" id="L77">                        .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>
<span class="fc" id="L78">                System.out.println(&quot;找到分类: &quot; + category.getName());</span>
<span class="fc" id="L79">            } catch (Exception e) {</span>
<span class="fc" id="L80">                System.err.println(&quot;查找分类失败: &quot; + e.getMessage());</span>
<span class="fc" id="L81">                e.printStackTrace();</span>
<span class="fc" id="L82">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;获取商品分类失败: &quot; + e.getMessage());</span>
<span class="fc" id="L83">            }</span>

<span class="fc" id="L85">            Item item = new Item();</span>
<span class="fc" id="L86">            item.setUser(currentUser);</span>
<span class="fc" id="L87">            item.setCategory(category);</span>
<span class="fc" id="L88">            item.setName(itemRequest.getName());</span>
<span class="fc" id="L89">            item.setDescription(itemRequest.getDescription());</span>

            // 设置价格信息
            try {
<span class="fc" id="L93">                item.setPrice(itemRequest.getPrice());</span>
<span class="fc" id="L94">                item.setPriceMin(itemRequest.getPrice_min());</span>
<span class="fc" id="L95">                item.setPriceMax(itemRequest.getPrice_max());</span>
<span class="fc" id="L96">                System.out.println(&quot;设置价格成功&quot;);</span>
<span class="nc" id="L97">            } catch (Exception e) {</span>
<span class="nc" id="L98">                System.err.println(&quot;设置价格失败: &quot; + e.getMessage());</span>
<span class="nc" id="L99">                e.printStackTrace();</span>
<span class="nc" id="L100">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;价格格式无效: &quot; + e.getMessage());</span>
<span class="fc" id="L101">            }</span>

            // 转换condition字符串为ItemCondition枚举
            ItemCondition itemCondition;
            try {
<span class="fc" id="L106">                System.out.println(&quot;转换商品状态: &quot; + itemRequest.getCondition());</span>

                // 检查condition是否为null或空
<span class="fc bfc" id="L109" title="All 4 branches covered.">                if (itemRequest.getCondition() == null || itemRequest.getCondition().isEmpty()) {</span>
<span class="fc" id="L110">                    System.err.println(&quot;商品状态为空，使用默认值BRAND_NEW&quot;);</span>
<span class="fc" id="L111">                    itemCondition = ItemCondition.BRAND_NEW;</span>
                } else {
                    try {
<span class="fc" id="L114">                        itemCondition = ItemCondition.valueOf(itemRequest.getCondition());</span>
<span class="fc" id="L115">                        System.out.println(&quot;商品状态转换成功: &quot; + itemCondition);</span>
<span class="fc" id="L116">                    } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L117">                        System.err.println(&quot;无效的物品状况: &quot; + itemRequest.getCondition() + &quot;，使用默认值BRAND_NEW&quot;);</span>
<span class="fc" id="L118">                        itemCondition = ItemCondition.BRAND_NEW;</span>
<span class="fc" id="L119">                    }</span>
                }
<span class="nc" id="L121">            } catch (Exception e) {</span>
<span class="nc" id="L122">                System.err.println(&quot;处理商品状态时发生异常: &quot; + e.getMessage());</span>
<span class="nc" id="L123">                e.printStackTrace();</span>
<span class="nc" id="L124">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品状况: &quot; + e.getMessage());</span>
<span class="fc" id="L125">            }</span>
<span class="fc" id="L126">            item.setCondition(itemCondition);</span>

            // 转换item_type字符串为ItemType枚举
            ItemType itemType;
            try {
<span class="fc" id="L131">                System.out.println(&quot;转换物品类型: &quot; + itemRequest.getItem_type());</span>
<span class="fc bfc" id="L132" title="All 2 branches covered.">                if (&quot;IDLE&quot;.equals(itemRequest.getItem_type())) {</span>
<span class="fc" id="L133">                    itemType = ItemType.IDLE;</span>
<span class="pc bpc" id="L134" title="1 of 2 branches missed.">                } else if (&quot;WANTED&quot;.equals(itemRequest.getItem_type())) {</span>
<span class="nc" id="L135">                    itemType = ItemType.WANTED;</span>
                } else {
<span class="fc" id="L137">                    System.err.println(&quot;无效的物品类型: &quot; + itemRequest.getItem_type());</span>
<span class="fc" id="L138">                    throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品类型: &quot; + itemRequest.getItem_type() +</span>
                            &quot;，有效值为: IDLE, WANTED&quot;);
                }
<span class="fc" id="L141">                System.out.println(&quot;物品类型转换成功: &quot; + itemType);</span>
<span class="fc" id="L142">            } catch (ApiException e) {</span>
<span class="fc" id="L143">                throw e;</span>
<span class="nc" id="L144">            } catch (Exception e) {</span>
<span class="nc" id="L145">                System.err.println(&quot;物品类型转换失败: &quot; + e.getMessage());</span>
<span class="nc" id="L146">                e.printStackTrace();</span>
<span class="nc" id="L147">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;物品类型转换失败: &quot; + e.getMessage());</span>
<span class="fc" id="L148">            }</span>
<span class="fc" id="L149">            item.setItemType(itemType);</span>

            // 处理图片
<span class="pc bpc" id="L152" title="1 of 4 branches missed.">            if (itemRequest.getImage_urls() != null &amp;&amp; !itemRequest.getImage_urls().isEmpty()) {</span>
<span class="fc" id="L153">                List&lt;ItemImage&gt; imageEntities = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L154">                System.out.println(&quot;处理图片URL列表，共 &quot; + itemRequest.getImage_urls().size() + &quot; 张图片&quot;);</span>

<span class="fc bfc" id="L156" title="All 2 branches covered.">                for (String imageUrl : itemRequest.getImage_urls()) {</span>
<span class="fc bfc" id="L157" title="All 4 branches covered.">                    if (imageUrl == null || imageUrl.trim().isEmpty()) {</span>
<span class="fc" id="L158">                        System.out.println(&quot;跳过空URL&quot;);</span>
<span class="fc" id="L159">                        continue;</span>
                    }

<span class="fc" id="L162">                    System.out.println(&quot;处理图片URL: &quot; + imageUrl);</span>

                    // 确保URL格式正确
<span class="fc" id="L165">                    String processedUrl = imageUrl;</span>
<span class="fc bfc" id="L166" title="All 2 branches covered.">                    if (processedUrl.startsWith(&quot;/api/&quot;)) {</span>
<span class="fc" id="L167">                        processedUrl = processedUrl.substring(4);</span>
<span class="fc" id="L168">                        System.out.println(&quot;处理后的URL: &quot; + processedUrl);</span>
                    }

<span class="fc" id="L171">                    ItemImage image = new ItemImage();</span>
<span class="fc" id="L172">                    image.setItem(item);</span>
<span class="fc" id="L173">                    image.setUrl(processedUrl);</span>
<span class="fc" id="L174">                    imageEntities.add(image);</span>
<span class="fc" id="L175">                    System.out.println(&quot;添加图片实体: &quot; + processedUrl);</span>
<span class="fc" id="L176">                }</span>

<span class="fc" id="L178">                item.setImages(imageEntities);</span>
<span class="fc" id="L179">                System.out.println(&quot;添加了 &quot; + imageEntities.size() + &quot; 张图片&quot;);</span>
<span class="fc" id="L180">            } else {</span>
<span class="fc" id="L181">                System.out.println(&quot;没有添加图片&quot;);</span>
            }

            try {
<span class="fc" id="L185">                System.out.println(&quot;开始保存商品到数据库...&quot;);</span>
<span class="fc" id="L186">                Item savedItem = itemRepository.save(item);</span>
<span class="fc" id="L187">                System.out.println(&quot;商品保存成功，ID: &quot; + savedItem.getId());</span>
<span class="fc" id="L188">                return convertToItemResponse(savedItem);</span>
<span class="fc" id="L189">            } catch (Exception e) {</span>
<span class="fc" id="L190">                System.err.println(&quot;保存商品到数据库失败: &quot; + e.getMessage());</span>
<span class="fc" id="L191">                e.printStackTrace();</span>
<span class="fc" id="L192">                throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;保存商品失败: &quot; + e.getMessage());</span>
            }
<span class="fc" id="L194">        } catch (ApiException e) {</span>
<span class="fc" id="L195">            System.err.println(&quot;API异常: &quot; + e.getMessage());</span>
<span class="fc" id="L196">            e.printStackTrace();</span>
<span class="fc" id="L197">            throw e;</span>
<span class="nc" id="L198">        } catch (Exception e) {</span>
<span class="nc" id="L199">            System.err.println(&quot;创建商品失败: &quot; + e.getMessage());</span>
<span class="nc" id="L200">            e.printStackTrace();</span>
<span class="nc" id="L201">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;创建商品失败: &quot; + e.getMessage());</span>
        }
    }

    @Override
    @Transactional
    @Cacheable(value = &quot;items&quot;, key = &quot;#id&quot;, cacheManager = &quot;cacheManager&quot;, condition = &quot;#id != null&quot;)
    public ItemResponse getItemById(Long id) {
<span class="fc" id="L209">        System.out.println(&quot;【收藏量调试】开始获取物品详情，ID: &quot; + id);</span>
        
<span class="fc" id="L211">        Item item = itemRepository.findById(id)</span>
<span class="fc" id="L212">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>
        
<span class="fc" id="L214">        System.out.println(&quot;【收藏量调试】从数据库获取到物品，ID: &quot; + id + &quot;，当前收藏量: &quot; + item.getFavoriteCount());</span>

        // 增加浏览量
<span class="fc" id="L217">        item.setViewCount(item.getViewCount() + 1);</span>
<span class="fc" id="L218">        Item updatedItem = itemRepository.save(item);</span>
        
<span class="fc" id="L220">        System.out.println(&quot;【收藏量调试】更新浏览量后的物品，ID: &quot; + id + &quot;，收藏量仍为: &quot; + updatedItem.getFavoriteCount());</span>

        // 创建响应对象
<span class="fc" id="L223">        ItemResponse response = convertToItemResponse(updatedItem);</span>
        
<span class="fc" id="L225">        System.out.println(&quot;【收藏量调试】转换为响应对象后，ID: &quot; + id + &quot;，响应中的收藏量: &quot; + response.getFavoriteCount());</span>

<span class="fc" id="L227">        return response;</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; getAllItems(Pageable pageable) {
<span class="fc" id="L233">        return itemRepository.findAll(pageable);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; searchItems(String categoryName, String keyword, Pageable pageable) {
<span class="fc" id="L239">        Category category = null;</span>
<span class="pc bpc" id="L240" title="2 of 4 branches missed.">        if (categoryName != null &amp;&amp; !categoryName.isEmpty()) {</span>
<span class="fc" id="L241">            category = categoryRepository.findByName(categoryName)</span>
<span class="fc" id="L242">                    .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>
        }
<span class="fc" id="L244">        return itemRepository.searchItems(category, keyword, Item.ItemStatus.FOR_SALE, pageable);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; fullTextSearch(String keyword, Pageable pageable) {
<span class="fc" id="L250">        return itemRepository.fullTextSearch(keyword, Item.ItemStatus.FOR_SALE, pageable);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; advancedSearch(
            String keyword,
            String categoryName,
            Double minPrice,
            Double maxPrice,
            String conditionStr,
            Pageable pageable) {

<span class="fc" id="L263">        Category category = null;</span>
<span class="pc bpc" id="L264" title="1 of 4 branches missed.">        if (categoryName != null &amp;&amp; !categoryName.isEmpty()) {</span>
<span class="fc" id="L265">            category = categoryRepository.findByName(categoryName)</span>
<span class="fc" id="L266">                    .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>
        }

<span class="fc" id="L269">        ItemCondition condition = null;</span>
<span class="pc bpc" id="L270" title="2 of 4 branches missed.">        if (conditionStr != null &amp;&amp; !conditionStr.isEmpty()) {</span>
            try {
<span class="fc" id="L272">                condition = ItemCondition.valueOf(conditionStr);</span>
<span class="fc" id="L273">            } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L274">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品状况&quot;);</span>
<span class="fc" id="L275">            }</span>
        }

<span class="fc" id="L278">        return itemRepository.advancedSearch(keyword, category, minPrice, maxPrice, condition, Item.ItemStatus.FOR_SALE,</span>
                pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; getItemsByCategory(String categoryName, Pageable pageable) {
<span class="fc" id="L285">        Category category = categoryRepository.findByName(categoryName)</span>
<span class="fc" id="L286">                .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>
<span class="fc" id="L287">        return itemRepository.findByCategory(category, pageable);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; getItemsByUser(Long userId, Pageable pageable) {
<span class="fc" id="L293">        User user = userService.getUserById(userId);</span>
<span class="fc" id="L294">        return itemRepository.findByUser(user, pageable);</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; getMyItems(Pageable pageable) {
<span class="fc" id="L300">        User currentUser = userService.getCurrentUser();</span>
<span class="fc" id="L301">        return itemRepository.findByUser(currentUser, pageable);</span>
    }

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, key = &quot;#id&quot;)
    public ItemResponse updateItem(Long id, ItemRequest itemRequest) {
<span class="fc" id="L308">        User currentUser = userService.getCurrentUser();</span>

<span class="fc" id="L310">        Item item = itemRepository.findById(id)</span>
<span class="fc" id="L311">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>

<span class="fc bfc" id="L313" title="All 2 branches covered.">        if (!item.getUser().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L314">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您不是该物品的卖家，无法修改&quot;);</span>
        }

        // 更新基本信息
<span class="fc bfc" id="L318" title="All 2 branches covered.">        if (itemRequest.getName() != null) {</span>
<span class="fc" id="L319">            item.setName(itemRequest.getName());</span>
        }

<span class="fc bfc" id="L322" title="All 2 branches covered.">        if (itemRequest.getDescription() != null) {</span>
<span class="fc" id="L323">            item.setDescription(itemRequest.getDescription());</span>
        }

        // 更新分类
<span class="fc bfc" id="L327" title="All 2 branches covered.">        if (itemRequest.getCategory_id() != null) {</span>
<span class="fc" id="L328">            Category category = categoryRepository.findById(itemRequest.getCategory_id())</span>
<span class="pc" id="L329">                    .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>
<span class="fc" id="L330">            item.setCategory(category);</span>
        }

        // 更新价格信息
<span class="fc bfc" id="L334" title="All 2 branches covered.">        if (itemRequest.getPrice() != null) {</span>
<span class="fc" id="L335">            item.setPrice(itemRequest.getPrice());</span>
        }
<span class="fc bfc" id="L337" title="All 2 branches covered.">        if (itemRequest.getPrice_min() != null) {</span>
<span class="fc" id="L338">            item.setPriceMin(itemRequest.getPrice_min());</span>
        }
<span class="fc bfc" id="L340" title="All 2 branches covered.">        if (itemRequest.getPrice_max() != null) {</span>
<span class="fc" id="L341">            item.setPriceMax(itemRequest.getPrice_max());</span>
        }

        // 转换condition字符串为ItemCondition枚举
        ItemCondition itemCondition;
<span class="pc bpc" id="L346" title="1 of 4 branches missed.">        if (itemRequest.getCondition() != null &amp;&amp; !itemRequest.getCondition().trim().isEmpty()) {</span>
            try {
<span class="fc" id="L348">                itemCondition = ItemCondition.valueOf(itemRequest.getCondition());</span>
<span class="fc" id="L349">            } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L350">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品状况&quot;);</span>
<span class="fc" id="L351">            }</span>
<span class="fc" id="L352">            item.setCondition(itemCondition);</span>
        }

        // 转换item_type字符串为ItemType枚举
        ItemType itemType;
<span class="pc bpc" id="L357" title="1 of 4 branches missed.">        if (itemRequest.getItem_type() != null &amp;&amp; !itemRequest.getItem_type().trim().isEmpty()) {</span>
<span class="pc bpc" id="L358" title="1 of 2 branches missed.">            if (&quot;IDLE&quot;.equals(itemRequest.getItem_type())) {</span>
<span class="nc" id="L359">                itemType = ItemType.IDLE;</span>
<span class="fc bfc" id="L360" title="All 2 branches covered.">            } else if (&quot;WANTED&quot;.equals(itemRequest.getItem_type())) {</span>
<span class="fc" id="L361">                itemType = ItemType.WANTED;</span>
            } else {
<span class="fc" id="L363">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品类型&quot;);</span>
            }
<span class="fc" id="L365">            item.setItemType(itemType);</span>
        }

        // 处理图片
<span class="pc bpc" id="L369" title="1 of 4 branches missed.">        if (itemRequest.getImage_urls() != null &amp;&amp; !itemRequest.getImage_urls().isEmpty()) {</span>
            // 清除现有图片
<span class="fc" id="L371">            item.getImages().clear();</span>

            // 添加新图片
<span class="fc bfc" id="L374" title="All 2 branches covered.">            for (String imageUrl : itemRequest.getImage_urls()) {</span>
<span class="fc" id="L375">                ItemImage image = new ItemImage();</span>
<span class="fc" id="L376">                image.setItem(item);</span>
<span class="fc" id="L377">                image.setUrl(imageUrl);</span>
<span class="fc" id="L378">                item.getImages().add(image);</span>
<span class="fc" id="L379">            }</span>
        }

<span class="fc" id="L382">        Item updatedItem = itemRepository.save(item);</span>
<span class="fc" id="L383">        return convertToItemResponse(updatedItem);</span>
    }

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, key = &quot;#id&quot;)
    public void deleteItem(Long id) {
<span class="fc" id="L390">        User currentUser = userService.getCurrentUser();</span>

<span class="fc" id="L392">        Item item = itemRepository.findById(id)</span>
<span class="fc" id="L393">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>

<span class="fc bfc" id="L395" title="All 2 branches covered.">        if (!item.getUser().getId().equals(currentUser.getId())) {</span>
<span class="fc" id="L396">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您不是该物品的卖家，无法删除&quot;);</span>
        }

        try {
            // 先检查是否存在关联的订单
<span class="fc" id="L401">            List&lt;Order&gt; orders = orderRepository.findByItemId(id);</span>
<span class="fc bfc" id="L402" title="All 2 branches covered.">            if (!orders.isEmpty()) {</span>
<span class="fc" id="L403">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;该物品已有关联订单，无法删除&quot;);</span>
            }

            // 处理收藏关系 - 从所有用户的收藏列表中移除该物品
<span class="fc" id="L407">            List&lt;User&gt; allUsers = userRepository.findAll();</span>
<span class="fc bfc" id="L408" title="All 2 branches covered.">            for (User user : allUsers) {</span>
<span class="pc bpc" id="L409" title="1 of 4 branches missed.">                if (user.getFavoriteItems() != null &amp;&amp; user.getFavoriteItems().contains(item)) {</span>
<span class="fc" id="L410">                    user.getFavoriteItems().remove(item);</span>
<span class="fc" id="L411">                    userRepository.save(user);</span>
                }
<span class="fc" id="L413">            }</span>

            // 删除物品
<span class="fc" id="L416">            itemRepository.delete(item);</span>
<span class="fc" id="L417">        } catch (ApiException e) {</span>
<span class="fc" id="L418">            throw e;</span>
<span class="fc" id="L419">        } catch (Exception e) {</span>
<span class="fc" id="L420">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;删除物品失败: &quot; + e.getMessage());</span>
<span class="fc" id="L421">        }</span>
<span class="fc" id="L422">    }</span>

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, key = &quot;#id&quot;)
    public ItemResponse markItemAsSold(Long id) {
<span class="fc" id="L428">        User currentUser = userService.getCurrentUser();</span>

<span class="fc" id="L430">        Item item = itemRepository.findById(id)</span>
<span class="pc" id="L431">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>

<span class="pc bpc" id="L433" title="1 of 2 branches missed.">        if (!item.getUser().getId().equals(currentUser.getId())) {</span>
<span class="nc" id="L434">            throw new ApiException(HttpStatus.FORBIDDEN, &quot;您不是该物品的卖家，无法标记为已售出&quot;);</span>
        }

<span class="fc" id="L437">        item.setStatus(ItemStatus.SOLD);</span>
<span class="fc" id="L438">        Item updatedItem = itemRepository.save(item);</span>
<span class="fc" id="L439">        return convertToItemResponse(updatedItem);</span>
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = &quot;recommendedItems&quot;, key = &quot;#categoryId&quot;)
    public List&lt;ItemResponse&gt; getRecommendedItems(Long categoryId) {
        // 将Long类型的categoryId转换为Integer类型
<span class="fc" id="L447">        Integer categoryIdInt = categoryId.intValue();</span>
<span class="fc" id="L448">        Category category = categoryRepository.findById(categoryIdInt)</span>
<span class="fc" id="L449">                .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;分类不存在&quot;));</span>

<span class="fc" id="L451">        List&lt;Item&gt; items = itemRepository.findTop5ByCategoryAndStatusOrderByCreatedAtDesc(category,</span>
                ItemStatus.FOR_SALE);
<span class="fc" id="L453">        return items.stream()</span>
<span class="fc" id="L454">                .map(this::convertToItemResponse)</span>
<span class="fc" id="L455">                .collect(Collectors.toList());</span>
    }

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, key = &quot;#itemId&quot;) // 添加缓存清除注解
    public void addToFavorites(Long itemId, Long userId) {
<span class="fc" id="L462">        System.out.println(&quot;【收藏量调试】开始添加收藏，物品ID: &quot; + itemId + &quot;, 用户ID: &quot; + userId);</span>
        
<span class="fc" id="L464">        Item item = itemRepository.findById(itemId)</span>
<span class="pc" id="L465">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>
        
<span class="fc" id="L467">        System.out.println(&quot;【收藏量调试】添加收藏前，物品收藏量: &quot; + item.getFavoriteCount());</span>

<span class="fc" id="L469">        User user = userService.getUserById(userId);</span>

<span class="fc bfc" id="L471" title="All 2 branches covered.">        if (!user.getFavoriteItems().contains(item)) {</span>
<span class="fc" id="L472">            user.getFavoriteItems().add(item);</span>
<span class="fc" id="L473">            userService.saveUser(user);</span>

            // 更新物品的收藏计数
<span class="fc" id="L476">            item.setFavoriteCount(item.getFavoriteCount() + 1);</span>
<span class="fc" id="L477">            Item savedItem = itemRepository.save(item);</span>
            
<span class="fc" id="L479">            System.out.println(&quot;【收藏量调试】添加收藏后，物品收藏量更新为: &quot; + savedItem.getFavoriteCount());</span>
<span class="fc" id="L480">            System.out.println(&quot;【收藏量调试】清除缓存，键值: items::&quot; + itemId);</span>
<span class="fc" id="L481">        } else {</span>
<span class="fc" id="L482">            System.out.println(&quot;【收藏量调试】用户已经收藏过该物品，不做处理&quot;);</span>
        }
<span class="fc" id="L484">    }</span>

    @Override
    @Transactional
    @CacheEvict(value = &quot;items&quot;, key = &quot;#itemId&quot;) // 添加缓存清除注解
    public void removeFromFavorites(Long itemId, Long userId) {
<span class="fc" id="L490">        System.out.println(&quot;【收藏量调试】开始取消收藏，物品ID: &quot; + itemId + &quot;, 用户ID: &quot; + userId);</span>
        
<span class="fc" id="L492">        Item item = itemRepository.findById(itemId)</span>
<span class="pc" id="L493">                .orElseThrow(() -&gt; new ApiException(HttpStatus.NOT_FOUND, &quot;物品不存在&quot;));</span>
        
<span class="fc" id="L495">        System.out.println(&quot;【收藏量调试】取消收藏前，物品收藏量: &quot; + item.getFavoriteCount());</span>

<span class="fc" id="L497">        User user = userService.getUserById(userId);</span>

<span class="fc bfc" id="L499" title="All 2 branches covered.">        if (user.getFavoriteItems().contains(item)) {</span>
<span class="fc" id="L500">            user.getFavoriteItems().remove(item);</span>
<span class="fc" id="L501">            userService.saveUser(user);</span>

            // 更新物品的收藏计数
<span class="fc bfc" id="L504" title="All 2 branches covered.">            if (item.getFavoriteCount() &gt; 0) {</span>
<span class="fc" id="L505">                item.setFavoriteCount(item.getFavoriteCount() - 1);</span>
<span class="fc" id="L506">                Item savedItem = itemRepository.save(item);</span>
                
<span class="fc" id="L508">                System.out.println(&quot;【收藏量调试】取消收藏后，物品收藏量更新为: &quot; + savedItem.getFavoriteCount());</span>
<span class="fc" id="L509">            } else {</span>
<span class="fc" id="L510">                System.out.println(&quot;【收藏量调试】物品收藏量已为0，不做减法操作&quot;);</span>
            }
            
<span class="fc" id="L513">            System.out.println(&quot;【收藏量调试】清除缓存，键值: items::&quot; + itemId);</span>
        } else {
<span class="fc" id="L515">            System.out.println(&quot;【收藏量调试】用户未收藏该物品，不做处理&quot;);</span>
        }
<span class="fc" id="L517">    }</span>

    @Override
    @Transactional(readOnly = true)
    public List&lt;ItemResponse&gt; getFavoriteItems(Long userId) {
<span class="fc" id="L522">        User user = userService.getUserById(userId);</span>
<span class="fc" id="L523">        return user.getFavoriteItems().stream()</span>
<span class="fc" id="L524">                .map(this::convertToItemResponse)</span>
<span class="fc" id="L525">                .collect(Collectors.toList());</span>
    }

    @Override
    @Transactional(readOnly = true)
    public Page&lt;Item&gt; getFilteredItems(
            String itemTypeStr,
            String categoryId,
            Double priceMin,
            Double priceMax,
            String keyword,
            Long userId,
            Pageable pageable) {

<span class="fc" id="L539">        ItemType itemType = null;</span>
<span class="pc bpc" id="L540" title="1 of 4 branches missed.">        if (itemTypeStr != null &amp;&amp; !itemTypeStr.isEmpty()) {</span>
            try {
<span class="fc" id="L542">                itemType = ItemType.valueOf(itemTypeStr);</span>
<span class="fc" id="L543">            } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L544">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;无效的物品类型&quot;);</span>
<span class="fc" id="L545">            }</span>
        }

        // 打印接收到的categoryId参数的值和类型
<span class="fc" id="L549">        System.out.println(&quot;接收到的categoryId参数: &quot; + categoryId +</span>
<span class="pc bpc" id="L550" title="1 of 2 branches missed.">                &quot;, 类型: &quot; + (categoryId != null ? categoryId.getClass().getName() : &quot;null&quot;));</span>

        // 将String类型的categoryId转换为Integer类型
<span class="fc" id="L553">        Integer categoryIdInt = null;</span>
<span class="pc bpc" id="L554" title="2 of 4 branches missed.">        if (categoryId != null &amp;&amp; !categoryId.isEmpty()) {</span>
            try {
<span class="fc" id="L556">                categoryIdInt = Integer.parseInt(categoryId);</span>
<span class="fc" id="L557">                System.out.println(&quot;转换后的categoryIdInt: &quot; + categoryIdInt);</span>
<span class="fc" id="L558">            } catch (NumberFormatException e) {</span>
<span class="fc" id="L559">                System.err.println(&quot;转换categoryId时出错: &quot; + e.getMessage());</span>
<span class="fc" id="L560">                throw new ApiException(HttpStatus.BAD_REQUEST, &quot;分类ID格式无效&quot;);</span>
<span class="fc" id="L561">            }</span>
        }

        try {
            // 调用仓库层方法，根据条件筛选
<span class="fc" id="L566">            return itemRepository.findItemsWithFilters(</span>
                    itemType, categoryIdInt, priceMin, priceMax, keyword, userId, pageable);
<span class="fc" id="L568">        } catch (Exception e) {</span>
<span class="fc" id="L569">            System.err.println(&quot;获取物品列表失败，异常详情: &quot; + e.getMessage());</span>
<span class="fc" id="L570">            e.printStackTrace(); // 打印完整堆栈信息</span>
<span class="fc" id="L571">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;获取物品列表失败: &quot; + e.getMessage());</span>
        }
    }

    // 辅助方法：将Item实体转换为ItemResponse
    private ItemResponse convertToItemResponse(Item item) {
<span class="fc" id="L577">        System.out.println(&quot;【收藏量调试】开始转换物品实体为响应对象，物品ID: &quot; + item.getId());</span>
<span class="fc" id="L578">        System.out.println(&quot;【收藏量调试】物品实体中的收藏量: &quot; + item.getFavoriteCount());</span>
        
<span class="fc" id="L580">        ItemResponse response = new ItemResponse();</span>
<span class="fc" id="L581">        response.setId(item.getId());</span>
<span class="fc" id="L582">        response.setName(item.getName());</span>
        // 为了兼容性，同时设置title字段
<span class="fc" id="L584">        response.setTitle(item.getName());</span>
<span class="fc" id="L585">        response.setDescription(item.getDescription());</span>
<span class="fc" id="L586">        response.setPrice(item.getPrice());</span>
<span class="fc" id="L587">        response.setPriceMin(item.getPriceMin());</span>
<span class="fc" id="L588">        response.setPriceMax(item.getPriceMax());</span>
<span class="fc" id="L589">        response.setStatus(item.getStatus());</span>
<span class="fc" id="L590">        response.setCondition(item.getCondition());</span>
<span class="fc" id="L591">        response.setItemType(item.getItemType());</span>
<span class="fc" id="L592">        response.setViewCount(item.getViewCount());</span>
<span class="fc" id="L593">        response.setFavoriteCount(item.getFavoriteCount());</span>
        
<span class="fc" id="L595">        System.out.println(&quot;【收藏量调试】设置响应对象的收藏量: &quot; + response.getFavoriteCount());</span>

        // 设置分类信息
<span class="pc bpc" id="L598" title="1 of 2 branches missed.">        if (item.getCategory() != null) {</span>
<span class="fc" id="L599">            Map&lt;String, Object&gt; categoryMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L600">            categoryMap.put(&quot;id&quot;, item.getCategory().getId());</span>
<span class="fc" id="L601">            categoryMap.put(&quot;name&quot;, item.getCategory().getName());</span>
<span class="fc" id="L602">            response.setCategory(categoryMap);</span>
        }

        // 设置卖家信息
<span class="pc bpc" id="L606" title="1 of 2 branches missed.">        if (item.getUser() != null) {</span>
<span class="fc" id="L607">            Map&lt;String, Object&gt; userMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L608">            userMap.put(&quot;id&quot;, item.getUser().getId());</span>
<span class="fc" id="L609">            userMap.put(&quot;username&quot;, item.getUser().getUsername());</span>
<span class="fc" id="L610">            userMap.put(&quot;avatar&quot;, item.getUser().getAvatarUrl());</span>
<span class="fc" id="L611">            userMap.put(&quot;rating&quot;, item.getUser().getRating());</span>
<span class="fc" id="L612">            response.setUser(userMap);</span>

            // 为了兼容性，同时设置seller字段
<span class="fc" id="L615">            Map&lt;String, Object&gt; sellerMap = new HashMap&lt;&gt;();</span>
<span class="fc" id="L616">            sellerMap.put(&quot;id&quot;, item.getUser().getId());</span>
<span class="fc" id="L617">            sellerMap.put(&quot;username&quot;, item.getUser().getUsername());</span>
<span class="fc" id="L618">            sellerMap.put(&quot;avatar&quot;, item.getUser().getAvatarUrl()); // 确保包含avatar属性</span>
<span class="fc" id="L619">            sellerMap.put(&quot;rating&quot;, item.getUser().getRating());</span>
<span class="fc" id="L620">            response.setSeller(sellerMap);</span>
        }

        // 设置图片信息
<span class="pc bpc" id="L624" title="1 of 4 branches missed.">        if (item.getImages() != null &amp;&amp; !item.getImages().isEmpty()) {</span>
<span class="fc" id="L625">            List&lt;String&gt; imageUrls = item.getImages().stream()</span>
<span class="fc" id="L626">                    .map(ItemImage::getUrl)</span>
<span class="fc" id="L627">                    .collect(Collectors.toList());</span>
<span class="fc" id="L628">            response.setImages(imageUrls);</span>
        }
        
        // 获取当前用户（如果已登录）
        try {
<span class="fc" id="L633">            User currentUser = userService.getCurrentUser();</span>
<span class="fc bfc" id="L634" title="All 2 branches covered.">            if (currentUser != null) {</span>
<span class="fc" id="L635">                boolean isFavorited = currentUser.getFavoriteItems().contains(item);</span>
<span class="fc" id="L636">                response.setIsFavorited(isFavorited);</span>
<span class="fc" id="L637">                System.out.println(&quot;【收藏量调试】当前用户是否已收藏该物品: &quot; + isFavorited);</span>
<span class="fc" id="L638">            } else {</span>
                // 用户未登录，设置为false
<span class="fc" id="L640">                response.setIsFavorited(false);</span>
            }
<span class="nc" id="L642">        } catch (Exception e) {</span>
            // 用户未登录，忽略异常，设置为false
<span class="nc" id="L644">            response.setIsFavorited(false);</span>
<span class="nc" id="L645">            System.out.println(&quot;【收藏量调试】获取当前用户失败，可能未登录: &quot; + e.getMessage());</span>
<span class="fc" id="L646">        }</span>
        
<span class="fc" id="L648">        System.out.println(&quot;【收藏量调试】转换完成，最终响应对象的收藏量: &quot; + response.getFavoriteCount());</span>

<span class="fc" id="L650">        return response;</span>
    }

    /**
     * 批量处理物品列表，为每个物品设置收藏状态
     * @param items 需要处理的物品列表
     * @return 包含收藏状态的ItemResponse列表
     */
    @Override
    public List&lt;ItemResponse&gt; convertItemsWithFavoriteStatus(List&lt;Item&gt; items) {
        // 尝试获取当前登录用户
<span class="fc" id="L661">        User currentUser = null;</span>
<span class="fc" id="L662">        Set&lt;Long&gt; favoriteItemIds = new HashSet&lt;&gt;();</span>
        
        try {
            // 获取当前用户并提取收藏的物品ID
<span class="fc" id="L666">            final User user = userService.getCurrentUser();</span>
<span class="pc bpc" id="L667" title="1 of 2 branches missed.">            if (user != null) {</span>
<span class="fc" id="L668">                favoriteItemIds.addAll(</span>
<span class="fc" id="L669">                    user.getFavoriteItems().stream()</span>
<span class="fc" id="L670">                        .map(Item::getId)</span>
<span class="fc" id="L671">                        .collect(Collectors.toSet())</span>
                );
<span class="fc" id="L673">                System.out.println(&quot;当前用户收藏的物品ID: &quot; + favoriteItemIds);</span>
            }
<span class="fc" id="L675">        } catch (Exception e) {</span>
<span class="fc" id="L676">            System.out.println(&quot;获取当前用户失败，可能未登录: &quot; + e.getMessage());</span>
<span class="fc" id="L677">        }</span>

        // 为了确保lambda中使用的变量是effectively final
<span class="fc" id="L680">        final Set&lt;Long&gt; finalFavoriteItemIds = favoriteItemIds;</span>
        
        // 为每个物品创建响应对象并设置收藏状态
<span class="fc" id="L683">        return items.stream().map(item -&gt; {</span>
<span class="fc" id="L684">            ItemResponse response = new ItemResponse(item);</span>
            
            // 设置收藏状态
<span class="fc" id="L687">            boolean isFavorited = finalFavoriteItemIds.contains(item.getId());</span>
<span class="fc" id="L688">            response.setIsFavorited(isFavorited);</span>
            
<span class="fc" id="L690">            return response;</span>
<span class="fc" id="L691">        }).collect(Collectors.toList());</span>
    }

    // 辅助方法：检查数据库中已有的商品状态
    private void logExistingItemConditions() {
        try {
<span class="nc" id="L697">            System.out.println(&quot;检查数据库中的商品状态枚举值...&quot;);</span>
<span class="nc" id="L698">            List&lt;Item&gt; items = itemRepository.findAll();</span>
<span class="nc" id="L699">            Set&lt;ItemCondition&gt; conditions = new HashSet&lt;&gt;();</span>

<span class="nc bnc" id="L701" title="All 2 branches missed.">            for (Item item : items) {</span>
<span class="nc bnc" id="L702" title="All 2 branches missed.">                if (item.getCondition() != null) {</span>
<span class="nc" id="L703">                    conditions.add(item.getCondition());</span>
                }
<span class="nc" id="L705">            }</span>

<span class="nc" id="L707">            System.out.println(&quot;数据库中存在的商品状态: &quot; + conditions);</span>
<span class="nc" id="L708">        } catch (Exception e) {</span>
<span class="nc" id="L709">            System.err.println(&quot;检查商品状态失败: &quot; + e.getMessage());</span>
<span class="nc" id="L710">        }</span>
<span class="nc" id="L711">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>