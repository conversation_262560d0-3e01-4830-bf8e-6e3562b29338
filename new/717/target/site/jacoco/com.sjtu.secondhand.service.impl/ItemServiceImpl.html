<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ItemServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">ItemServiceImpl</span></div><h1>ItemServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">154 of 1,549</td><td class="ctr2">90%</td><td class="bar">25 of 108</td><td class="ctr2">76%</td><td class="ctr1">28</td><td class="ctr2">88</td><td class="ctr1">34</td><td class="ctr2">361</td><td class="ctr1">5</td><td class="ctr2">34</td></tr></tfoot><tbody><tr><td id="a4"><a href="ItemServiceImpl.java.html#L61" class="el_method">createItem(ItemRequest)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="67" alt="67"/><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="326" alt="326"/></td><td class="ctr2" id="c28">82%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="18" alt="18"/></td><td class="ctr2" id="e2">90%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g1">11</td><td class="ctr1" id="h0">17</td><td class="ctr2" id="i0">101</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a29"><a href="ItemServiceImpl.java.html#L697" class="el_method">logExistingItemConditions()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="43" alt="43"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="ItemServiceImpl.java.html#L577" class="el_method">convertToItemResponse(Item)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="215" alt="215"/></td><td class="ctr2" id="c26">95%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="7" alt="7"/></td><td class="ctr2" id="e5">70%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i1">52</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a30"><a href="ItemServiceImpl.java.html#L428" class="el_method">markItemAsSold(Long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="32" alt="32"/></td><td class="ctr2" id="c27">84%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e8">50%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a25"><a href="ItemServiceImpl.java.html#L493" class="el_method">lambda$removeFromFavorites$0()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a16"><a href="ItemServiceImpl.java.html#L465" class="el_method">lambda$addToFavorites$0()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a24"><a href="ItemServiceImpl.java.html#L431" class="el_method">lambda$markItemAsSold$0()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a28"><a href="ItemServiceImpl.java.html#L329" class="el_method">lambda$updateItem$1()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a33"><a href="ItemServiceImpl.java.html#L308" class="el_method">updateItem(Long, ItemRequest)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="175" alt="175"/></td><td class="ctr2" id="c25">98%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="28" alt="28"/></td><td class="ctr2" id="e4">87%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i2">42</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="ItemServiceImpl.java.html#L539" class="el_method">getFilteredItems(String, String, Double, Double, String, Long, Pageable)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="83" alt="83"/></td><td class="ctr2" id="c24">98%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">60%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i3">21</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a5"><a href="ItemServiceImpl.java.html#L390" class="el_method">deleteItem(Long)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="90" alt="90"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="9" alt="9"/></td><td class="ctr2" id="e3">90%</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i4">21</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a31"><a href="ItemServiceImpl.java.html#L490" class="el_method">removeFromFavorites(Long, Long)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="74" alt="74"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d10"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i5">17</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="ItemServiceImpl.java.html#L462" class="el_method">addToFavorites(Long, Long)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="66" alt="66"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d11"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i6">15</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a10"><a href="ItemServiceImpl.java.html#L209" class="el_method">getItemById(Long)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="50" alt="50"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i10">10</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a2"><a href="ItemServiceImpl.java.html#L661" class="el_method">convertItemsWithFavoriteStatus(List)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="46" alt="46"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e9">50%</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i7">15</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a1"><a href="ItemServiceImpl.java.html#L263" class="el_method">advancedSearch(String, String, Double, Double, String, Pageable)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="5" alt="5"/></td><td class="ctr2" id="e6">62%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i9">11</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a14"><a href="ItemServiceImpl.java.html#L447" class="el_method">getRecommendedItems(Long)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="27" alt="27"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a32"><a href="ItemServiceImpl.java.html#L239" class="el_method">searchItems(String, String, Pageable)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="23" alt="23"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">50%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i14">5</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a15"><a href="ItemServiceImpl.java.html#L48" class="el_method">ItemServiceImpl(ItemRepository, UserService, OrderRepository, UserRepository, CategoryRepository)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a18"><a href="ItemServiceImpl.java.html#L684" class="el_method">lambda$convertItemsWithFavoriteStatus$0(Set, Item)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="16" alt="16"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i15">4</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a8"><a href="ItemServiceImpl.java.html#L522" class="el_method">getFavoriteItems(Long)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="15" alt="15"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i16">4</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a11"><a href="ItemServiceImpl.java.html#L285" class="el_method">getItemsByCategory(String, Pageable)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a12"><a href="ItemServiceImpl.java.html#L293" class="el_method">getItemsByUser(Long, Pageable)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a13"><a href="ItemServiceImpl.java.html#L300" class="el_method">getMyItems(Pageable)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a6"><a href="ItemServiceImpl.java.html#L250" class="el_method">fullTextSearch(String, Pageable)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="7" alt="7"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a23"><a href="ItemServiceImpl.java.html#L449" class="el_method">lambda$getRecommendedItems$0()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a20"><a href="ItemServiceImpl.java.html#L393" class="el_method">lambda$deleteItem$0()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a27"><a href="ItemServiceImpl.java.html#L311" class="el_method">lambda$updateItem$0()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a22"><a href="ItemServiceImpl.java.html#L286" class="el_method">lambda$getItemsByCategory$0()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a17"><a href="ItemServiceImpl.java.html#L266" class="el_method">lambda$advancedSearch$0()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">0</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a26"><a href="ItemServiceImpl.java.html#L242" class="el_method">lambda$searchItems$0()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">0</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a21"><a href="ItemServiceImpl.java.html#L212" class="el_method">lambda$getItemById$0()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">0</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a19"><a href="ItemServiceImpl.java.html#L77" class="el_method">lambda$createItem$0()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">0</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a7"><a href="ItemServiceImpl.java.html#L233" class="el_method">getAllItems(Pageable)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">0</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k33">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>