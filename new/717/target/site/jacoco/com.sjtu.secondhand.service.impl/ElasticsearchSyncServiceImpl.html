<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchSyncServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">ElasticsearchSyncServiceImpl</span></div><h1>ElasticsearchSyncServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">223 of 2,442</td><td class="ctr2">90%</td><td class="bar">49 of 160</td><td class="ctr2">69%</td><td class="ctr1">46</td><td class="ctr2">94</td><td class="ctr1">41</td><td class="ctr2">494</td><td class="ctr1">1</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a13"><a href="ElasticsearchSyncServiceImpl.java.html#L586" class="el_method">transformElasticsearchResponse(Map, int, int)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="73" alt="73"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="554" alt="554"/></td><td class="ctr2" id="c10">88%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="52" alt="52"/></td><td class="ctr2" id="e4">65%</td><td class="ctr1" id="f0">24</td><td class="ctr2" id="g0">41</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i1">123</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="ElasticsearchSyncServiceImpl.java.html#L163" class="el_method">searchItemsByKeyword(String, int, int)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="62" alt="62"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="63" alt="63"/></td><td class="ctr2" id="c12">50%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i6">20</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="ElasticsearchSyncServiceImpl.java.html#L200" class="el_method">advancedSearch(String, Long, Double, Double, String, String, int, int, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="50" alt="50"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="56" alt="56"/></td><td class="ctr2" id="c11">52%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i5">22</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="ElasticsearchSyncServiceImpl.java.html#L822" class="el_method">convertItemToMap(Item)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="180" alt="180"/></td><td class="ctr2" id="c9">91%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="7" alt="7"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i3">33</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="ElasticsearchSyncServiceImpl.java.html#L109" class="el_method">syncAllItemsToElasticsearch()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="115" alt="115"/></td><td class="ctr2" id="c8">94%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">33</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="ElasticsearchSyncServiceImpl.java.html#L371" class="el_method">directAdvancedSearch(String, Long, Double, Double, String, String, int, int, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="119" height="10" title="640" alt="640"/></td><td class="ctr2" id="c5">99%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="33" alt="33"/></td><td class="ctr2" id="e2">78%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">22</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i0">135</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="ElasticsearchSyncServiceImpl.java.html#L240" class="el_method">directSearchByKeyword(String, int, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="71" height="10" title="382" alt="382"/></td><td class="ctr2" id="c6">98%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">80%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i2">78</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="ElasticsearchSyncServiceImpl.java.html#L860" class="el_method">lambda$convertItemToMap$0(ItemImage)</a></td><td class="bar" id="b7"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="ElasticsearchSyncServiceImpl.java.html#L79" class="el_method">syncItemToElasticsearch(Item)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="49" alt="49"/></td><td class="ctr2" id="c7">98%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="ElasticsearchSyncServiceImpl.java.html#L797" class="el_method">convertToMap(ItemDocument)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="114" alt="114"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i7">20</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="ElasticsearchSyncServiceImpl.java.html#L98" class="el_method">deleteItemFromElasticsearch(Long)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="33" alt="33"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="ElasticsearchSyncServiceImpl.java.html#L61" class="el_method">ElasticsearchSyncServiceImpl(ItemRepository, ItemDocumentRepository, CategoryRepository, ElasticsearchOperations)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="23" alt="23"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="ElasticsearchSyncServiceImpl.java.html#L72" class="el_method">init()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a10"><a href="ElasticsearchSyncServiceImpl.java.html#L45" class="el_method">static {...}</a></td><td class="bar" id="b13"/><td class="ctr2" id="c4">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>