<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CategoryServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_class">CategoryServiceImpl</span></div><h1>CategoryServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">10 of 144</td><td class="ctr2">93%</td><td class="bar">1 of 8</td><td class="ctr2">87%</td><td class="ctr1">3</td><td class="ctr2">14</td><td class="ctr1">0</td><td class="ctr2">33</td><td class="ctr1">2</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a6"><a href="CategoryServiceImpl.java.html#L84" class="el_method">lambda$deleteCategory$0()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="CategoryServiceImpl.java.html#L73" class="el_method">lambda$updateCategory$0()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="CategoryServiceImpl.java.html#L31" class="el_method">getCategoryTree()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="58" alt="58"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">87%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="CategoryServiceImpl.java.html#L71" class="el_method">updateCategory(Long, Category)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="26" alt="26"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="CategoryServiceImpl.java.html#L82" class="el_method">deleteCategory(Long)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="17" alt="17"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="CategoryServiceImpl.java.html#L58" class="el_method">getCategoryById(Long)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="12" alt="12"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="CategoryServiceImpl.java.html#L20" class="el_method">CategoryServiceImpl(CategoryRepository)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="CategoryServiceImpl.java.html#L65" class="el_method">createCategory(Category)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="6" alt="6"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="CategoryServiceImpl.java.html#L60" class="el_method">lambda$getCategoryById$0()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="CategoryServiceImpl.java.html#L26" class="el_method">getAllCategories()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>