<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">AuthServiceImpl.java</span></div><h1>AuthServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.sjtu.secondhand.dto.request.LoginRequest;
import com.sjtu.secondhand.dto.request.RegisterRequest;
import com.sjtu.secondhand.dto.response.JwtAuthResponse;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.security.JwtTokenProvider;
import com.sjtu.secondhand.service.AuthService;
import com.sjtu.secondhand.util.DefaultAvatarUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthServiceImpl implements AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider jwtTokenProvider;

    @Autowired
    public AuthServiceImpl(
            UserRepository userRepository,
            PasswordEncoder passwordEncoder,
            AuthenticationManager authenticationManager,
<span class="fc" id="L34">            JwtTokenProvider jwtTokenProvider) {</span>
<span class="fc" id="L35">        this.userRepository = userRepository;</span>
<span class="fc" id="L36">        this.passwordEncoder = passwordEncoder;</span>
<span class="fc" id="L37">        this.authenticationManager = authenticationManager;</span>
<span class="fc" id="L38">        this.jwtTokenProvider = jwtTokenProvider;</span>
<span class="fc" id="L39">    }</span>

    @Override
    public JwtAuthResponse login(LoginRequest loginRequest) {
        // 尝试认证用户
<span class="fc" id="L44">        Authentication authentication = authenticationManager.authenticate(</span>
                new UsernamePasswordAuthenticationToken(
<span class="fc" id="L46">                        loginRequest.getUsername(),</span>
<span class="fc" id="L47">                        loginRequest.getPassword()));</span>

        // 设置认证信息到上下文
<span class="fc" id="L50">        SecurityContextHolder.getContext().setAuthentication(authentication);</span>

        // 获取用户信息
<span class="fc" id="L53">        User user = userRepository.findByUsername(loginRequest.getUsername())</span>
<span class="pc" id="L54">                .orElseThrow(() -&gt; new ApiException(HttpStatus.BAD_REQUEST, &quot;用户不存在&quot;));</span>

        // 生成JWT令牌
<span class="fc" id="L57">        String token = jwtTokenProvider.createToken(user.getUsername(), user.getId());</span>

        // 创建响应对象
<span class="fc" id="L60">        JwtAuthResponse response = new JwtAuthResponse();</span>
<span class="fc" id="L61">        response.setToken(token);</span>
<span class="fc" id="L62">        response.setTokenType(&quot;Bearer&quot;);</span>
<span class="fc" id="L63">        response.setId(user.getId());</span>
<span class="fc" id="L64">        response.setUsername(user.getUsername());</span>
<span class="fc" id="L65">        response.setEmail(user.getEmail());</span>
<span class="fc" id="L66">        response.setAvatar(user.getAvatarUrl());</span>

<span class="fc" id="L68">        return response;</span>
    }

    @Override
    public String register(RegisterRequest registerRequest) {
        // 检查用户名是否已存在
<span class="fc bfc" id="L74" title="All 2 branches covered.">        if (userRepository.existsByUsername(registerRequest.getUsername())) {</span>
<span class="fc" id="L75">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;用户名已被占用&quot;);</span>
        }

        // 检查邮箱是否已存在
<span class="fc bfc" id="L79" title="All 2 branches covered.">        if (userRepository.existsByEmail(registerRequest.getEmail())) {</span>
<span class="fc" id="L80">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;邮箱已被注册&quot;);</span>
        }

        // 验证邮箱后缀是否为sjtu.edu.cn
<span class="fc bfc" id="L84" title="All 2 branches covered.">        if (!registerRequest.getEmail().endsWith(&quot;@sjtu.edu.cn&quot;)) {</span>
<span class="fc" id="L85">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;请使用上海交通大学邮箱注册&quot;);</span>
        }

        // 创建新用户对象
<span class="fc" id="L89">        User user = new User();</span>
<span class="fc" id="L90">        user.setUsername(registerRequest.getUsername());</span>
<span class="fc" id="L91">        user.setEmail(registerRequest.getEmail());</span>
<span class="fc" id="L92">        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));</span>
        
        // 为用户分配默认头像
        // 使用基于用户名的头像分配，确保同一用户名总是获得相同头像
<span class="fc" id="L96">        String defaultAvatarUrl = DefaultAvatarUtil.getDefaultAvatarUrlByUsername(registerRequest.getUsername());</span>
<span class="fc" id="L97">        user.setAvatarUrl(defaultAvatarUrl);</span>

        // 保存用户
<span class="fc" id="L100">        userRepository.save(user);</span>

<span class="fc" id="L102">        return &quot;用户注册成功&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>