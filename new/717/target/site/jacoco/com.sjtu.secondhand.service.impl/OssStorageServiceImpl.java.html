<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OssStorageServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.source.html" class="el_package">com.sjtu.secondhand.service.impl</a> &gt; <span class="el_source">OssStorageServiceImpl.java</span></div><h1>OssStorageServiceImpl.java</h1><pre class="source lang-java linenums">package com.sjtu.secondhand.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PolicyConditions;
import com.sjtu.secondhand.exception.ApiException;
import com.sjtu.secondhand.service.OssStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class OssStorageServiceImpl implements OssStorageService {

<span class="fc" id="L28">    private static final Logger logger = LoggerFactory.getLogger(OssStorageServiceImpl.class);</span>

    private final OSS ossClient;

    @Value(&quot;${aliyun.oss.bucket-name:shareu-youyu}&quot;)
    private String bucketName;

    @Value(&quot;${aliyun.oss.bucket-domain:shareu-youyu.oss-cn-shanghai.aliyuncs.com}&quot;)
    private String bucketDomain;
    
    @Value(&quot;${aliyun.oss.endpoint:oss-cn-shanghai.aliyuncs.com}&quot;)
    private String endpoint;
    
    @Value(&quot;${aliyun.oss.access-key-id:LTAI5tLKLwDpsP947BPBLsnX}&quot;)
    private String accessKeyId;

    // 图片上传文件夹路径
    private static final String IMAGE_FOLDER = &quot;item_images/&quot;;
    
    // OSS URL有效期设置为3600秒（1小时）
    private static final long URL_EXPIRATION_TIME = 3600L;

<span class="fc" id="L50">    public OssStorageServiceImpl(OSS ossClient) {</span>
<span class="fc" id="L51">        this.ossClient = ossClient;</span>
<span class="fc" id="L52">    }</span>

    /**
     * 生成唯一的文件名，使用时间戳+UUID的方式
     * 
     * @param fileExtension 文件扩展名，包含点号，如 &quot;.jpg&quot;
     * @return 唯一的文件名
     */
    private String generateUniqueObjectName(String fileExtension) {
        // 生成时间戳，格式为yyyyMMddHHmmss
<span class="fc" id="L62">        SimpleDateFormat dateFormat = new SimpleDateFormat(&quot;yyyyMMddHHmmss&quot;);</span>
<span class="fc" id="L63">        String timestamp = dateFormat.format(new Date());</span>
        
        // 生成UUID
<span class="fc" id="L66">        String uuid = UUID.randomUUID().toString();</span>
        
        // 返回时间戳+UUID+文件扩展名
<span class="fc" id="L69">        return timestamp + &quot;_&quot; + uuid + fileExtension;</span>
    }

    @Override
    public Map&lt;String, String&gt; generatePresignedUrl(String filename, String contentType) {
<span class="fc" id="L74">        String originalFilename = StringUtils.cleanPath(filename);</span>
<span class="fc bfc" id="L75" title="All 2 branches covered.">        if (originalFilename.contains(&quot;..&quot;)) {</span>
<span class="fc" id="L76">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;文件名包含无效路径序列 &quot; + originalFilename);</span>
        }

<span class="fc" id="L79">        String fileExtension = &quot;&quot;;</span>
<span class="fc bfc" id="L80" title="All 2 branches covered.">        if (originalFilename.contains(&quot;.&quot;)) {</span>
<span class="fc" id="L81">            fileExtension = originalFilename.substring(originalFilename.lastIndexOf(&quot;.&quot;));</span>
        }

        // 生成唯一对象名称，并添加到item_images文件夹
<span class="fc" id="L85">        String objectName = IMAGE_FOLDER + generateUniqueObjectName(fileExtension);</span>

        try {
<span class="fc" id="L88">            logger.info(&quot;生成OSS预签名URL，文件名: {}, 内容类型: {}, 对象名: {}&quot;, filename, contentType, objectName);</span>
            
            // 使用预签名URL方式
            // 设置请求参数
<span class="fc" id="L92">            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName);</span>
    
            // 设置HTTP方法为PUT（用于上传）
<span class="fc" id="L95">            request.setMethod(com.aliyun.oss.HttpMethod.PUT);</span>
    
            // 设置Content-Type
<span class="fc" id="L98">            request.setContentType(contentType);</span>
    
            // 设置URL过期时间（1小时）
<span class="fc" id="L101">            Date expiration = new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME * 1000);</span>
<span class="fc" id="L102">            request.setExpiration(expiration);</span>
    
            // 生成预签名URL
<span class="fc" id="L105">            URL url = ossClient.generatePresignedUrl(request);</span>
            
<span class="fc" id="L107">            logger.info(&quot;生成的预签名URL: {}&quot;, url.toString());</span>
    
            // 构建结果
<span class="fc" id="L110">            Map&lt;String, String&gt; result = new HashMap&lt;&gt;();</span>
<span class="fc" id="L111">            result.put(&quot;upload_url&quot;, url.toString());</span>
<span class="fc" id="L112">            result.put(&quot;access_url&quot;, &quot;https://&quot; + bucketDomain + &quot;/&quot; + objectName);</span>
<span class="fc" id="L113">            result.put(&quot;object_name&quot;, objectName);</span>
<span class="fc" id="L114">            result.put(&quot;method&quot;, &quot;PUT&quot;);</span>
<span class="fc" id="L115">            result.put(&quot;content_type&quot;, contentType);</span>
    
<span class="fc" id="L117">            return result;</span>
<span class="fc" id="L118">        } catch (Exception e) {</span>
<span class="fc" id="L119">            logger.error(&quot;生成预签名URL失败&quot;, e);</span>
<span class="fc" id="L120">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;生成预签名URL失败: &quot; + e.getMessage());</span>
        }
    }

    @Override
    public String uploadFile(MultipartFile file) {
<span class="fc" id="L126">        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());</span>
<span class="fc bfc" id="L127" title="All 2 branches covered.">        if (originalFilename.contains(&quot;..&quot;)) {</span>
<span class="fc" id="L128">            throw new ApiException(HttpStatus.BAD_REQUEST, &quot;文件名包含无效路径序列 &quot; + originalFilename);</span>
        }

<span class="fc" id="L131">        String fileExtension = &quot;&quot;;</span>
<span class="fc bfc" id="L132" title="All 2 branches covered.">        if (originalFilename.contains(&quot;.&quot;)) {</span>
<span class="fc" id="L133">            fileExtension = originalFilename.substring(originalFilename.lastIndexOf(&quot;.&quot;));</span>
        }

        // 生成唯一对象名称，并添加到item_images文件夹
<span class="fc" id="L137">        String objectName = IMAGE_FOLDER + generateUniqueObjectName(fileExtension);</span>

        try {
<span class="fc" id="L140">            logger.info(&quot;开始上传文件到OSS，文件名: {}, 大小: {} bytes&quot;, originalFilename, file.getSize());</span>
            
            // 设置元数据
<span class="fc" id="L143">            ObjectMetadata metadata = new ObjectMetadata();</span>
<span class="fc" id="L144">            metadata.setContentType(file.getContentType());</span>
<span class="fc" id="L145">            metadata.setContentLength(file.getSize());</span>

            // 上传文件到OSS
<span class="fc" id="L148">            ossClient.putObject(bucketName, objectName, file.getInputStream(), metadata);</span>
            
<span class="fc" id="L150">            String fileUrl = &quot;https://&quot; + bucketDomain + &quot;/&quot; + objectName;</span>
<span class="fc" id="L151">            logger.info(&quot;文件上传成功，访问URL: {}&quot;, fileUrl);</span>

            // 返回访问URL
<span class="fc" id="L154">            return fileUrl;</span>
<span class="fc" id="L155">        } catch (IOException ex) {</span>
<span class="fc" id="L156">            logger.error(&quot;上传文件到OSS失败&quot;, ex);</span>
<span class="fc" id="L157">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;上传文件到OSS失败: &quot; + ex.getMessage());</span>
        }
    }

    @Override
    public boolean deleteFile(String objectName) {
        try {
<span class="fc" id="L164">            logger.info(&quot;尝试删除OSS文件: {}&quot;, objectName);</span>
            
            // 检查文件是否存在
<span class="fc" id="L167">            boolean exists = ossClient.doesObjectExist(bucketName, objectName);</span>
<span class="fc bfc" id="L168" title="All 2 branches covered.">            if (!exists) {</span>
<span class="fc" id="L169">                logger.warn(&quot;要删除的文件不存在: {}&quot;, objectName);</span>
<span class="fc" id="L170">                return false;</span>
            }

            // 删除文件
<span class="fc" id="L174">            ossClient.deleteObject(bucketName, objectName);</span>
<span class="fc" id="L175">            logger.info(&quot;文件删除成功: {}&quot;, objectName);</span>
<span class="fc" id="L176">            return true;</span>
<span class="fc" id="L177">        } catch (Exception ex) {</span>
<span class="fc" id="L178">            logger.error(&quot;从OSS删除文件失败: {}&quot;, objectName, ex);</span>
<span class="fc" id="L179">            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, &quot;从OSS删除文件失败: &quot; + ex.getMessage());</span>
        }
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>